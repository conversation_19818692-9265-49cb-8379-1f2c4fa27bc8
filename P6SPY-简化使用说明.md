# P6Spy SQL监控 - 简化使用说明

## 🎯 设计目标
- **零配置**：dev环境自动启用，无需任何配置
- **自动化**：代码自动处理驱动和URL转换
- **简单化**：开发者无感知，直接看到SQL日志

## 📋 使用方式

### 1. Dev环境（自动启用）
```bash
# 使用dev profile启动应用
mvn spring-boot:run -Pdev
# 或者
java -jar app.jar --spring.profiles.active=dev
```

**结果**：
- ✅ P6Spy自动启用
- ✅ 自动打印SQL语句和执行时间
- ✅ 无需任何配置
- ✅ 无需在Nacos中配置p6spy.enabled
- ✅ 无需手动配置数据库驱动和URL

### 2. Test环境（可选启用）
```yaml
# 在Nacos配置中心添加以下配置启用P6Spy
p6spy:
  enabled: true  # 默认false，需要时手动启用
```

**结果**：
- 🔧 Test环境默认禁用P6Spy
- ✅ 配置 `p6spy.enabled=true` 后启用
- ✅ 灵活控制，按需开启

### 3. 生产环境（强制禁用）
- ❌ P6Spy强制禁用，无法通过配置启用
- ❌ 不包含p6spy依赖
- ✅ 性能不受影响

## 🧠 启用逻辑

P6Spy的启用遵循以下简洁逻辑：

```java
if (UnifiedConfig.isProdEnv()) {
    ENABLED = false;  // 生产环境强制禁用
} else {
    // dev环境默认启用 || test环境通过配置启用
    ENABLED = UnifiedConfig.isDevEnv() || PropUtils.getBoolean(props, "p6spy", "enabled");
}
```

**逻辑说明**：
- 🚫 **生产环境**：强制禁用，无法通过配置覆盖
- ✅ **Dev环境**：自动启用，无需配置
- 🔧 **Test环境**：默认禁用，可通过 `p6spy.enabled=true` 启用

## 🔍 SQL日志格式
```
2025-07-04 10:30:15.123 | 25ms | statement | connection1 | UPDATE u_message_setting SET sign_remind = 'T' WHERE user_id = 12345
```

## 🚀 启动日志
应用启动时会看到：
```
🔍 P6Spy SQL监控已自动启用，将打印SQL语句和执行时间
```

## ⚙️ 技术实现
- **环境检测**：自动检测spring.profiles.active
- **依赖检查**：自动检查p6spy类是否存在
- **驱动转换**：自动将数据库驱动转换为P6SpyDriver
- **URL转换**：自动在JDBC URL前添加p6spy前缀
- **优雅降级**：p6spy不存在时自动使用原始驱动

## 📁 相关文件
- `P6spyConfig.java` - 配置管理
- `JdbcDataSource.java` - 数据源自动配置
- `spy.properties` - P6Spy配置文件
- `pom.xml` - Maven依赖配置

## 🎉 使用体验
1. **开发者**：启动应用即可看到SQL日志，无需任何配置
2. **测试人员**：可选择性启用SQL监控
3. **运维人员**：生产环境自动禁用，无性能影响

---
*P6Spy版本：3.9.1*  
*更新时间：2025-07-04*
