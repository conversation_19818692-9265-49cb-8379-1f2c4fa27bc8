// Test P6Spy URL conversion logic
public class TestP6SpyUrl {
    public static void main(String[] args) {
        // Test original URL
        String originalUrl = "*****************************************************************************************************************";

        System.out.println("Original URL: " + originalUrl);

        // Simulate P6Spy URL conversion logic
        String p6spyUrl;
        if (!originalUrl.startsWith("jdbc:p6spy:")) {
            p6spyUrl = "jdbc:p6spy:" + originalUrl;
        } else {
            p6spyUrl = originalUrl;
        }

        System.out.println("P6Spy URL: " + p6spyUrl);

        // Check for double prefix
        if (p6spyUrl.contains("jdbc:jdbc:")) {
            System.out.println("ERROR: Double prefix detected!");
        } else {
            System.out.println("OK: URL conversion is correct");
        }

        // Test URL that already has p6spy prefix
        String alreadyP6spyUrl = "*************************************************************";
        System.out.println("\nURL with existing P6Spy prefix: " + alreadyP6spyUrl);

        String result;
        if (!alreadyP6spyUrl.startsWith("jdbc:p6spy:")) {
            result = "jdbc:p6spy:" + alreadyP6spyUrl;
        } else {
            result = alreadyP6spyUrl;
        }

        System.out.println("Processed URL: " + result);

        if (result.contains("jdbc:jdbc:")) {
            System.out.println("ERROR: Double prefix detected!");
        } else {
            System.out.println("OK: Existing prefix URL handled correctly");
        }
    }
}
