<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tuowan.yeliao.commons.data.persistence.query.CIdempotentMapper">
  <sql id="Base_Column_List">
    sign, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="CIdempotent" resultType="CIdempotent">
    select 
    <include refid="Base_Column_List" />
    from c_idempotent
    where sign = #{sign}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="CIdempotent">
    delete from c_idempotent
    where sign = #{sign}
  </delete>
  <insert id="insert" parameterType="CIdempotent">
    insert into c_idempotent (sign, create_time)
    values (#{sign}, #{createTime})
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="CIdempotent">
    update c_idempotent
    <set>
      <if test="createTime != null">
        create_time = #{createTime},
      </if>
    </set>
    where sign = #{sign}
  </update>
  <update id="updateByPrimaryKey" parameterType="CIdempotent">
    update c_idempotent
    set create_time = #{createTime}
    where sign = #{sign}
  </update>
</mapper>