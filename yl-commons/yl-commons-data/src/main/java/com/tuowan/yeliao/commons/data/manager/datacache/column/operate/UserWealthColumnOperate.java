package com.tuowan.yeliao.commons.data.manager.datacache.column.operate;


import com.tuowan.yeliao.commons.core.exception.DataException;
import com.tuowan.yeliao.commons.data.dto.user.UserBusiExtendDTO;
import com.tuowan.yeliao.commons.data.entity.user.UUserWealth;
import com.tuowan.yeliao.commons.data.enums.config.ProdGoodsType;
import com.tuowan.yeliao.commons.data.manager.datacache.column.BaseBusiColumnOperate;

import java.util.*;

public class UserWealthColumnOperate extends BaseBusiColumnOperate<UUserWealth> {

    @Override
    public void setValueFromDB(UserBusiExtendDTO dto, UUserWealth t, Long programId) {
        switch (column) {
            case PlatformBeans:
                dto.setPlatformBeans(t.getPlatformBeans());
                break;
            case RechargeBeans:
                dto.setRechargeBeans(t.getRechargeBeans());
                break;
            case Silver:
                dto.setSilver(t.getSilver());
                break;
            default:
                throw new DataException("未定义业务缓存属性{}的操作方法", column);
        }
    }

    @Override
    public List<String> getLoadRedisFields(Long programId) {
        return Arrays.asList(column.name());
    }

    /**
     * redis有无缓存当前业务属性
     */
    @Override
    public boolean hasColumnInRedis(Map<String, String> fieldValueMap, Long programId) {
        if (fieldValueMap == null) {
            return false;
        }
        return fieldValueMap.containsKey(column.name());
    }

    @Override
    public void setValueFromRedis(UserBusiExtendDTO dto, Map<String, String> fieldValueMap, Long programId) {

        switch (column) {
            case PlatformBeans:
                dto.setPlatformBeans(Long.valueOf(fieldValueMap.get(column.name())));
                break;
            case RechargeBeans:
                dto.setRechargeBeans(Long.valueOf(fieldValueMap.get(column.name())));
                break;
            case Silver:
                dto.setSilver(Long.valueOf(fieldValueMap.get(column.name())));
                break;
            default:
                throw new DataException("未定义业务缓存属性{}的操作方法", column);
        }
    }

    @Override
    public void bindValue(UserBusiExtendDTO oldDTO, UserBusiExtendDTO newDTO) {
        if (oldDTO == null) {
            return;
        }
        switch (column) {
            case PlatformBeans:
                if (oldDTO.getPlatformBeans() != null) {
                    newDTO.setPlatformBeans(oldDTO.getPlatformBeans());
                }
                break;
            case RechargeBeans:
                if (oldDTO.getRechargeBeans() != null) {
                    newDTO.setRechargeBeans(oldDTO.getRechargeBeans());
                }
                break;
            case Silver:
                if (oldDTO.getSilver() != null) {
                    newDTO.setSilver(oldDTO.getSilver());
                }
                break;
            default:
                throw new DataException("未定义业务缓存属性{}的操作方法", column);
        }

    }

    @Override
    public Map<String, String> flushRedisHash(UserBusiExtendDTO dto) {
        Map<String, String> fieldValueMap = new HashMap<>();
        switch (column) {
            case PlatformBeans:
                fieldValueMap.put(column.name(), dto.getPlatformBeans().toString());
                break;
            case RechargeBeans:
                fieldValueMap.put(column.name(), dto.getRechargeBeans().toString());
                break;
            case Silver:
                fieldValueMap.put(column.name(), dto.getSilver().toString());
                break;
            default:
                throw new DataException("未定义业务缓存属性{}的操作方法", column);
        }

        return fieldValueMap;
    }

    @Override
    public Map<String, String> updateRedisHash(UserBusiExtendDTO dto) {
        return this.flushRedisHash(dto);
    }

    @Override
    public Set<String> delRedisHash(UserBusiExtendDTO dto) {
        return null;
    }

    @Override
    public ProdGoodsType changeBindBagProdType() {
        return null;
    }

    @Override
    public void updateByBagChange(UserBusiExtendDTO dto) {
        return;
    }

}
