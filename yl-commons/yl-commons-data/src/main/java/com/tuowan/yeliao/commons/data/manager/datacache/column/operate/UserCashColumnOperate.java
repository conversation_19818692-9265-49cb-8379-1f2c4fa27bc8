package com.tuowan.yeliao.commons.data.manager.datacache.column.operate;


import com.tuowan.yeliao.commons.core.exception.DataException;
import com.tuowan.yeliao.commons.data.dto.query.UCashDTO;
import com.tuowan.yeliao.commons.data.dto.user.UserBusiExtendDTO;
import com.tuowan.yeliao.commons.data.enums.config.ProdGoodsType;
import com.tuowan.yeliao.commons.data.manager.datacache.column.BaseBusiColumnOperate;

import java.util.*;

/**
 * u_cash 对应的业务缓存操作方法
 *
 * <AUTHOR>
 * @date 2020/7/7 11:08
 */
public class UserCashColumnOperate extends BaseBusiColumnOperate<UCashDTO> {
    @Override
    public void setValueFromDB(UserBusiExtendDTO dto, UCashDTO c, Long programId) {
        switch (column) {
            case Cash:
                dto.setCash(c.getCash());
                break;
            case CashTotal:
                dto.setCashTotal(c.getTotalCash());
                break;
            default:
                throw new DataException("未定义业务缓存属性{}的操作方法", column);
        }
    }

    @Override
    public List<String> getLoadRedisFields(Long programId) {
        return Arrays.asList(column.name());
    }

    @Override
    public boolean hasColumnInRedis(Map<String, String> fieldValueMap, Long programId) {
        if (fieldValueMap == null) {
            return false;
        }
        return fieldValueMap.containsKey(column.name());
    }

    @Override
    public void setValueFromRedis(UserBusiExtendDTO dto, Map<String, String> fieldValueMap, Long programId) {
        switch (column) {
            case Cash:
                dto.setCash(Long.valueOf(fieldValueMap.get(column.name())));
                break;
            case CashTotal:
                dto.setCashTotal(Long.valueOf(fieldValueMap.get(column.name())));
                break;
            default:
                throw new DataException("未定义业务缓存属性{}的操作方法", column);
        }
    }

    @Override
    public void bindValue(UserBusiExtendDTO oldDTO, UserBusiExtendDTO newDTO) {
        if (oldDTO == null) {
            return;
        }
        switch (column) {
            case Cash:
                if (oldDTO.getCash() != null) {
                    newDTO.setCash(oldDTO.getCash());
                }
                break;
            case CashTotal:
                if (oldDTO.getCashTotal() != null) {
                    newDTO.setCashTotal(oldDTO.getCashTotal());
                }
                break;
            default:
                throw new DataException("未定义业务缓存属性{}的操作方法", column);
        }
    }

    @Override
    public Map<String, String> flushRedisHash(UserBusiExtendDTO dto) {
        Map<String, String> fieldValueMap = new HashMap<>();
        switch (column) {
            case Cash:
                fieldValueMap.put(column.name(), dto.getCash().toString());
                break;
            case CashTotal:
                fieldValueMap.put(column.name(), dto.getCashTotal().toString());
                break;
            default:
                throw new DataException("未定义业务缓存属性{}的操作方法", column);
        }
        return fieldValueMap;
    }

    @Override
    public Map<String, String> updateRedisHash(UserBusiExtendDTO dto) {
        return this.flushRedisHash(dto);
    }

    @Override
    public Set<String> delRedisHash(UserBusiExtendDTO dto) {
        return null;
    }

    @Override
    public ProdGoodsType changeBindBagProdType() {
        return null;
    }

    @Override
    public void updateByBagChange(UserBusiExtendDTO dto) {

    }
}
