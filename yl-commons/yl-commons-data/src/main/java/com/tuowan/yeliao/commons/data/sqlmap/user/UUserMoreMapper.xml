<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tuowan.yeliao.commons.data.persistence.user.UUserMoreMapper">
  <sql id="Base_Column_List">
    user_id, im_token, constellation_id, height, weight, hometown_city_id, station_city_id,
    education, school_id, profession_id, income, profession_feature, start_year, perfect_code,
    wechat, marriage_status,
    live_status, house_status, car_status, live_together, appointment, wx_qr, footprints, media_status, mobile_contact, wx_contact
  </sql>
  <select id="selectByPrimaryKey" parameterType="UUserMore" resultType="UUserMore">
    select 
    <include refid="Base_Column_List" />
    from u_user_more
    where user_id = #{userId}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="UUserMore">
    delete from u_user_more
    where user_id = #{userId}
  </delete>
  <insert id="insert" parameterType="UUserMore">
    insert into u_user_more (user_id, im_token, constellation_id, height, weight, hometown_city_id, station_city_id,
    education, school_id, profession_id, income, profession_feature,
    start_year, perfect_code, wechat, marriage_status,
    live_status, house_status, car_status, live_together, appointment, wx_qr, footprints, media_status, mobile_contact, wx_contact)
    values (#{userId}, #{imToken}, #{constellationId}, #{height}, #{weight}, #{hometownCityId}, #{stationCityId},
    #{education}, #{schoolId}, #{professionId}, #{income}, #{professionFeature},
    #{startYear}, #{perfectCode}, #{wechat}, #{marriageStatus},
    #{liveStatus}, #{houseStatus}, #{carStatus}, #{liveTogether}, #{appointment}, #{wxQr}, #{footprints}, #{mediaStatus}, #{mobileContact}, #{wxContact})
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="UUserMore">
    update u_user_more
    <set>
      <if test="imToken != null">
        im_token = #{imToken},
      </if>
      <if test="constellationId != null">
        constellation_id = #{constellationId},
      </if>
      <if test="height != null">
        height = #{height},
      </if>
      <if test="weight != null">
        weight = #{weight},
      </if>
      <if test="hometownCityId != null">
        hometown_city_id = #{hometownCityId},
      </if>
      <if test="stationCityId != null">
        station_city_id = #{stationCityId},
      </if>
      <if test="education != null">
        education = #{education},
      </if>
      <if test="schoolId != null">
        school_id = #{schoolId},
      </if>
      <if test="professionId != null">
        profession_id = #{professionId},
      </if>
      <if test="income != null">
        income = #{income},
      </if>
      <if test="professionFeature != null">
        profession_feature = #{professionFeature},
      </if>
      <if test="startYear != null">
        start_year = #{startYear},
      </if>
      <if test="perfectCode != null">
        perfect_code = #{perfectCode},
      </if>
      <if test="wechat != null">
        wechat = #{wechat},
      </if>
      <if test="marriageStatus != null">
        marriage_status = #{marriageStatus},
      </if>
      <if test="liveStatus != null">
        live_status = #{liveStatus},
      </if>
      <if test="houseStatus != null">
        house_status = #{houseStatus},
      </if>
      <if test="carStatus != null">
        car_status = #{carStatus},
      </if>
      <if test="liveTogether != null">
        live_together = #{liveTogether},
      </if>
      <if test="appointment != null">
        appointment = #{appointment},
      </if>
      <if test="wxQr != null">
        wx_qr = #{wxQr},
      </if>
      <if test="footprints != null">
        footprints = #{footprints},
      </if>
      <if test="mediaStatus != null">
        media_status = #{mediaStatus},
      </if>
      <if test="mobileContact != null">
        mobile_contact = #{mobileContact},
      </if>
      <if test="wxContact != null">
        wx_contact = #{wxContact},
      </if>
    </set>
    where user_id = #{userId}
  </update>
  <update id="updateByPrimaryKey" parameterType="UUserMore">
    update u_user_more
    set im_token = #{imToken},
      constellation_id = #{constellationId},
      height = #{height},
      weight = #{weight},
      hometown_city_id = #{hometownCityId},
      station_city_id = #{stationCityId},
      education = #{education},
      school_id = #{schoolId},
      profession_id = #{professionId},
      income = #{income},
      profession_feature = #{professionFeature},
      start_year = #{startYear},
      perfect_code = #{perfectCode},
      wechat = #{wechat},
      marriage_status = #{marriageStatus},
      live_status = #{liveStatus},
      house_status = #{houseStatus},
      car_status = #{carStatus},
      live_together = #{liveTogether},
      appointment = #{appointment},
      wx_qr = #{wxQr},
      footprints = #{footprints},
      media_status = #{mediaStatus},
      mobile_contact = #{mobileContact},
      wx_contact = #{wxContact}
    where user_id = #{userId}
  </update>
</mapper>