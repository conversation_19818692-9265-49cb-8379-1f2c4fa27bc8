package com.tuowan.yeliao.commons.data.manager.commons;

import com.easyooo.framework.common.dto.KeyPairInfo;
import com.easyooo.framework.common.http.HttpRequestExt;
import com.easyooo.framework.common.util.*;
import com.tuowan.yeliao.commons.config.configuration.impl.ApiKeyConfig;
import com.tuowan.yeliao.commons.config.configuration.impl.AppConfig;
import com.tuowan.yeliao.commons.config.redis.RedisKey;
import com.tuowan.yeliao.commons.config.redis.template.BusiRedisTemplate;
import com.tuowan.yeliao.commons.core.enums.redis.BusiKeyDefine;
import com.tuowan.yeliao.commons.core.http.HttpPoolManagerFactory;
import com.tuowan.yeliao.commons.data.dto.common.PreheatRequestDTO;
import com.tuowan.yeliao.commons.data.enums.config.SettingsType;
import com.tuowan.yeliao.commons.data.support.config.impl.SettingsConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 程序预热业务封装
 *
 * <AUTHOR>
 * @date 2022/8/5 17:56
 */
@Component
public class PreheatManager {

    private static final Logger LOG = LoggerFactory.getLogger(PreheatManager.class);

    private static final String HEADER_INFO = "o=13.5.1&h=1615269239183&a=R&i=4F18D7B9-EF84-49AA-9888-312FF3C95972-46300974&x=F14DBC43-0663-4F6E-9221-D3A0339F6613&r=T&c=AStore&d=0&t=B&m=iPhone%206%20S&n=Wifi&v=*******";

    /** 接口调用时间最大值 */
    private final long PREHEAT_EXEC_MAX_TIME = 800;

    private final Integer PREHEAT_MIN_TIMES = 10;

    @Autowired
    private BusiRedisTemplate busiRedisTemplate;

    public void start(List<PreheatRequestDTO> requestList) {
        long start = System.currentTimeMillis();
        String sessionId = SettingsConfig.getString(SettingsType.PreheatSessionId);
        if (!busiRedisTemplate.exists(buildSidKey(sessionId))) {
            LOG.warn("会话已失效，无法执行程序预热");
            return;
        }
        ExecutorService service = Executors.newFixedThreadPool(50);
        Integer[] timesList = new Integer[]{2, 4, 6, 8, 10};
        for (int times : timesList) {
            try {
                int count = requestList.size() * times;
                CountDownLatch downLatch = new CountDownLatch(count);
                AtomicLong execTime = new AtomicLong();
                for (PreheatRequestDTO request : requestList) {
                    for (int j = 0; j < times; j++) {
                        service.submit(new PreheatTask(downLatch, request, sessionId, execTime));
                    }
                }
                downLatch.await();
                // 如果接口平均耗时小于预期值，则终止预热
                long avgTime = execTime.get() / count;
                if (times >= PREHEAT_MIN_TIMES && avgTime <= PREHEAT_EXEC_MAX_TIME) {
                    break;
                }
                LOG.info("程序预热中，每个接口执行：{}次，接口平均耗时：{}ms", times, avgTime);
            } catch (Exception e) {
                LOG.error("程序预热失败，原因：", e);
            }
        }
        long execTime = System.currentTimeMillis() - start;
        LOG.info("程序预热完成，累计耗时：{}ms", execTime);
        service.shutdown();
    }

    private class PreheatTask implements Runnable {

        private CountDownLatch downLatch;
        private PreheatRequestDTO request;
        private String sessionId;
        private AtomicLong execTime;

        public PreheatTask(CountDownLatch downLatch, PreheatRequestDTO request, String sessionId, AtomicLong execTime) {
            this.downLatch = downLatch;
            this.request = request;
            this.sessionId = sessionId;
            this.execTime = execTime;
        }

        @Override
        public void run() {
            execTime.addAndGet(sendRequest(request, sessionId));
            downLatch.countDown();
        }
    }

    private long sendRequest(PreheatRequestDTO request, String sessionId) {
        long start = System.currentTimeMillis();
        // 存储请求头参数
        String headInfo = HEADER_INFO;
        if (sessionId != null) {
            headInfo = headInfo + "&s=" + sessionId;
        }
        Map<String, Object> headers = new HashMap<>();
        String aesKey = StringUtils.generateRandomStr(16);
        headers.put("x-tid", "B" + StringUtils.getUUID());
        headers.put("x-arg", RSAUtils.encrypt(aesKey, ApiKeyConfig.ROBOT_PUB));
        headers.put("x-inf", EncryptUtils.encryptByAESWithECB(headInfo, aesKey));

        Map<String, String> reqParams = new HashMap<>();
        // 将JSON数据加密，其中参数名固定为data
        reqParams.put("data", EncryptUtils.encryptByAESWithECB(JsonUtils.seriazileAsString(request.getParams()), aesKey));
        HttpRequestExt ext = new HttpRequestExt(headers);
        String apiUrl = MsgUtils.format("http://localhost:{}{}", AppConfig.SERVER_PORT, request.getMappingUri());
        try {
            HttpPoolManagerFactory.getLoginPool().post(apiUrl, reqParams, ext);
        } catch (Exception e) {
            LOG.error(MsgUtils.format("接口请求失败，apiUrl: {}，原因：", apiUrl), e);
        }
        return System.currentTimeMillis() - start;
    }

    private RedisKey buildSidKey(String sessionId) {
        return RedisKey.create(BusiKeyDefine.Sid, sessionId);
    }

    public static void main(String[] args) {
        KeyPairInfo info = RSAUtils.getKeyPair(1024);
        System.out.println(info.getPrivateKey());
        System.out.println(info.getPublicKey());
    }
}
