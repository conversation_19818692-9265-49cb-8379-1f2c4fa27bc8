package com.tuowan.yeliao.commons.data.utils;

import com.tuowan.yeliao.commons.data.dto.common.QrCodeOptionsDTO;
import com.tuowan.yeliao.commons.data.support.qr.QrCodeGenWrapper;
import com.easyooo.framework.common.util.ImageUtils;
import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.MultiFormatWriter;
import com.google.zxing.WriterException;
import com.google.zxing.common.BitMatrix;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.util.Map;

/**
 * <AUTHOR> WeiGuo
 * @date 2019/3/7 10:42
 */
public class QrCodeUtils {

    /**
     * 生成二维码
     */
    public static BitMatrix encode(QrCodeOptionsDTO cfg) throws Exception {
        String msg = cfg.getMsg();
        Integer w = cfg.getWidth();
        Integer h = cfg.getHeight();
        Map<EncodeHintType, Object> hints = cfg.getHints();
        BitMatrix encode = null;
        try {
            encode = new MultiFormatWriter().encode(msg, BarcodeFormat.QR_CODE, w, h, hints);
        } catch (WriterException e) {
            throw new Exception("二维码生成失败：调用谷歌encode失败", e);
        }
        return deleteWhite(encode);
    }

    /**
     * 删除白边
     */
    private static BitMatrix deleteWhite(BitMatrix matrix) {
        int[] rec = matrix.getEnclosingRectangle();
        int resWidth = rec[2] + 1;
        int resHeight = rec[3] + 1;

        BitMatrix resMatrix = new BitMatrix(resWidth, resHeight);
        resMatrix.clear();
        for (int i = 0; i < resWidth; i++) {
            for (int j = 0; j < resHeight; j++) {
                if (matrix.get(i + rec[0], j + rec[1]))
                    resMatrix.set(i, j);
            }
        }
        return resMatrix;
    }

    /**
     * 根据二维码配置 & 二维码矩阵生成二维码图片
     *
     * @param cfg
     * @param bitMatrix
     * @return
     * @throws IOException
     */
    public static BufferedImage toBufferedImage(QrCodeOptionsDTO cfg, BitMatrix bitMatrix) throws Exception {
        int width = bitMatrix.getWidth();
        int height = bitMatrix.getHeight();
        BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);

        for (int x = 0; x < width; x++) {
            for (int y = 0; y < height; y++) {
                image.setRGB(x, y,
                        bitMatrix.get(x, y) ?
                                cfg.getMatrixToImageConfig().getPixelOnColor() :
                                cfg.getMatrixToImageConfig().getPixelOffColor());
            }
        }

        // 插入logo
        if (!(cfg.getLogo() == null || "".equals(cfg.getLogo()))) {
            Image img = getImageByPath(cfg.getLogo());
            Graphics2D gs = image.createGraphics();
            int ratioWidth = image.getWidth() * 2 / 10;
            int ratioHeight = image.getHeight() * 2 / 10;

            int logoWidth = img.getWidth(null) > ratioWidth ? ratioWidth : img.getWidth(null);
            int logoHeight = img.getHeight(null) > ratioHeight ? ratioHeight : img.getHeight(null);

            int x = (image.getWidth() - logoWidth) / 2;
            int y = (image.getHeight() - logoHeight) / 2;

            gs.drawImage(img, x, y, logoWidth, logoHeight, null);
            gs.setColor(Color.black);
            gs.setBackground(Color.WHITE);
            gs.dispose();
            img.flush();
        }

        return image;
    }

    /**
     * 根据路径获取图片
     *
     * @param path 本地路径 or 网络地址
     * @return 图片
     * @throws IOException
     */
    private static BufferedImage getImageByPath(String path) throws Exception {
        try {
            if (path.startsWith("http")) { // 从网络获取logo
                byte[] data = ImageUtils.getImgBytes(path);
                if (data == null) {
                    // 如果图片数据为空，使用APP的LOGO
                    //data = ImageUtils.getImgBytes(ServiceUrlConfig.FILE_URL + "/" + GlobalConstant.APP_LOGO_PIC);
                }
                return ImageIO.read(new ByteArrayInputStream(data));
            } else if (path.startsWith("/")) { // 绝对地址获取logo
                return ImageIO.read(new File(path));
            } else { // 从资源目录下获取logo
                return ImageIO.read(QrCodeUtils.class.getClassLoader().getResourceAsStream(path));
            }
        } catch (IOException e) {
            throw new Exception("生成二维码失败：二维码logo获取失败", e);
        }
    }

    public static void main(String[] args) throws Exception{
        String s = QrCodeGenWrapper.of("http://www.baidu.com").setHeight(120).setWidth(120).asBase64();
        System.out.println(s);
    }

}
