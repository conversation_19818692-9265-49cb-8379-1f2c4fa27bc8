package com.tuowan.yeliao.commons.data.dto.user;


import com.tuowan.yeliao.commons.core.enums.general.BoolType;
import com.tuowan.yeliao.commons.data.entity.config.TProdSocialGift;
import com.tuowan.yeliao.commons.data.entity.user.UUserBasic;
import com.tuowan.yeliao.commons.data.enums.user.GuardAction;

/**
 * 守护信息
 *
 * <AUTHOR>
 * @date 2021/4/28 19:24
 */
public class GuardInfoDTO {

    /** 执行动作 */
    private GuardAction action;
    /** 用户ID */
    private Long userId;
    /** 用户昵称 */
    private String nickname;
    /** 用户头像 */
    private String headPic;
    /** 剩余天数 */
    private Integer surplusDays;


    /** 所需数量量 */
    private Integer needNum;
    /** 所需金币 */
    private Integer needBeans;
    /** 礼物图标 */
    private String giftPic;
    /** 守护一次单位有效天 */
    private Integer unitDays;
    /** 隐藏守护者 */
    private BoolType hideGuard;

    public static GuardInfoDTO create(GuardAction action, Integer needNum, TProdSocialGift gift) {
        GuardInfoDTO dto = new GuardInfoDTO();
        dto.setAction(action);
        dto.setNeedNum(needNum);
        dto.setNeedBeans(needNum * gift.getBeans());
        dto.setGiftPic(gift.getPic());
        return dto;
    }

    public static GuardInfoDTO create(GuardAction action, UUserBasic user) {
        return GuardInfoDTO.create(action, user, null);
    }

    public static GuardInfoDTO create(GuardAction action, UUserBasic user, Integer surplusDays) {
        GuardInfoDTO dto = new GuardInfoDTO();
        dto.setAction(action);
        dto.setUserId(user.getUserId());
        dto.setNickname(user.getNickname());
        dto.setHeadPic(user.getHeadPic());
        dto.setSurplusDays(surplusDays);
        return dto;
    }

    public static GuardInfoDTO create(GuardAction action, UUserBasic user, Integer surplusDays, TProdSocialGift gift,Integer unitDays) {
        GuardInfoDTO dto = new GuardInfoDTO();
        dto.setAction(action);
        dto.setUserId(user.getUserId());
        dto.setNickname(user.getNickname());
        dto.setHeadPic(user.getHeadPic());
        dto.setSurplusDays(surplusDays);
        dto.setNeedBeans(gift.getBeans());
        dto.setGiftPic(gift.getPic());
        dto.setUnitDays(unitDays);
        return dto;
    }

    public GuardAction getAction() {
        return action;
    }

    public void setAction(GuardAction action) {
        this.action = action;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getHeadPic() {
        return headPic;
    }

    public void setHeadPic(String headPic) {
        this.headPic = headPic;
    }

    public Integer getSurplusDays() {
        return surplusDays;
    }

    public void setSurplusDays(Integer surplusDays) {
        this.surplusDays = surplusDays;
    }

    public Integer getNeedNum() {
        return needNum;
    }

    public void setNeedNum(Integer needNum) {
        this.needNum = needNum;
    }

    public Integer getNeedBeans() {
        return needBeans;
    }

    public void setNeedBeans(Integer needBeans) {
        this.needBeans = needBeans;
    }

    public String getGiftPic() {
        return giftPic;
    }

    public void setGiftPic(String giftPic) {
        this.giftPic = giftPic;
    }

    public Integer getUnitDays() {
        return unitDays;
    }

    public void setUnitDays(Integer unitDays) {
        this.unitDays = unitDays;
    }

    public BoolType getHideGuard() {
        return hideGuard;
    }

    public void setHideGuard(BoolType hideGuard) {
        this.hideGuard = hideGuard;
    }
}
