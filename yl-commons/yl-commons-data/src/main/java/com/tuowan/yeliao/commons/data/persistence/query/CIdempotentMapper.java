/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.commons.data.persistence.query;

import com.easyooo.framework.sharding.annotation.Table;
import com.tuowan.yeliao.commons.data.entity.query.CIdempotent;
import org.springframework.stereotype.Repository;

@Repository
@Table(value = "C_IDEMPOTENT", schema = "YL_BUSI")
public interface CIdempotentMapper {
    int deleteByPrimaryKey(CIdempotent record);

    int insert(CIdempotent record);

    CIdempotent selectByPrimaryKey(CIdempotent record);

    int updateByPrimaryKeySelective(CIdempotent record);

    int updateByPrimaryKey(CIdempotent record);
}