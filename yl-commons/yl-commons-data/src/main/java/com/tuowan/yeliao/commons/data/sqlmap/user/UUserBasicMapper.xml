<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tuowan.yeliao.commons.data.persistence.user.UUserBasicMapper">
  <sql id="Base_Column_List">
    user_id, invite_code, user_type, nickname, alise_name, head_pic, real_name, real_person, sex, birth_date,
    my_sign, default_sign, head_status, chat_master_level, status, vip_type, vip_exp_time, create_time, acct_status
  </sql>
  <select id="selectByPrimaryKey" parameterType="UUserBasic" resultType="UUserBasic">
    select 
    <include refid="Base_Column_List" />
    from u_user_basic
    where user_id = #{userId}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="UUserBasic">
    delete from u_user_basic
    where user_id = #{userId}
  </delete>
  <insert id="insert" parameterType="UUserBasic">
    insert into u_user_basic (user_id, invite_code, user_type, nickname, alise_name, head_pic, real_name, real_person,
                              sex, birth_date, my_sign, default_sign, head_status,
                              chat_master_level, status, vip_type, vip_exp_time, create_time, acct_status)
    values (#{userId}, #{inviteCode}, #{userType}, #{nickname}, #{aliseName}, #{headPic}, #{realName}, #{realPerson},
            #{sex}, #{birthDate}, #{mySign}, #{defaultSign}, #{headStatus},
            #{chatMasterLevel}, #{status}, #{vipType}, #{vipExpTime}, #{createTime}, #{acctStatus})
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="UUserBasic">
    update u_user_basic
    <set>
      <if test="inviteCode != null">
        invite_code = #{inviteCode},
      </if>
      <if test="userType != null">
        user_type = #{userType},
      </if>
      <if test="nickname != null">
        nickname = #{nickname},
      </if>
      <if test="aliseName != null">
        alise_name = #{aliseName},
      </if>
      <if test="headPic != null">
        head_pic = #{headPic},
      </if>
      <if test="realName != null">
        real_name = #{realName},
      </if>
      <if test="realPerson != null">
        real_person = #{realPerson},
      </if>
      <if test="sex != null">
        sex = #{sex},
      </if>
      <if test="birthDate != null">
        birth_date = #{birthDate},
      </if>
      <if test="mySign != null">
        my_sign = #{mySign},
      </if>
      <if test="defaultSign != null">
        default_sign = #{defaultSign},
      </if>
      <if test="headStatus != null">
        head_status = #{headStatus},
      </if>
      <if test="chatMasterLevel != null">
        chat_master_level = #{chatMasterLevel},
      </if>
      <if test="status != null">
        status = #{status},
      </if>
      <if test="vipType != null">
        vip_type = #{vipType},
      </if>
      <if test="vipExpTime != null">
        vip_exp_time = #{vipExpTime},
      </if>
      <if test="createTime != null">
        create_time = #{createTime},
      </if>
      <if test="acctStatus != null">
        acct_status = #{acctStatus},
      </if>
    </set>
    where user_id = #{userId}
  </update>
  <update id="updateByPrimaryKey" parameterType="UUserBasic">
    update u_user_basic
    set invite_code       = #{inviteCode},
        user_type         = #{userType},
        nickname          = #{nickname},
        alise_name        = #{aliseName},
        head_pic          = #{headPic},
        real_name         = #{realName},
        real_person       = #{realPerson},
        sex               = #{sex},
        birth_date        = #{birthDate},
        my_sign           = #{mySign},
        default_sign      = #{defaultSign},
        head_status       = #{headStatus},
        chat_master_level = #{chatMasterLevel},
        status            = #{status},
        vip_type          = #{vipType},
        vip_exp_time      = #{vipExpTime},
        create_time       = #{createTime},
        acct_status       = #{acctStatus}
    where user_id = #{userId}
  </update>

    <select id="existsForNicknameNotSelf" resultType="int">
    select count(1) from u_user_basic
    <where>
      <if test="userId != null">
        and user_id != #{userId}
      </if>
      <if test="nickname != null">
        and nickname = #{nickname}
      </if>
    </where>
    limit 1
  </select>

  <select id="selectForUpdate" parameterType="map" resultType="UUserBasic">
      select
      <include refid="Base_Column_List" />
      from u_user_basic
      where user_id = #{userId}
      for update
  </select>

  <select id="selectUserIds" resultType="java.lang.Long">
    select user_id from yl_busi.u_user_basic where status = 'E'
    and head_pic is not null and nickname is not null and birth_date is not null
  </select>
  <select id="selectFemaleUserIds" resultType="java.lang.Long">
    select user_id from yl_busi.u_user_basic where sex = 'F' and status = 'E'
    and head_pic is not null and nickname is not null and birthday is not null limit 0, 10000
  </select>

  <select id="selectMaleUserIds" resultType="java.lang.Long">
    select user_id from yl_busi.u_user_basic where sex = 'M' and status = 'E'
    and head_pic is not null and nickname is not null and birthday is not null limit 0, 100
  </select>

  <select id="listPidsByNickname" resultType="java.lang.Long">
    select user_id
    from yl_busi.u_user_basic
    where user_type = 'A'
    and nickname like concat('%', #{nickname}, '%')
  </select>

  <select id="selectTestUserList" resultType="UUserBasic">
    select
    <include refid="Base_Column_List" />
    from u_user_basic
    where status = 'E'
  </select>

  <insert id="initRelationTotal">
    insert into yl_social.f_relation_total (user_id, follow_cnt, fans_cnt, intimate_cnt, friend_cnt)
    values (#{userId}, 0, 0, 0, 0)
  </insert>

  <select id="selectAllUserBasic" resultType="UUserBasic">
    select
    <include refid="Base_Column_List" />
    from u_user_basic
  </select>
</mapper>