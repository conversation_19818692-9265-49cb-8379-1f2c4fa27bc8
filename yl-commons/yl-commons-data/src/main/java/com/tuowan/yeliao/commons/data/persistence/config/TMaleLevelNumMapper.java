/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.commons.data.persistence.config;

import com.easyooo.framework.sharding.annotation.Table;
import com.tuowan.yeliao.commons.data.entity.config.TMaleLevelNum;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@Table(value = "t_male_level_num", schema = "YL_CONFIG")
public interface TMaleLevelNumMapper {
    int deleteByPrimaryKey(TMaleLevelNum record);

    int insert(TMaleLevelNum record);

    TMaleLevelNum selectByPrimaryKey(TMaleLevelNum record);

    int updateByPrimaryKeySelective(TMaleLevelNum record);

    int updateByPrimaryKey(TMaleLevelNum record);

    List<TMaleLevelNum> selectAll();
}