/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.commons.data.persistence.config;

import com.easyooo.framework.sharding.annotation.Table;
import com.tuowan.yeliao.commons.data.entity.config.TProdSocialGift;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@Table(value = "T_PROD_CHAT_GIFT", schema = "YL_CONFIG")
public interface TProdSocialGiftMapper {
    int deleteByPrimaryKey(TProdSocialGift record);

    int insert(TProdSocialGift record);

    TProdSocialGift selectByPrimaryKey(TProdSocialGift record);

    int updateByPrimaryKeySelective(TProdSocialGift record);

    int updateByPrimaryKey(TProdSocialGift record);

    List<TProdSocialGift> selectAll();
}