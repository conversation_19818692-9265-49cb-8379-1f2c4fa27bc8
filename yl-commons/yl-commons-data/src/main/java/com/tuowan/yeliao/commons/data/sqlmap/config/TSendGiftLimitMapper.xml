<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tuowan.yeliao.commons.data.persistence.config.TSendGiftLimitMapper">
  <sql id="Base_Column_List">
    cfg_id, province, city, limit_money, female_num, male_num, status, creator, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="TSendGiftLimit" resultType="TSendGiftLimit">
    select 
    <include refid="Base_Column_List" />
    from t_send_gift_limit
    where cfg_id = #{cfgId}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="TSendGiftLimit">
    delete from t_send_gift_limit
    where cfg_id = #{cfgId}
  </delete>
  <insert id="insert" parameterType="TSendGiftLimit">
    insert into t_send_gift_limit (cfg_id, province, city, limit_money, female_num, male_num, 
      status, creator, create_time)
    values (#{cfgId}, #{province}, #{city}, #{limitMoney}, #{femaleNum}, #{maleNum}, 
      #{status}, #{creator}, #{createTime})
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="TSendGiftLimit">
    update t_send_gift_limit
    <set>
      <if test="province != null">
        province = #{province},
      </if>
      <if test="city != null">
        city = #{city},
      </if>
      <if test="limitMoney != null">
        limit_money = #{limitMoney},
      </if>
      <if test="femaleNum != null">
        female_num = #{femaleNum},
      </if>
      <if test="maleNum != null">
        male_num = #{maleNum},
      </if>
      <if test="status != null">
        status = #{status},
      </if>
      <if test="creator != null">
        creator = #{creator},
      </if>
      <if test="createTime != null">
        create_time = #{createTime},
      </if>
    </set>
    where cfg_id = #{cfgId}
  </update>
  <update id="updateByPrimaryKey" parameterType="TSendGiftLimit">
    update t_send_gift_limit
    set province = #{province},
      city = #{city},
      limit_money = #{limitMoney},
      female_num = #{femaleNum},
      male_num = #{maleNum},
      status = #{status},
      creator = #{creator},
      create_time = #{createTime}
    where cfg_id = #{cfgId}
  </update>

  <select id="selectByProvince" parameterType="TSendGiftLimit" resultType="TSendGiftLimit">
    select
    <include refid="Base_Column_List" />
    from t_send_gift_limit
    where province = #{province} and status = 'E'
  </select>

</mapper>