<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tuowan.yeliao.commons.data.persistence.user.UUserAgreementMapper">
    <sql id="Base_Column_List">
        user_id, external_agreement_no, agreement_type, client_type, client_version, tpl_id,
        period_type, period, single_amount, execute_time, package_type, agreement_status,
        callback_time, agreement_no, remark, third_party_user_id, sign_time, pay_fail_count,
        create_time, beans, give_beans, ext_json_cfg, valid_time, invalid_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="UUserAgreement" resultType="UUserAgreement">
        select
        <include refid="Base_Column_List"/>
        from u_user_agreement
        where user_id = #{userId}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="UUserAgreement">
        delete from u_user_agreement
        where user_id = #{userId}
    </delete>
    <insert id="insert" parameterType="UUserAgreement">
        insert into u_user_agreement (user_id, external_agreement_no, agreement_type, client_type, client_version,
        tpl_id, period_type, period, single_amount, execute_time, package_type,
        agreement_status, callback_time, agreement_no, remark, third_party_user_id,
        sign_time, pay_fail_count, create_time, beans, give_beans, ext_json_cfg,
        valid_time, invalid_time)
        values (#{userId}, #{externalAgreementNo}, #{agreementType}, #{clientType}, #{clientVersion},
        #{tplId}, #{periodType}, #{period}, #{singleAmount}, #{executeTime}, #{packageType},
        #{agreementStatus}, #{callbackTime}, #{agreementNo}, #{remark}, #{thirdPartyUserId},
        #{signTime}, #{payFailCount}, #{createTime}, #{beans}, #{giveBeans}, #{extJsonCfg},
        #{validTime}, #{invalidTime})
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="UUserAgreement">
        update u_user_agreement
        <set>
            <if test="externalAgreementNo != null">
                external_agreement_no = #{externalAgreementNo},
            </if>
            <if test="agreementType != null">
                agreement_type = #{agreementType},
            </if>
            <if test="clientType != null">
                client_type = #{clientType},
            </if>
            <if test="clientVersion != null">
                client_version = #{clientVersion},
            </if>
            <if test="tplId != null">
                tpl_id = #{tplId},
            </if>
            <if test="periodType != null">
                period_type = #{periodType},
            </if>
            <if test="period != null">
                period = #{period},
            </if>
            <if test="singleAmount != null">
                single_amount = #{singleAmount},
            </if>
            <if test="executeTime != null">
                execute_time = #{executeTime},
            </if>
            <if test="packageType != null">
                package_type = #{packageType},
            </if>
            <if test="agreementStatus != null">
                agreement_status = #{agreementStatus},
            </if>
            <if test="callbackTime != null">
                callback_time = #{callbackTime},
            </if>
            <if test="agreementNo != null">
                agreement_no = #{agreementNo},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
            <if test="thirdPartyUserId != null">
                third_party_user_id = #{thirdPartyUserId},
            </if>
            <if test="signTime != null">
                sign_time = #{signTime},
            </if>
            <if test="payFailCount != null">
                pay_fail_count = #{payFailCount},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="beans != null">
                beans = #{beans},
            </if>
            <if test="giveBeans != null">
                give_beans = #{giveBeans},
            </if>
            <if test="extJsonCfg != null">
                ext_json_cfg = #{extJsonCfg},
            </if>
            <if test="validTime != null">
                valid_time = #{validTime},
            </if>
            <if test="invalidTime != null">
                invalid_time = #{invalidTime},
            </if>
        </set>
        where user_id = #{userId}
    </update>
    <update id="updateByPrimaryKey" parameterType="UUserAgreement">
        update u_user_agreement
        set external_agreement_no = #{externalAgreementNo},
        agreement_type = #{agreementType},
        client_type = #{clientType},
        client_version = #{clientVersion},
        tpl_id = #{tplId},
        period_type = #{periodType},
        period = #{period},
        single_amount = #{singleAmount},
        execute_time = #{executeTime},
        package_type = #{packageType},
        agreement_status = #{agreementStatus},
        callback_time = #{callbackTime},
        agreement_no = #{agreementNo},
        remark = #{remark},
        third_party_user_id = #{thirdPartyUserId},
        sign_time = #{signTime},
        pay_fail_count = #{payFailCount},
        create_time = #{createTime},
        beans = #{beans},
        give_beans = #{giveBeans},
        ext_json_cfg = #{extJsonCfg},
        valid_time = #{validTime},
        invalid_time = #{invalidTime}
        where user_id = #{userId}
    </update>

    <select id="selectByExternalAgreementNo" parameterType="UUserAgreement" resultType="UUserAgreement">
        select
        <include refid="Base_Column_List"/>
        from u_user_agreement
        where external_agreement_no = #{externalAgreementNo}
    </select>

    <select id="selectByAgreementNo" resultType="UUserAgreement">
        select
        <include refid="Base_Column_List"/>
        from u_user_agreement
        where agreement_no = #{agreementNo}
    </select>

    <select id="selectByExecuteTimeAndAgreementType" resultType="UUserAgreement">
        select
        <include refid="Base_Column_List"/>
        from u_user_agreement
        where execute_time &lt;= #{end} and agreement_type = #{type} and agreement_status = 'N'
    </select>

    <select id="selectAllWaitRemindAgreement" resultType="UUserAgreement">
        select
        <include refid="Base_Column_List"/>
        from u_user_agreement
        where execute_time &gt;= #{start} and execute_time &lt;= #{end}
    </select>
</mapper>