/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.commons.data.persistence.config;

import com.easyooo.framework.sharding.annotation.Table;
import com.tuowan.yeliao.commons.data.entity.config.TSettings;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@Table(value = "T_SETTINGS", schema = "YL_CONFIG")
public interface TSettingsMapper {
    int deleteByPrimaryKey(TSettings record);

    int insert(TSettings record);

    TSettings selectByPrimaryKey(TSettings record);

    int updateByPrimaryKeySelective(TSettings record);

    int updateByPrimaryKey(TSettings record);

    List<TSettings> selectAll();

    int updateIncrValue(String paramName);
}