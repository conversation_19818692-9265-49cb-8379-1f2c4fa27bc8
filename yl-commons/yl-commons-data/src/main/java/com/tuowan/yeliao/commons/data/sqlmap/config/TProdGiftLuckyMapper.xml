<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tuowan.yeliao.commons.data.persistence.config.TProdGiftLuckyMapper">
  <sql id="Base_Column_List">
    key_id, match_rule, gift_id, count_min, count_max, luck_code, count_type, count_type_value, 
    luck_pool_type
  </sql>
  <select id="selectByPrimaryKey" parameterType="TProdGiftLucky" resultType="TProdGiftLucky">
    select 
    <include refid="Base_Column_List" />
    from t_prod_gift_lucky
    where key_id = #{keyId}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="TProdGiftLucky">
    delete from t_prod_gift_lucky
    where key_id = #{keyId}
  </delete>
  <insert id="insert" parameterType="TProdGiftLucky">
    insert into t_prod_gift_lucky (key_id, match_rule, gift_id, count_min, count_max, luck_code, 
      count_type, count_type_value, luck_pool_type)
    values (#{keyId}, #{matchRule}, #{giftId}, #{countMin}, #{countMax}, #{luckCode}, 
      #{countType}, #{countTypeValue}, #{luckPoolType})
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="TProdGiftLucky">
    update t_prod_gift_lucky
    <set>
      <if test="matchRule != null">
        match_rule = #{matchRule},
      </if>
      <if test="giftId != null">
        gift_id = #{giftId},
      </if>
      <if test="countMin != null">
        count_min = #{countMin},
      </if>
      <if test="countMax != null">
        count_max = #{countMax},
      </if>
      <if test="luckCode != null">
        luck_code = #{luckCode},
      </if>
      <if test="countType != null">
        count_type = #{countType},
      </if>
      <if test="countTypeValue != null">
        count_type_value = #{countTypeValue},
      </if>
      <if test="luckPoolType != null">
        luck_pool_type = #{luckPoolType},
      </if>
    </set>
    where key_id = #{keyId}
  </update>
  <update id="updateByPrimaryKey" parameterType="TProdGiftLucky">
    update t_prod_gift_lucky
    set match_rule = #{matchRule},
      gift_id = #{giftId},
      count_min = #{countMin},
      count_max = #{countMax},
      luck_code = #{luckCode},
      count_type = #{countType},
      count_type_value = #{countTypeValue},
      luck_pool_type = #{luckPoolType}
    where key_id = #{keyId}
  </update>

  <select id="selectByGiftId" parameterType="TProdGiftLucky" resultType="TProdGiftLucky">
    select
    <include refid="Base_Column_List" />
    from t_prod_gift_lucky
    where gift_id = #{giftId}
  </select>
</mapper>