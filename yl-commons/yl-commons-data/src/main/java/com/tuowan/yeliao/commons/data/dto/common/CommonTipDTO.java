package com.tuowan.yeliao.commons.data.dto.common;

import java.util.Map;

public class CommonTipDTO {
    /** 奖励提示模板 */
    private String textTpl;
    /** 模板参数内容 */
    private Map<String, Object> params;

    /**
     * 建造方法1
     * @return
     */
    public static CommonTipDTO build1(String textTpl, Map<String, Object> params){
        CommonTipDTO dto = new CommonTipDTO();
        dto.setTextTpl(textTpl);
        dto.setParams(params);
        return dto;
    }

    public String getTextTpl() {
        return textTpl;
    }

    public void setTextTpl(String textTpl) {
        this.textTpl = textTpl;
    }

    public Map<String, Object> getParams() {
        return params;
    }

    public void setParams(Map<String, Object> params) {
        this.params = params;
    }
}
