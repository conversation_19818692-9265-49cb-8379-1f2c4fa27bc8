/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.commons.data.persistence.config;

import com.easyooo.framework.sharding.annotation.Table;
import com.tuowan.yeliao.commons.data.entity.config.TTaskConfig;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@Table(value = "T_TASK_CONFIG", schema = "YL_CONFIG")
public interface TTaskConfigMapper {
    int deleteByPrimaryKey(TTaskConfig record);

    int insert(TTaskConfig record);

    TTaskConfig selectByPrimaryKey(TTaskConfig record);

    int updateByPrimaryKeySelective(TTaskConfig record);

    int updateByPrimaryKey(TTaskConfig record);

    List<TTaskConfig> selectAll();
}