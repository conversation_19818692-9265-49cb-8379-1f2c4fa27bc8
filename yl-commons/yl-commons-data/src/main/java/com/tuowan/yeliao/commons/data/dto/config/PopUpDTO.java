package com.tuowan.yeliao.commons.data.dto.config;

import com.tuowan.yeliao.commons.data.entity.config.TPopUp;

/**
 * 弹窗数据封装
 */
public class PopUpDTO {
    /**
     * 标题
     */
    private String title;
    /**
     * 内容
     */
    private String content;
    /**
     * 按钮显示内容
     */
    private String touchName;

    public static PopUpDTO build1(TPopUp popUp){
        PopUpDTO dto = new PopUpDTO();
        dto.setTitle(popUp.getTitle());
        dto.setContent(popUp.getContent());
        dto.setTouchName("我知道了");
        return dto;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getTouchName() {
        return touchName;
    }

    public void setTouchName(String touchName) {
        this.touchName = touchName;
    }
}
