/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.commons.data.persistence.config;

import com.easyooo.framework.cache.annotations.GroupStrategy;
import com.easyooo.framework.sharding.annotation.Table;
import com.tuowan.yeliao.commons.data.entity.config.TLevel;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@Table(value = "T_LEVEL", schema = "YL_CONFIG")
public interface TLevelMapper {
    int deleteByPrimaryKey(TLevel record);

    int insert(TLevel record);

    TLevel selectByPrimaryKey(TLevel record);

    int updateByPrimaryKeySelective(TLevel record);

    int updateByPrimaryKey(TLevel record);

    @GroupStrategy
    List<TLevel> selectByLevelType(TLevel record);
}