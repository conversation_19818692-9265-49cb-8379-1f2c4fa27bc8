<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tuowan.yeliao.commons.data.persistence.user.UChatMasterChangeMapper">
  <sql id="Base_Column_List">
    log_id, busi_code, user_id,property_name, old_value, new_value, remark, creator, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="UChatMasterChange" resultType="UChatMasterChange">
    select 
    <include refid="Base_Column_List" />
    from u_chat_master_change
    where log_id = #{logId}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="UChatMasterChange">
    delete from u_chat_master_change
    where log_id = #{logId}
  </delete>
  <insert id="insert" parameterType="UChatMasterChange">
    insert into u_chat_master_change (log_id, busi_code, user_id,property_name,
     old_value, new_value, remark, creator, create_time)
    values (#{logId}, #{busiCode}, #{userId},#{propertyName}, #{oldValue}, #{newValue}, #{remark}, #{creator},
      #{createTime})
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="UChatMasterChange">
    update u_chat_master_change
    <set>
      <if test="busiCode != null">
        busi_code = #{busiCode},
      </if>
      <if test="userId != null">
        user_id = #{userId},
      </if>
      <if test="propertyName != null">
        property_name = #{propertyName},
      </if>
      <if test="oldValue != null">
        old_value = #{oldValue},
      </if>
      <if test="newValue != null">
        new_value = #{newValue},
      </if>
      <if test="remark != null">
        remark = #{remark},
      </if>
      <if test="creator != null">
        creator = #{creator},
      </if>
      <if test="createTime != null">
        create_time = #{createTime},
      </if>
    </set>
    where log_id = #{logId}
  </update>
  <update id="updateByPrimaryKey" parameterType="UChatMasterChange">
    update u_chat_master_change
    set busi_code = #{busiCode},
      user_id = #{userId},
      property_name = #{propertyName},
      old_value = #{oldValue},
      new_value = #{newValue},
      remark = #{remark},
      creator = #{creator},
      create_time = #{createTime}
    where log_id = #{logId}
  </update>
</mapper>