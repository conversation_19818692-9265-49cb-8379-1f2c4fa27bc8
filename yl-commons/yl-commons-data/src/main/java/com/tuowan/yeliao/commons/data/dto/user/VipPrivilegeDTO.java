package com.tuowan.yeliao.commons.data.dto.user;

/**
 * <AUTHOR>
 * @date 2022/2/3 9:59
 */
public class VipPrivilegeDTO {
    /**
     * 特权标识
     */
    private String type;
    /**
     * 特权标题
     */
    private String title;
    /**
     * 特权图标
     */
    private String icon;
    /**
     * 特权描述DTO
     */
    private VipRemarkDTO vipRemarkDTO;

    public VipPrivilegeDTO() {
    }

    public VipPrivilegeDTO(String type, String title, String icon, VipRemarkDTO vipRemarkDTO) {
        this.type = type;
        this.title = title;
        this.icon = icon;
        this.vipRemarkDTO = vipRemarkDTO;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public VipRemarkDTO getVipRemarkDTO() {
        return vipRemarkDTO;
    }

    public void setVipRemarkDTO(VipRemarkDTO vipRemarkDTO) {
        this.vipRemarkDTO = vipRemarkDTO;
    }
}
