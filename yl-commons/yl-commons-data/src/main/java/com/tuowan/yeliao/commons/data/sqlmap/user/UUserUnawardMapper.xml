<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tuowan.yeliao.commons.data.persistence.user.UUserUnawardMapper">
  <sql id="Base_Column_List">
    unaward_id, tid, grant_type, grant_remark, award_config_code, user_id, nickname, 
    head_pic, ranking, beans, content, exp_time, creator, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="UUserUnaward" resultType="UUserUnaward">
    select 
    <include refid="Base_Column_List" />
    from u_user_unaward
    where unaward_id = #{unawardId}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="UUserUnaward">
    delete from u_user_unaward
    where unaward_id = #{unawardId}
  </delete>
  <insert id="insert" parameterType="UUserUnaward" useGeneratedKeys="true" keyProperty="unawardId">
    insert into u_user_unaward (unaward_id, tid, grant_type, grant_remark, award_config_code, user_id, 
      nickname, head_pic, ranking, beans, content, exp_time, creator, 
      create_time)
    values (#{unawardId}, #{tid}, #{grantType}, #{grantRemark}, #{awardConfigCode}, #{userId}, 
      #{nickname}, #{headPic}, #{ranking}, #{beans}, #{content}, #{expTime}, #{creator}, 
      #{createTime})
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="UUserUnaward">
    update u_user_unaward
    <set>
      <if test="tid != null">
        tid = #{tid},
      </if>
      <if test="grantType != null">
        grant_type = #{grantType},
      </if>
      <if test="grantRemark != null">
        grant_remark = #{grantRemark},
      </if>
      <if test="awardConfigCode != null">
        award_config_code = #{awardConfigCode},
      </if>
      <if test="userId != null">
        user_id = #{userId},
      </if>
      <if test="nickname != null">
        nickname = #{nickname},
      </if>
      <if test="headPic != null">
        head_pic = #{headPic},
      </if>
      <if test="ranking != null">
        ranking = #{ranking},
      </if>
      <if test="beans != null">
        beans = #{beans},
      </if>
      <if test="content != null">
        content = #{content},
      </if>
      <if test="expTime != null">
        exp_time = #{expTime},
      </if>
      <if test="creator != null">
        creator = #{creator},
      </if>
      <if test="createTime != null">
        create_time = #{createTime},
      </if>
    </set>
    where unaward_id = #{unawardId}
  </update>
  <update id="updateByPrimaryKey" parameterType="UUserUnaward">
    update u_user_unaward
    set tid = #{tid},
      grant_type = #{grantType},
      grant_remark = #{grantRemark},
      award_config_code = #{awardConfigCode},
      user_id = #{userId},
      nickname = #{nickname},
      head_pic = #{headPic},
      ranking = #{ranking},
      beans = #{beans},
      content = #{content},
      exp_time = #{expTime},
      creator = #{creator},
      create_time = #{createTime}
    where unaward_id = #{unawardId}
  </update>

  <select id="selectByPrimaryKeyToLock" parameterType="UUserUnaward" resultType="UUserUnaward">
    select
    <include refid="Base_Column_List" />
    from u_user_unaward
    where unaward_id = #{unawardId}
    for update
  </select>
</mapper>