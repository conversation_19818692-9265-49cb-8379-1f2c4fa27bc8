package com.tuowan.yeliao.commons.data.dto.user;

import com.tuowan.yeliao.commons.core.enums.general.BoolType;
import com.tuowan.yeliao.commons.data.enums.user.ChatMasterLevel;

/**
 * <AUTHOR>
 * @date 2021/4/26 11:55
 */
public class MsgFeeConfigDTO {

    /** 费用，单位：金币*/
    private Integer fee;
    /** 描述 */
    private String desc;
    /** 是否需要真人认证 */
    private BoolType realPerson;
    /** 是否需要实名认证 */
    private BoolType realName;
    /** 聊主等级 */
    private ChatMasterLevel minMasterLevel;
    /** 是否默认 */
    private Boolean isDef;


    public MsgFeeConfigDTO(Integer fee, String desc, BoolType realPerson, BoolType realName, ChatMasterLevel minMasterLevel, Boolean isDef) {
        this.fee = fee;
        this.desc = desc;
        this.realPerson = realPerson;
        this.realName = realName;
        this.minMasterLevel = minMasterLevel;
        this.isDef = isDef;
    }

    public Integer getFee() {
        return fee;
    }

    public void setFee(Integer fee) {
        this.fee = fee;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public BoolType getRealPerson() {
        return realPerson;
    }

    public void setRealPerson(BoolType realPerson) {
        this.realPerson = realPerson;
    }

    public BoolType getRealName() {
        return realName;
    }

    public void setRealName(BoolType realName) {
        this.realName = realName;
    }

    public ChatMasterLevel getMinMasterLevel() {
        return minMasterLevel;
    }

    public void setMinMasterLevel(ChatMasterLevel minMasterLevel) {
        this.minMasterLevel = minMasterLevel;
    }

    public Boolean getDef() {
        return isDef;
    }

    public void setDef(Boolean def) {
        isDef = def;
    }
}
