<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tuowan.yeliao.commons.data.persistence.user.UUserPortrayMapper">
  <sql id="Base_Column_List">
    user_id, age, salary, emotion, goal, intention, target_age, target_area, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="UUserPortray" resultType="UUserPortray">
    select 
    <include refid="Base_Column_List" />
    from u_user_portray
    where user_id = #{userId}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="UUserPortray">
    delete from u_user_portray
    where user_id = #{userId}
  </delete>
  <insert id="insert" parameterType="UUserPortray">
    insert into u_user_portray (user_id, age, salary, emotion, goal, intention, target_age, 
      target_area, create_time)
    values (#{userId}, #{age}, #{salary}, #{emotion}, #{goal}, #{intention}, #{targetAge}, 
      #{targetArea}, #{createTime})
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="UUserPortray">
    update u_user_portray
    <set>
      <if test="age != null">
        age = #{age},
      </if>
      <if test="salary != null">
        salary = #{salary},
      </if>
      <if test="emotion != null">
        emotion = #{emotion},
      </if>
      <if test="goal != null">
        goal = #{goal},
      </if>
      <if test="intention != null">
        intention = #{intention},
      </if>
      <if test="targetAge != null">
        target_age = #{targetAge},
      </if>
      <if test="targetArea != null">
        target_area = #{targetArea},
      </if>
      <if test="createTime != null">
        create_time = #{createTime},
      </if>
    </set>
    where user_id = #{userId}
  </update>
  <update id="updateByPrimaryKey" parameterType="UUserPortray">
    update u_user_portray
    set age = #{age},
      salary = #{salary},
      emotion = #{emotion},
      goal = #{goal},
      intention = #{intention},
      target_age = #{targetAge},
      target_area = #{targetArea},
      create_time = #{createTime}
    where user_id = #{userId}
  </update>
</mapper>