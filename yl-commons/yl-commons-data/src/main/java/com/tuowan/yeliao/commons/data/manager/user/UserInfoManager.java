package com.tuowan.yeliao.commons.data.manager.user;

import com.alibaba.nacos.common.utils.Pair;
import com.easyooo.framework.common.extend.SimpleMap;
import com.easyooo.framework.common.util.*;
import com.tuowan.yeliao.commons.config.enums.MiniLockType;
import com.tuowan.yeliao.commons.config.lock.MiniLockTemplate;
import com.tuowan.yeliao.commons.config.redis.RedisKey;
import com.tuowan.yeliao.commons.config.redis.template.BusiRedisTemplate;
import com.tuowan.yeliao.commons.config.utils.CallbackAfterTransactionUtil;
import com.tuowan.yeliao.commons.core.constant.GlobalConstant;
import com.tuowan.yeliao.commons.core.enums.general.BoolType;
import com.tuowan.yeliao.commons.core.enums.general.ClientType;
import com.tuowan.yeliao.commons.core.enums.general.PackageType;
import com.tuowan.yeliao.commons.core.enums.general.user.UserType;
import com.tuowan.yeliao.commons.core.enums.redis.BusiKeyDefine;
import com.tuowan.yeliao.commons.core.exception.BusiException;
import com.tuowan.yeliao.commons.data.dto.user.OnlineDTO;
import com.tuowan.yeliao.commons.data.dto.user.UserCoverDTO;
import com.tuowan.yeliao.commons.data.entity.config.*;
import com.tuowan.yeliao.commons.data.entity.user.*;
import com.tuowan.yeliao.commons.data.enums.config.*;
import com.tuowan.yeliao.commons.data.enums.general.AuditStatus;
import com.tuowan.yeliao.commons.data.enums.general.FixedAward;
import com.tuowan.yeliao.commons.data.enums.general.Status;
import com.tuowan.yeliao.commons.data.enums.user.*;
import com.tuowan.yeliao.commons.data.manager.config.CommonTextManager;
import com.tuowan.yeliao.commons.data.persistence.config.*;
import com.tuowan.yeliao.commons.data.persistence.user.*;
import com.tuowan.yeliao.commons.data.support.config.impl.SettingsConfig;
import com.tuowan.yeliao.commons.data.utils.BusiUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户信息工具
 *
 * <AUTHOR>
 * @date 2020/7/1 17:09
 */
@Component
public class UserInfoManager {

    private Logger LOG = LoggerFactory.getLogger(getClass());

    @Autowired
    private UUserExtMapper uUserExtMapper;
    @Autowired
    private UUserLevelMapper userLevelMapper;
    @Autowired
    private UUserAnonymousMapper userAnonymousMapper;
    @Autowired
    private UUserBasicMapper uUserBasicMapper;
    @Autowired
    private UUserMoreMapper uUserMoreMapper;
    @Autowired
    private UUserCoverMapper uUserCoverMapper;
    @Autowired
    private TJobMapper tJobMapper;
    @Autowired
    private TProfessionMapper tProfessionMapper;
    @Autowired
    private TCityMapper tCityMapper;
    @Autowired
    private TNickNameMapper tNickNameMapper;
    @Autowired
    private BusiRedisTemplate busiRedisTemplate;
    @Autowired
    private TSchoolMapper schoolMapper;
    @Autowired
    private TConstellationMapper tConstellationMapper;
    @Autowired
    private UUserLocationMapper uUserLocationMapper;
    @Autowired
    private UUserLocationHisMapper uUserLocationHisMapper;
    @Autowired
    private UIdentityAuthMapper uIdentityAuthMapper;
    @Autowired
    private UUserOnlineMapper uUserOnlineMapper;
    @Autowired
    private CommonTextManager commonTextManager;
    @Autowired
    private UserKeyMarkManager userKeyMarkManager;
    @Autowired
    private TIdCardBlackListMapper idCardBlackListMapper;
    @Autowired
    private UUserWealthMapper userWealthMapper;
    @Autowired
    private MiniLockTemplate miniLockTemplate;
    @Autowired
    private TStoreAuditShieldMapper storeAuditShieldMapper;
    @Autowired
    private UUserMediaMapper userMediaMapper;
    @Autowired
    private UUserMediaApplyMapper userMediaApplyMapper;
    @Autowired
    private UUserUnlockMediaMapper userUnlockMediaMapper;

    /**
     * 计算男用户等级
     * 备注：
     * 1、MS 注册时间大于3天 并且充值总金额大于300
     * 2、MA 注册时间小于3天
     * 3、MB 注册时间大于3天 并且充值金额小于等于300
     */
    public ChatMasterLevel calculateMaleLevel(UUserBasic basic){
        // 优先判断是不是 MA
        if(DateUtils.getDiffDays(basic.getCreateTime()) < 3){
            return ChatMasterLevel.MA;
        }
        UUserWealth userWealth = userWealthMapper.selectByPrimaryKey(new UUserWealth(basic.getUserId()));
        long totalRecharge = Objects.isNull(userWealth) ? 0L : userWealth.getTotalRecharge();
        if(totalRecharge > 30000){
            return ChatMasterLevel.MS;
        }
        return ChatMasterLevel.MB;
    }

    /**
     * 判断是否是有效邀请人麾下的女用户
     */
    public boolean checkFemaleBelongValidInvitor(Long userId){
        if(getUserBasic(userId).getSex().isMale()){
            return false;
        }
        UUserExt userExt = getUserExt(userId);
        if(Objects.isNull(userExt.getInviteUserId())){
            return false;
        }
        return getUserExt(userExt.getInviteUserId()).getValidInvitor() == BoolType.True;
    }

    /**
     * 保存用户解锁付费媒资记录
     */
    public void saveUserUnlockMedia(UUserUnlockMedia record){
        userUnlockMediaMapper.insert(record);
    }

    /**
     * 用户解锁新付费媒资了
     * 删除缓存
     */
    public void deleteUserUnlockChargeMediaCache(Long userId){
        CallbackAfterTransactionUtil.send(() -> {
            busiRedisTemplate.del(buildUserUnlockChargeMedia(userId));
        });
    }

    /**
     * 保存用户媒资审核数据
     */
    public void batchSaveUserMediaApply(List<UUserMediaApply> medias){
        userMediaApplyMapper.batchInsert(medias);
    }

    /**
     * 删除用户媒资审核数据
     */
    public void deleteUserMediaApply(Long userId){
        userMediaApplyMapper.deleteUserMedias(userId);
    }

    /**
     * 获取 主页封面、图片合集
     */
    public List<String> getHomePageCovers(Long userId){
        List<String> resultList = new LinkedList<>();
        // 常规封面放在第一个
        List<UUserMedia> userMedias1 = getUserMedias(userId, MediaType.CommonCover);
        if(ListUtils.isNotEmpty(userMedias1)){
            resultList.addAll(userMedias1.stream().map(UUserMedia::getMediaValue).collect(Collectors.toList()));
        }
        // 常规图片放在第二个
        List<UUserMedia> userMedias2 = getUserMedias(userId, MediaType.CommonPic);
        if(ListUtils.isNotEmpty(userMedias2)){
            resultList.addAll(userMedias2.stream().map(UUserMedia::getMediaValue).collect(Collectors.toList()));
        }
        return resultList;
    }

    /**
     * 获取用户收费照片合集
     */
    public List<String> getUserChargePics(Long userId){
        List<UUserMedia> userMedias = getUserMedias(userId, MediaType.ChargePic);
        if(ListUtils.isEmpty(userMedias)){
            return null;
        }
        return userMedias.stream().map(UUserMedia::getMediaValue).collect(Collectors.toList());
    }

    /**
     * 获取用户普通照片合集
     */
    public List<String> getUserPics(Long userId){
        List<UUserMedia> userMedias = getUserMedias(userId, MediaType.CommonPic);
        if(ListUtils.isEmpty(userMedias)){
            return null;
        }
        return userMedias.stream().map(UUserMedia::getMediaValue).collect(Collectors.toList());
    }

    /**
     * 获取用户封面信息
     */
    public String getUserCover(Long userId){
        List<UUserMedia> userMedias = getUserMedias(userId, MediaType.CommonCover);
        if(ListUtils.isEmpty(userMedias)){
            return null;
        }
        return userMedias.get(0).getMediaValue();
    }

    /**
     * 获取用户语音签名信息
     */
    public String getUserVoiceUrl(Long userId){
        List<UUserMedia> userMedias = getUserMedias(userId, MediaType.CommonVoice);
        if(ListUtils.isEmpty(userMedias)){
            return null;
        }
        return userMedias.get(0).getMediaValue();
    }

    /**
     * 获取用户视频信息
     * first 是视频封面
     * second 是视频地址
     */
    public Pair<String, String> getUserVideoInfo(Long userId){
        List<UUserMedia> userMedias = getUserMedias(userId, MediaType.CommonVideo);
        if(ListUtils.isEmpty(userMedias)){
            return Pair.with(null, null);
        }
        UUserMedia media = userMedias.get(0);
        // 视频封面存储在 ext扩展信息里面 （我们这里不去做判空校验 在插入的时候我们注意就行）
        SimpleMap extMap = JsonUtils.toSimpleMap(media.getMediaExt());
        return Pair.with(extMap.getString("videoCover"), media.getMediaValue());
    }

    /**
     * 获取用户媒资
     */
    public List<UUserMedia> getUserMedias(Long userId, MediaType type){
        return userMediaMapper.selectByUserIdAndType(new UUserMedia(userId, type));
    }

    /**
     * 获取用户媒资
     */
    public List<UUserMedia> getUserMedias(Long userId){
        return userMediaMapper.selectByUserId(userId);
    }

    /**
     * 获取用户媒资
     */
    public UUserMedia getUserMedia(Long recordId){
        return userMediaMapper.selectByPrimaryKey(new UUserMedia(recordId));
    }

    /**
     * 删除用户媒资
     */
    public void deleteUserMedia(UUserMedia media){
        userMediaMapper.deleteByPrimaryKey(media);
    }

    /**
     * 保存用户媒资信息
     */
    public void saveUserMedia(UUserMedia media){
        userMediaMapper.insert(media);
    }

    /**
     * 根据注册渠道、性别获取商店审核禁止
     */
    public TStoreAuditShield getTStoreAuditShield(String channelId, SexType sex){
        return storeAuditShieldMapper.selectByPrimaryKey(new TStoreAuditShield(channelId, sex));
    }

    /**
     * 邀请人邀请的女用户真人认证成功
     * 添加该邀请人的有效邀请人数
     */
    public void saveInvitorValidUserNum(Long invitorUserId){
        // 修改该用户邀请人的有效邀请人数
        miniLockTemplate.execute(MiniLockType.ValidInviteBind, invitorUserId, () -> {
            UUserExt invitorExt = getUserExt(invitorUserId);
            invitorExt.setTotalRpInvite(invitorExt.getTotalRpInvite() + 1);
            updateUserExt(invitorExt);
            return null;
        });
    }

    /**
     * 解绑邀请关系
     * 减少该邀请人的有效邀请人数
     */
    public void reduceInvitorValidUserNum(Long invitorUserId){
        // 修改该用户邀请人的有效邀请人数
        miniLockTemplate.execute(MiniLockType.ValidInviteBind, invitorUserId, () -> {
            UUserExt invitorExt = getUserExt(invitorUserId);
            invitorExt.setTotalRpInvite(Math.max(0, invitorExt.getTotalRpInvite() - 1));
            updateUserExt(invitorExt);
            return null;
        });
    }

    /**
     * 获取集团黑名单待统计数据
     */
    public List<UIdentityAuth> getGroupBlackListStatisticUsers(Integer offset, Integer limit){
        return uIdentityAuthMapper.queryGroupBlackListStatisticUser(offset, limit);
    }

    /**
     * 初始化用户心情匿名信息
     */
    public UUserAnonymous initMoodUserAnonymous(Long userId, SexType sexType){
        UUserAnonymous insert = new UUserAnonymous(userId);
        insert.setMoodNickname(getAliseName());
        insert.setMoodHeadPic(getAmHeadPic(sexType));
        insert.setCreateTime(new Date());
        userAnonymousMapper.insert(insert);
        return insert;
    }

    /**
     * 获取用户匿名信息
     */
    public UUserAnonymous getUserAnonymous(Long userId){
        UUserAnonymous uUserAnonymous = userAnonymousMapper.selectByPrimaryKey(new UUserAnonymous(userId));
        if(uUserAnonymous == null){
            UUserBasic userBasic = getUserBasic(userId);
            return initMoodUserAnonymous(userId, userBasic.getSex());
        }
        return uUserAnonymous;
    }

    /**
     * 修改用户匿名信息
     */
    public void updateUserAnonymous(UUserAnonymous update){
        userAnonymousMapper.updateByPrimaryKeySelective(update);
    }

    /**
     * 记录用户当日青少年弹窗已弹
     */
    public void recordUserPopTeenagers(Long userId){
        CallbackAfterTransactionUtil.send(() -> {
            busiRedisTemplate.set(buildUserPopTeenagers(userId), "T");
        });
    }

    /**
     * 判断用户当日青少年弹窗是否已弹
     */
    public boolean checkUserPopTeenagers(Long userId){
        BoolType boolType = SettingsConfig.getBoolType(SettingsType.PopTeenagersSwitch);
        if(BoolType.True != boolType){
            return false;
        }
        return !busiRedisTemplate.exists(buildUserPopTeenagers(userId));
    }

    /**
     * 随机获取用户别名
     */
    public String getAliseName(){
        int aInt = RandomUtils.getInt(GlobalConstant.USER_ALISE_NAME_A.length);
        String aStr = GlobalConstant.USER_ALISE_NAME_A[aInt];
        int bInt = RandomUtils.getInt(65, 91);
        char bStr = (char)bInt;
        int cInt = RandomUtils.getInt(10, 1000);
        return MsgUtils.format("{}{}-{}星人", aStr, bStr, cInt);
    }

    /**
     * 随机获取用户匿名头像
     */
    public String getAmHeadPic(SexType sexType){
        int i = RandomUtils.getInt(GlobalConstant.AM_HEAD_PIC_INDEX_ARRAY.length);
        return MsgUtils.format(SexType.Female == sexType ? GlobalConstant.FEMALE_AM_HEAD_PIC_FORMAT : GlobalConstant.MALE_AM_HEAD_PIC_FORMAT,
                GlobalConstant.AM_HEAD_PIC_INDEX_ARRAY[i]);
    }

    /**
     * 判断用户身份证是否在黑名单列表中
     *
     * @param idCard 是用户未加密的身份证
     */
    public boolean checkUserIdCardInBlackList(String idCard) {
        String idCardMd5 = BusiUtils.encrypt(idCard.toUpperCase());
        TIdCardBlackList tIdCardBlackList = idCardBlackListMapper.selectByPrimaryKey(new TIdCardBlackList(idCardMd5));
        if (Objects.nonNull(tIdCardBlackList) && Status.Enable == tIdCardBlackList.getStatus()) {
            return true;
        }
        return false;
    }

    /**
     * 判断用户是否有完善信息奖励
     * @return fasle 表示用户已经没有奖励了
     * @return true 表示用户还有奖励
     */
    public boolean checkUserPerfectInfoAward(Long userId, SexType sexType){
        List<UserBusiKeyMark> list2 = new ArrayList<>();
        list2.add(UserBusiKeyMark.UserFirstBaseInfo);
        if(SexType.Female == sexType){
            list2.add(UserBusiKeyMark.UserFirstLightInfo);
            list2.add(UserBusiKeyMark.FirstCoverMoreThree);
        }
        return !userKeyMarkManager.exists(userId, list2.toArray(new UserBusiKeyMark[0]));
    }

    /**
     * 判断用户是否有认证信息奖励
     * @return fasle 表示用户已经没有奖励了
     * @return true 表示用户还有奖励
     */
    public boolean checkUserAuthInfoAward(Long userId, SexType sexType){
        if(SexType.Female == sexType){
            return false;
        }
        List<UserBusiKeyMark> list1 = new ArrayList<>();
        list1.add(UserBusiKeyMark.UserFirstRealHead);
        return !userKeyMarkManager.exists(userId, list1.toArray(new UserBusiKeyMark[0]));
    }

    /**
     * 判断用户实名+完善信息是否都已完成
     * 根据奖励标识有判断
     * @return true 表示用户奖励已经领完(新人任务完成)
     * @return false 表示用户奖励还未领完(新人任务还未完成)
     */
    public boolean checkUserPerfectAll(Long userId, SexType sexType){
        boolean auth = checkUserAuthInfoAward(userId, sexType);
        boolean info = checkUserPerfectInfoAward(userId, sexType);
        return !auth && !info;
    }

    /**
     * 判断用户是否完成真人认证
     * @return Ture 代表完成
     * @return False 代表未完成
     */
    public BoolType checkUserFinishRealPerson(Long userId){
        UIdentityAuth userIdentityAuth = getUserIdentityAuth(userId);
        return BoolType.valueOf(Objects.nonNull(userIdentityAuth) && StringUtils.isNotEmpty(userIdentityAuth.getRealPeopleInfo()));
    }

    /**
     * 判断用户修改资料奖励是否领取完
     * @return True 还有奖励未领取
     * @return False 没有奖励了
     * 1. 上传真人头像
     * 2. 完善基本资料
     * 3. 上传封面图片
     */
    public BoolType checkHaveEditDataAward(Long userId){
        // 用户首次完善基本信息
        UUserKeyMark keyMark1 = userKeyMarkManager.getKeyMark(userId, UserBusiKeyMark.UserFirstBaseInfo);
        if(Objects.isNull(keyMark1)){
            return BoolType.True;
        }
        // 用户首次完善亮点信息
/*        UUserKeyMark keyMark2 = userKeyMarkManager.getKeyMark(userId, UserBusiKeyMark.UserFirstLightInfo);
        if(Objects.isNull(keyMark2)){
            return BoolType.True;
        }*/
        // 用户首次上传爱好标签>=3个
/*        UUserKeyMark keyMark3 = userKeyMarkManager.getKeyMark(userId, UserBusiKeyMark.UserFirstHobbyInfo);
        if(Objects.isNull(keyMark3)){
            return BoolType.True;
        }*/
        // 用户首次上传真人头像
        UUserKeyMark keyMark4 = userKeyMarkManager.getKeyMark(userId, UserBusiKeyMark.UserFirstUploadRealHead);
        if(Objects.isNull(keyMark4)){
            return BoolType.True;
        }
        // 初次封面上传数量>=3张
        UUserKeyMark keyMark5 = userKeyMarkManager.getKeyMark(userId, UserBusiKeyMark.FirstCoverMoreThree);
        if(Objects.isNull(keyMark5)){
            return BoolType.True;
        }

        return BoolType.False;
    }

    /**
     * 获取userExt信息
     */
    public List<Long> queryUserExts(Long min, Long max){
        List<UUserExt> exts = uUserExtMapper.queryUserExts(min, max);
        return exts.stream().map(UUserExt::getUserId).collect(Collectors.toList());
    }

    /**
     * 判断昵称是否存在（更新昵称时调用）
     * 备注：判断时排除自己
     * @return true 存在该昵称
     * @return false 不存在
     */
    public boolean nicknameIsExists(Long userId, String nickname){
        return uUserBasicMapper.existsForNicknameNotSelf(nickname, userId) > 0;
    }

    /**
     * 更新用户认证记录
     */
    public void updateIdentityAuth(UIdentityAuth update){
        uIdentityAuthMapper.updateByPrimaryKeySelective(update);
    }

    /**
     * 更新用户认证记录所有信息
     */
    public void updateIdentityAuthAll(UIdentityAuth update){
        uIdentityAuthMapper.updateByPrimaryKey(update);
    }

    /**
     * 判断用户基本信息是否完善
     */
    public boolean checkUserInfoPerfect(Object obj, String feildStr){
        try {
            Class<?> clazz = obj.getClass();
            Field[] declaredFields = clazz.getDeclaredFields();
            for(Field item : declaredFields){
                item.setAccessible(true);
                if(feildStr.contains(item.getName()) && Objects.isNull(item.get(obj))){
                    return false;
                }
            }
            return true;
        }catch (Exception e){
            LOG.error("UserInfoManager-checkUserInfoPerfect-error reason:", e);
            return false;
        }
    }

    /**
     * 根据用户性别和城市生成默认签名
     */
    public String buildDefaultSign(SexType sexType) {
        String socialWish = commonTextManager.getRandomText(TextUseType.DefaultMySignNotCity, sexType);
        if(StringUtils.isNotEmpty(socialWish)){
            return socialWish;
        }
        return GlobalConstant.SYSTEM_MY_SIGN;
    }

    /**
     * 判断身份证号是否被绑定
     *
     * @param identityNo
     */
    public boolean checkIdCardExists(String identityNo, PackageType packageType) {
        return uIdentityAuthMapper.countByIdentityNo(identityNo, packageType) > 0;
    }

    /**
     * 多用户ID查询 返回<userId,UUserBasic>
     *
     * @param userIds
     * @return
     */
    public Map<Long, UUserBasic> getUserBasicMap(Set<Long> userIds) {
        Map<Long, UUserBasic> userMap = new HashMap<>();
        for (Long userId : userIds) {
            userMap.put(userId, getUserBasic(userId));
        }
        return userMap;
    }

    /**
     * 查询用户UUserBasic信息
     *
     * @param userId
     * @return
     */
    public UUserBasic getUserBasic(Long userId) {
        return uUserBasicMapper.selectByPrimaryKey(new UUserBasic(userId));
    }

    /**
     * 查询用户UUserMore信息
     *
     * @param userId
     * @return
     */
    public UUserMore getUserMore(Long userId) {
        return uUserMoreMapper.selectByPrimaryKey(new UUserMore(userId));
    }

    /**
     * 查询用户UUserExt信息
     *
     * @param userId
     * @return
     */
    public UUserExt getUserExt(Long userId) {
        if (userId == null) {
            return null;
        }
        return uUserExtMapper.selectByPrimaryKey(new UUserExt(userId));
    }

    /**
     * 判断用户是否为VIP
     *
     * 如果上下文已经有UUserBasic对象，请直接使用BusiUtils.isVip(user)，避免重复查询缓存或数据库
     *
     * @param userId
     * @return
     */
    public boolean isVip(Long userId) {
        UUserBasic user = getUserBasic(userId);
        return BusiUtils.isVip(user);
    }

    /**
     * 是否实名认证
     *
     * @param userId
     * @return
     */
    public BoolType isRealName(Long userId) {
        UUserBasic userBasic = getUserBasic(userId);
        if (userBasic == null || userBasic.getRealName() == null) {
            return BoolType.False;
        }
        return userBasic.getRealName();
    }

    /**
     * 用户封禁校验
     *
     * @param basic
     */
    public void checkUserHasBan(UUserBasic basic) {
        // 封禁提示
        if (null != basic && Status.Disable == basic.getStatus()) {
            throw new BusiException(GlobalConstant.BAN_OTHER_USER_MSG);
        }
    }

    /**
     * 获取用户定位信息
     *
     * @param userId
     * @return
     */
    public UUserLocation getUserLocation(Long userId) {
        return uUserLocationMapper.selectByPrimaryKey(new UUserLocation(userId));
    }

    /**
     * 保存用户等级信息
     */
    public void saveUserLevel(UUserLevel record) {
        userLevelMapper.insert(record);
    }

    /**
     * 获取用户等级信息
     */
    public UUserLevel getUserLevel(Long userId) {
        return userLevelMapper.selectByPrimaryKey(new UUserLevel(userId));
    }

    /**
     * 保存用户基本信息
     *
     * @param userBasic
     */
    public void saveUserBasic(UUserBasic userBasic) {
        uUserBasicMapper.insert(userBasic);
    }

    /**
     * 保存用户在线状态
     *
     * @param userOnline
     */
    public void saveUserOnline(UUserOnline userOnline) {
        uUserOnlineMapper.insert(userOnline);
    }

    /**
     * 更新用户基本信息
     *
     * @param userBasic
     */
    public void updateUserBasic(UUserBasic userBasic) {
        uUserBasicMapper.updateByPrimaryKeySelective(userBasic);
    }

    /**
     * 更新用户融云token
     */
    public void updateUserImToken(Long userId, String imToken) {
        UUserMore update = new UUserMore(userId);
        update.setImToken(imToken);
        uUserMoreMapper.updateByPrimaryKeySelective(update);
    }

    /**
     * 保存用户扩展信息
     *
     * @param userExt
     */
    public void saveUserExt(UUserExt userExt) {
        uUserExtMapper.insert(userExt);
    }

    /**
     * 更新用户扩展信息
     *
     * @param userExt
     */
    public void updateUserExt(UUserExt userExt) {
        uUserExtMapper.updateByPrimaryKeySelective(userExt);
    }

    /**
     * 更新用户扩展信息
     */
    public void updateUserExtAll(UUserExt userExt) {
        uUserExtMapper.updateByPrimaryKey(userExt);
    }

    /**
     * 保存用户更多信息
     *
     * @param userMore
     */
    public void saveUserMore(UUserMore userMore) {
        uUserMoreMapper.insert(userMore);
    }

    /**
     * 更新用户更多信息
     *
     * @param userMore
     */
    public void updateUserMore(UUserMore userMore) {
        uUserMoreMapper.updateByPrimaryKeySelective(userMore);
    }


    /**
     * 是否真人认证
     *
     * @param userId
     * @return
     */
    public boolean isRealPerson(Long userId) {
        UIdentityAuth auth = uIdentityAuthMapper.selectByPrimaryKey(new UIdentityAuth(userId));
        if (null == auth || StringUtils.isEmpty(auth.getRealPeopleInfo())) {
            return false;
        }
        return true;
    }


    /**
     * 更新用户封面相册
     *
     * @param userId
     * @param covers
     */
    public void saveCoverPic(Long userId, List<String> covers) {
        UUserCover select = new UUserCover();
        select.setUserId(userId);
        List<UUserCover> uUserCovers = new ArrayList<>(); // uUserCoverMapper.selectByUserId(select);
        for(UUserCover item : uUserCovers){
            uUserCoverMapper.deleteByPrimaryKey(item);
        }
        if(ListUtils.isEmpty(covers)){
            return;
        }
        int order = 1;
        Date now = new Date();
        for(String item : covers){
            UUserCover cover = new UUserCover();
            cover.setCoverIdx(order);
            cover.setCoverPic(item);
            cover.setUserId(userId);
            cover.setIsHide(BoolType.False);
            cover.setCreateTime(now);
            uUserCoverMapper.insert(cover);
            order ++;
        }
    }

    /**
     * 删除用户封面信息
     * 备注：删除用户封面信息时 需传入对应userId 删除对应的分组缓存
     */
    public void deleteUserCover(UUserCover userCover){
        uUserCoverMapper.deleteByPrimaryKey(userCover);
    }

    /**
     * 删除用户basic信息
     */
    public void deleteUserBasic(Long userId){
        uUserBasicMapper.deleteByPrimaryKey(new UUserBasic(userId));
    }

    /**
     * 删除用户ext信息
     */
    public void deleteUserExt(Long userId){
        uUserExtMapper.deleteByPrimaryKey(new UUserExt(userId));
    }

    /**
     * 删除用户more信息
     */
    public void deleteUserMore(Long userId){
        uUserMoreMapper.deleteByPrimaryKey(new UUserMore(userId));
    }

    /**
     * 删除用户location信息
     */
    public void deleteUserLocation(Long userId){
        uUserLocationMapper.deleteByPrimaryKey(new UUserLocation(userId));
    }

    /**
     * 删除用户auth信息
     */
    public void deleteUserAuth(Long userId){
        uIdentityAuthMapper.deleteByPrimaryKey(new UIdentityAuth(userId));
    }

    /**
     * 删除用户level信息
     */
    public void deleteUserLevel(Long userId){
        userLevelMapper.deleteByPrimaryKey(new UUserLevel(userId));
    }

    /**
     * 删除用户online信息
     */
    public void deleteUserOnline(Long userId){
        uUserOnlineMapper.deleteByPrimaryKey(new UUserOnline(userId));
    }

    /**
     * 删除用户wealth信息
     */
    public void deleteUserWealth(Long userId){
        userWealthMapper.deleteByPrimaryKey(new UUserWealth(userId));
    }

    /**
     * 根据professionId获取职业信息
     *
     * @param professionId
     * @return
     */
    public TProfession getProfession(Integer professionId) {
        return tProfessionMapper.selectByPrimaryKey(new TProfession(professionId));
    }

    /**
     * 获取2.0.0职业信息集合
     *
     * @return
     */
    public List<TProfession> getProfessionList() {
        return tProfessionMapper.selectAll();
    }


    /**
     * 获取城市信息集合
     *
     * @return
     */
    public List<TCity> getCityList() {
        List<TCity> cityList = tCityMapper.selectAll();
        cityList = cityList.stream().sorted(Comparator.comparing(TCity::getProvinceOrderNum).thenComparing(TCity::getCityOrderNum)).collect(Collectors.toList());
        return cityList;
    }

    /**
     * 获取某个城市信息
     *
     * @param cityId
     * @return
     */
    public TCity getCityById(Integer cityId) {
        if (Objects.isNull(cityId)) {
            return null;
        }
        return tCityMapper.selectByPrimaryKey(new TCity(cityId));
    }

    /**
     * 获取用户第一张封面
     *
     * @param userId
     * @return
     */
    public String getFirstCoverPic(Long userId) {
        return getUserCover(userId);
    }

    /**
     * 获取编辑时候的用户封面相册，已排序
     *
     * @param userId
     * @param isViewSelf 是否为查看自己的封面
     * @return
     */
    public List<UserCoverDTO> getBasicInfoCoverPic(Long userId, boolean isViewSelf, Integer count) {
        UUserCover cover = new UUserCover();
        cover.setUserId(userId);
        List<UUserCover> picList = new ArrayList<>(); //uUserCoverMapper.selectByUserId(cover);
        if (ListUtils.isEmpty(picList)) {
            return new ArrayList<>();
        }
        picList.sort(Comparator.comparing(UUserCover::getCoverIdx));
        List<UserCoverDTO> covers = new ArrayList<>();
        for (UUserCover pic : picList) {
            if (isViewSelf || BoolType.False == pic.getIsHide()) {
                covers.add(new UserCoverDTO(pic));
                if (count != -1 && covers.size() == count) {
                    break;
                }
            }
        }
        return covers;
    }

    /**
     * 获取用户封面相册，已排序
     *
     * @param userId
     * @return
     */
    public List<String> getStaticCoverPic(Long userId) {
        return getStaticCoverPic(userId, true, -1);
    }


    /**
     * 获取用户封面相册，已排序
     *
     * @param userId
     * @param isViewSelf 是否为查看自己的封面
     * @return
     */
    public List<String> getStaticCoverPic(Long userId, boolean isViewSelf, Integer count) {
        List<UserCoverDTO> covers = getBasicInfoCoverPic(userId, isViewSelf, count);
        return covers.stream().map(UserCoverDTO::getCoverPic).collect(Collectors.toList());
    }

    /**
     * 更新用户定位信息
     *
     * @param location
     */
    public void saveOrUpdateUserLocation(UUserLocation location) {
        UUserLocation exist = getUserLocation(location.getUserId());
        if (null == exist) {
            location.setCreateTime(new Date());
            uUserLocationMapper.insert(location);
            return;
        }
        String oldCity = StringUtils.isNotEmpty(exist.getCity()) ? exist.getCity() : "";
        String newCity = StringUtils.isNotEmpty(location.getCity()) ? location.getCity() : "";
        uUserLocationMapper.updateByPrimaryKeySelective(location);
        if (!oldCity.equals(newCity)) {
            // 前后城市不同插入位置变动记录
            uUserLocationHisMapper.insert(new UUserLocationHis(location));
        }
    }

    /**
     * 获取用户实名认证状态
     *
     * @param userId
     * @return
     */
    public boolean hasRealName(Long userId) {
        UUserBasic userBasic = getUserBasic(userId);
        return userBasic.getRealName().boolValue();
    }

    /**
     * 获取实名认证信息
     *
     * @param userId
     * @return
     */
    public UIdentityAuth getUserIdentityAuth(Long userId) {
        return uIdentityAuthMapper.selectByPrimaryKey(new UIdentityAuth(userId));
    }

    /**
     * 判断支付宝是否被其他账号绑定
     *
     * @param alipayUserId
     */
    public boolean checkAliapyExists(String alipayUserId, PackageType packageType) {
        return uIdentityAuthMapper.countByAlipayUserId(alipayUserId, packageType) > 0;
    }

    /**
     * 保存。修改 用户实名认证信息
     */
    public void saveOrUpdateUserIdentityAuth(UIdentityAuth auth) {
        UIdentityAuth uIdentityAuth = uIdentityAuthMapper.selectByPrimaryKey(auth);
        if(uIdentityAuth == null){
            uIdentityAuthMapper.insert(auth);
        }else{
            uIdentityAuthMapper.updateByPrimaryKeySelective(auth);
        }
    }

    /**
     * 保存。修改（全部信息） 用户实名认证信息
     */
    public void saveOrUpdateAllUserIdentityAuth(UIdentityAuth auth) {
        UIdentityAuth uIdentityAuth = uIdentityAuthMapper.selectByPrimaryKey(auth);
        if(uIdentityAuth == null){
            uIdentityAuthMapper.insert(auth);
        }else{
            uIdentityAuthMapper.updateByPrimaryKey(auth);
        }
    }

    /**
     * 获取随机昵称
     */
    public String getRandomNickName(Long userNo, SexType sexType){
        List<String> randomNickName = getRandomNickName(sexType, 1);
        if(ListUtils.isNotEmpty(randomNickName)){
            return randomNickName.get(0);
        }
        String nickname = MsgUtils.format("用户@{}", userNo);
        return StringUtils.substring(nickname, 0, 8);
    }

    /**
     * 获取指定数量的随机昵称
     *
     * @param sexType
     * @param count
     * @return
     */
    public List<String> getRandomNickName(SexType sexType, Integer count) {
        if (sexType == null) {
            int random = RandomUtils.getInt(2);
            sexType = random == 0 ? SexType.Male : SexType.Female;
        }
        RedisKey redisKey = RedisKey.create(BusiKeyDefine.SystemNickName, sexType);
        List<String> names = busiRedisTemplate.srandmember(redisKey, count);
        if (ListUtils.isNotEmpty(names)) {
            return names;
        }
        List<TNickName> tNickNames = tNickNameMapper.selectBySexType(new TNickName(sexType));
        names = tNickNames.stream().map(TNickName::getNickname).collect(Collectors.toList());
        busiRedisTemplate.sadd(redisKey, names.toArray(new String[0]));
        Collections.shuffle(names);
        return ListUtils.subList(names, 0, count);
    }

    /**
     * 是否系统默认昵称
     *
     * @return
     */
    public boolean isDefaultNickName(String nickname, SexType sexType) {
        RedisKey redisKey = RedisKey.create(BusiKeyDefine.SystemNickName, sexType);
        if (busiRedisTemplate.exists(redisKey)) {
            return busiRedisTemplate.sismember(redisKey, nickname);
        }
        int count = tNickNameMapper.countByNickname(nickname);
        return count > 0;
    }

    /**
     * 更新客户端上次打开的一些信息
     *
     * @param userId
     * @param clientType
     * @param clientVersion
     */
    public Long updateLastOpenTime(Long userId, String channelId, ClientType clientType, String clientVersion, AppVersionType versionType,
                                   String deviceModel, String deviceOs) {
        UUserExt existed = uUserExtMapper.selectByPrimaryKey(new UUserExt(userId));
        UUserExt userExt = new UUserExt();
        userExt.setUserId(userId);
        userExt.setLastChannelId(channelId);
        userExt.setLastOpenTime(new Date());
        userExt.setLastOpenVersion(clientVersion);
        userExt.setLastClientType(clientType);
        if (versionType == null) {
            userExt.setLastVersionType(AppVersionType.Release);
        } else {
            userExt.setLastVersionType(versionType);
        }
        userExt.setLastDeviceModel(deviceModel);
        userExt.setLastDeviceOs(deviceOs);
        uUserExtMapper.updateByPrimaryKeySelective(userExt);
        if (existed.getLastOpenTime() != null) {
            return DateUtils.getDiffSeconds(existed.getLastOpenTime());
        }
        return 0L;
    }

    /**
     * 获取某个学校
     *
     * @param schoolId
     * @return
     */
    public TSchool getConfigSchool(Integer schoolId) {
        return schoolMapper.selectByPrimaryKey(new TSchool(schoolId));
    }

    /**
     * 模糊查询学校列表
     *
     * @param schoolName
     * @return
     */
    public List<TSchool> getSchoolList(String schoolName) {
        return schoolMapper.selectByName(new TSchool(schoolName));
    }


    /**
     * 获取星座
     *
     * @param constellationId
     * @return
     */
    public TConstellation getConfigTConstellation(Integer constellationId) {
        return tConstellationMapper.selectByPrimaryKey(new TConstellation(constellationId));
    }

    /**
     * 获取真实的more 收入字段信息
     *
     * @param more
     * @return
     */
    public IncomeType getMoreIncome(UUserMore more) {
        if (null != more && null != more.getIncome()) {
            return more.getIncome();
        }
        return null;
    }

    public MarriageStatus getMoreMarriageStatus(UUserMore more) {
        if (null != more && null != more.getMarriageStatus()) {
            return more.getMarriageStatus();
        }
        return null;
    }


    /**
     * 将用户更新为固定位置
     *
     * @param userId
     * @param city
     */
    public void updateFixedLocation(Long userId, TCity city) {
        UUserLocation location = getUserLocation(userId);
        if (location == null) {
            throw new BusiException("用户位置不存在");
        }
        location.setProvince(city.getProvince());
        location.setCity(city.getCity());
        location.setDistrict(null);
        location.setLat(city.getLat());
        location.setLng(city.getLng());
        location.setUpdateTime(DateUtils.nowTime());
        uUserLocationMapper.updateByPrimaryKey(location);
        uUserLocationHisMapper.insert(new UUserLocationHis(location));
        // 加入到固定位置用户标记缓存
        busiRedisTemplate.sadd(buildFixLocationUserKey(), userId.toString());
    }

    /**
     * 固定定位的用户标记缓存
     *
     * @return
     */
    public RedisKey buildFixLocationUserKey() {
        return RedisKey.create(BusiKeyDefine.FixedLocationUsers);
    }


    /**
     * 获取用户在线状态信息
     *
     * @param userId
     * @return
     */
    public UUserOnline getUserOnline(Long userId) {
        return uUserOnlineMapper.selectByPrimaryKey(new UUserOnline(userId));
    }

    /**
     * 修改用户在线状态信息
     *
     * @param userId
     * @param onlineStatus
     */
    public void updateUserOnline(Long userId, OnlineStatus onlineStatus, String onlineId) {
        UUserOnline online = new UUserOnline(userId);
        online.setOnlineStatus(onlineStatus);
        online.setOnlineId(onlineId);
        if (OnlineStatus.Online == onlineStatus) {
            online.setLastOnlineTime(new Date());
        } else if (OnlineStatus.Offline == onlineStatus) {
            online.setLastOfflineTime(new Date());
        }
        uUserOnlineMapper.updateByPrimaryKeySelective(online);
    }

    /**
     * 设置上线时间(unix时间)
     *
     * @param userId
     */
    public void setLastOnlineTime(Long userId) {
        Long now = DateUtils.nowUnixTime();
        busiRedisTemplate.set(buildUserLastOnlineTimeKey(userId), now.toString());
    }

    /**
     * 获取上线时间(unix时间)
     *
     * @param userId
     * @return
     */
    public Long getLastOnlineTime(Long userId) {
        RedisKey onlineTimeKey = buildUserLastOnlineTimeKey(userId);
        return busiRedisTemplate.getLong(onlineTimeKey);
    }

    /**
     * 判断用户是否在线
     *
     * @param userId
     * @return
     */
    public boolean isOnline(Long userId) {
        Long lastUpdateTime = busiRedisTemplate.getLong(RedisKey.create(BusiKeyDefine.UserOnline, userId));
        return (DateUtils.nowUnixTime() - lastUpdateTime) < GlobalConstant.ONLINE_KEEP_TIME;
    }


    /**
     * 临时设置用户在线，用户加白渠道模拟用户上线
     *
     * @param userId
     */
    public void setOnlineStatus(Long userId) {
        busiRedisTemplate.set(buildUserOnlineKey(userId), DateUtils.nowUnixTime() + "");
    }

    /**
     * 封装在线信息
     *
     * @param userId
     * @return
     */
    public OnlineDTO buildOnlineInfo(Long userId, SexType sex) {
        Long lastUpdateTime = busiRedisTemplate.getLong(RedisKey.create(BusiKeyDefine.UserOnline, userId));
        double onlineKeepTime = SexType.Female == sex ? GlobalConstant.ONLINE_KEEP_TIME : GlobalConstant.MALE_ONLINE_KEEP_TIME;
        if(DateUtils.nowUnixTime() - lastUpdateTime < onlineKeepTime){
            if(isInCall(userId)){
                return new OnlineDTO(OnlineStatus.Busy);
            }else{
                return new OnlineDTO(OnlineStatus.Online);
            }
        }else{
            return new OnlineDTO(OnlineStatus.Offline);
        }
    }

    /**
     * 获取用户在线状态
     * 备注：该方法使用在对外展示上面（首页状态、私聊列表状态、关注列表状态 等待）
     */
    public OnlineStatus getOnlineStatus(Long userId, SexType sex){
        Long lastUpdateTime = busiRedisTemplate.getLong(RedisKey.create(BusiKeyDefine.UserOnline, userId));
        double onlineKeepTime = SexType.Female == sex ? GlobalConstant.ONLINE_KEEP_TIME : GlobalConstant.MALE_ONLINE_KEEP_TIME;
        if(DateUtils.nowUnixTime() - lastUpdateTime < onlineKeepTime){
            if(isInCall(userId)){
                return OnlineStatus.Busy;
            }else{
                return OnlineStatus.Online;
            }
        }else{
            return OnlineStatus.Offline;
        }
    }

    /**
     * 获取职业文本
     *
     * @param more
     * @return
     */
    public String getProfessionName(UUserMore more) {
        if (null == more.getProfessionId()) {
            return null;
        }
        TProfession profession = getProfession(more.getProfessionId());
        return profession.getProfessionName();
    }

    /**
     * 获取收入文本
     *
     * @param more
     * @return
     */
    public String getIncomeText(UUserMore more) {
        // 收入，职业
        IncomeType incomeType = getMoreIncome(more);
        if (null == incomeType || IncomeType.Secret == incomeType) {
            return null;
        }
        return incomeType.getDesc();
    }

    /**
     * 根据开关显示当前城市
     *
     * @param city
     * @param hideLocation
     * @return
     */
    public String getCity(String city, boolean hideLocation) {
        return hideLocation ? null : BusiUtils.getSimpleCity(city);
    }

    /**
     * 首页与他人城市信息
     *
     * @param basic       当前访问用户
     * @param targetBasic 对象用户
     * @param hasPosition 当前用户（非对方）是否开启了定位
     * @return
     * 策略：
     * 1. 自己看自己 显示自己的定位城市
     * 2. 对方设置了位置保密 或者 对方没有定位信息 （显示： 位置保密）
     * 3. 我（自己）没有定位信息 显示对方定位城市（有的话则显示，没有显示 位置保密）
     * 4. 双方都有位置信息时，不在同一个城市 则显示对方城市信息， 在同一个城市 显示距离多少Km
     */
    public String getUserArea(UUserBasic basic, UUserBasic targetBasic, BoolType hasPosition, BoolType hasLocation) {
        // 1.
        /*if(basic.getUserId().equals(targetBasic.getUserId())){
            return basic.getCity();
        }*/
        String targetCity = null; //BusiUtils.getSimpleCity(targetBasic.getCity());
        String selfCity = null; //BusiUtils.getSimpleCity(basic.getCity());
        // 2.
        if (StringUtils.isEmpty(targetCity) || hasLocation == BoolType.True) {
            return GlobalConstant.DEFAULT_AREA;
        }
        // 3.
        if(StringUtils.isEmpty(selfCity) || BoolType.False == hasPosition){
            return targetCity;
        }
        // 4.
        // 距离测算
        UUserLocation basicLocation = getUserLocation(basic.getUserId());
        UUserLocation targetLocation = getUserLocation(targetBasic.getUserId());
        // 城市相同, 如果城市相同，隐藏位置无效
        if (targetCity.equals(selfCity)) {
            if (null == basicLocation || null == targetLocation) {
                return targetCity;
            }
            if (BusiUtils.isInValidLocationData(basicLocation.getLng()) || BusiUtils.isInValidLocationData(basicLocation.getLat())) {
                return targetCity;
            }
            if (BusiUtils.isInValidLocationData(targetLocation.getLng()) || BusiUtils.isInValidLocationData(targetLocation.getLat())) {
                return targetCity;
            }
            // 计算 city 到 otherCity 的直线距离 单位m 最小100m
            Long distance = BusiUtils.getDistance(basicLocation.getLat(), basicLocation.getLng(), targetLocation.getLat(), targetLocation.getLng());
            if (null != distance) {
                String distanceStr = "<1km";
                if (distance > 1000) {
                    distanceStr = NumberUtils.format(distance / 1000D, 2) + "km";
                }
                return distanceStr;
            }
            return targetCity;
        }
        return targetCity;
    }

    /**
     * 记录转账验证通过标识
     */
    public void saveTransferVerifyPassSign(Long userId){
        CallbackAfterTransactionUtil.send(() -> {
            RedisKey redisKey = buildTransferVerifyPassSign(userId);
            busiRedisTemplate.set(redisKey, String.valueOf(System.currentTimeMillis()));
        });
    }

    /**
     * 判断是否存在转账验证通过标识
     */
    public boolean existsTransferVerifyPassSign(Long userId){
        RedisKey redisKey = buildTransferVerifyPassSign(userId);
        return busiRedisTemplate.exists(redisKey);
    }

    /**
     * 获取两个用户的距离 单位km
     *
     * @param userId
     * @param friendId
     * @return
     */
    public Double getUserAndFriendDistance(Long userId, Long friendId) {
        // 距离测算
        UUserLocation basicLocation = getUserLocation(userId);
        UUserLocation targetLocation = getUserLocation(friendId);
        if (null == basicLocation || null == targetLocation) {
            return null;
        }
        if (BusiUtils.isInValidLocationData(basicLocation.getLng()) || BusiUtils.isInValidLocationData(basicLocation.getLat())) {
            return null;
        }
        if (BusiUtils.isInValidLocationData(targetLocation.getLng()) || BusiUtils.isInValidLocationData(targetLocation.getLat())) {
            return null;
        }
        // 返回单位Km,保留两位小数
        return BusiUtils.getDistanceKm(basicLocation.getLat(), basicLocation.getLng(), targetLocation.getLat(), targetLocation.getLng());
    }

    /**
     * 获取用户当日修改年龄的次数
     */
    public Long getUserUpdateAgeTimes(String today, Long userId) {
        return busiRedisTemplate.getLong(buildUpdateAgeTimesLimit(today, userId));
    }

    /**
     * 记录用户当日修改年龄的次数
     */
    public void recordUserUpdateAgeTimes(String today, Long userId) {
        CallbackAfterTransactionUtil.send(() -> {
            busiRedisTemplate.incr(buildUpdateAgeTimesLimit(today, userId));
        });
    }

    /**
     * 是够爱思助手注册渠道使用苹果客户端
     */
    public boolean isAisiRegChannel(ClientType clientType, String channel) {
        return ClientType.iOS == clientType && GlobalConstant.ASZS_CHANNEL.equals(channel);
    }

    public String getMarriageStatusText(UUserMore more) {
        // 收入，职业
        MarriageStatus marriageStatus = getMoreMarriageStatus(more);
        if (null == marriageStatus || !marriageStatus.isDisplay()) {
            return null;
        }
        return marriageStatus.getDesc();
    }

    /**
     * 事务后保存用户位置权限信息
     */
    public void saveUserLocationRightInfo(Long userId, BoolType sign){
        CallbackAfterTransactionUtil.send(() -> {
            if(userId == null || sign == null){
                return;
            }
            busiRedisTemplate.setObject(buildUserLocationRightSign(userId), sign);
        });
    }

    public BoolType getUserLocationRightInfo(Long userId, BoolType defaultValue){
        BoolType value = getUserLocationRightInfo(userId);
        return value == null ? defaultValue : value;
    }

    /**
     * 判断用户的位置权限是否打开
     * 注意：如果缓存过期 返回null， 调用者根据自己的逻辑取相应默认值
     */
    public BoolType getUserLocationRightInfo(Long userId){
        return busiRedisTemplate.getObject(buildUserLocationRightSign(userId), BoolType.class);
    }

    /**
     * 获取全量用户信息
     */
    public List<Long> getAllUserIds(){
        return uUserBasicMapper.selectUserIds();
    }

    /**
     * 判断用户是否在通话中
     *
     * @param userId
     * @return
     */
    public boolean isInCall(Long userId) {
        return busiRedisTemplate.get(buildNetCallUserInfoKey(userId)) != null;
    }

    /**
     * 获取用户通话ID
     */
    public Long getCallId(Long userId){
        return busiRedisTemplate.getLong(buildNetCallUserInfoKey(userId), false);
    }

    /**
     * 初始化社交信息
     *
     * @param userId
     */
    public void initSocialInfo(Long userId) {
        // 初始化用户关系数据统计
        uUserBasicMapper.initRelationTotal(userId);
    }

    private RedisKey buildUserLastOnlineTimeKey(Long userId) {
        return RedisKey.create(BusiKeyDefine.UserLastOnlineTime, userId);
    }

    /**
     * 构建用户位置权限打开与否标识缓存
     */
    public RedisKey buildUserLocationRightSign(Long userId){
        return RedisKey.create(BusiKeyDefine.UserLocationRightSign, userId);
    }

    /**
     * 用户转账验证通过标识
     */
    private RedisKey buildTransferVerifyPassSign(Long userId){
        return RedisKey.create(BusiKeyDefine.TransferVerifyPassSign, userId);
    }

    /**
     * 聊主列表主页男用户完善资料引导
     * 一天仅显示一次
     */
    private RedisKey buildSocialHomeManPerfectDataGuide(Long userId){
        return RedisKey.create(BusiKeyDefine.SocialHomeManPerfectDataGuide, userId);
    }

    /**
     * 用户每日修改年龄次数限制
     */
    private RedisKey buildUpdateAgeTimesLimit(String today, Long userId) {
        return RedisKey.create(BusiKeyDefine.UserUpdateAgeTimesLimit, userId);
    }

    private RedisKey buildUserOnlineKey(Long userId) {
        return RedisKey.create(BusiKeyDefine.UserOnline, userId);
    }

    /**
     * 记录用户弹窗青少年弹窗
     */
    private RedisKey buildUserPopTeenagers(Long userId){
        return RedisKey.create(BusiKeyDefine.UserPopTeenagers, userId);
    }

    /**
     * 用户解锁付费媒资信息
     */
    private RedisKey buildUserUnlockChargeMedia(Long userId){
        return RedisKey.create(BusiKeyDefine.UserUnlockChargeMedia, userId);
    }

    /**
     * 用户忙碌缓存标识
     * @param userId
     * @return
     */
    private RedisKey buildNetCallUserInfoKey(Long userId) {
        return RedisKey.create(BusiKeyDefine.NetCallUserInfo, userId);
    }
}
