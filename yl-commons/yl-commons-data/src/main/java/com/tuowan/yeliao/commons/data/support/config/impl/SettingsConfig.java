package com.tuowan.yeliao.commons.data.support.config.impl;

import com.alibaba.nacos.common.utils.Pair;
import com.easyooo.framework.common.enums.DatePattern;
import com.easyooo.framework.common.util.DateUtils;
import com.easyooo.framework.common.util.EnumUtils;
import com.easyooo.framework.common.util.SpringContextUtils;
import com.easyooo.framework.common.util.StringUtils;
import com.tuowan.yeliao.commons.core.enums.errcode.ErrCodeType;
import com.tuowan.yeliao.commons.core.enums.general.BoolType;
import com.tuowan.yeliao.commons.core.exception.DataException;
import com.tuowan.yeliao.commons.data.entity.config.TSettings;
import com.tuowan.yeliao.commons.data.enums.config.SettingsType;
import com.tuowan.yeliao.commons.data.persistence.config.TSettingsMapper;
import com.tuowan.yeliao.commons.data.support.config.ConfigFactory;
import com.tuowan.yeliao.commons.data.support.config.ConfigReloadBase;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/9/20 10:39
 */
public class SettingsConfig extends ConfigReloadBase<TSettings> {

    private static final Logger LOG = LoggerFactory.getLogger(SettingsConfig.class);

    /**
     * @see #getString(SettingsType)
     */
    public SettingsConfig() {
    }

    public static String getString(SettingsType type) {
        TSettings setting = ConfigFactory.getSettingsInstance().get(type.name());
        if (setting == null) {
            LOG.error("全局参数{}未配置，请检查", type.name());
            throw new DataException(ErrCodeType.Unknown);
        }
        return setting.getParamValue();
    }

    public static Long getLong(SettingsType type) {
        return new Long(getString(type));
    }

    public static Integer getInteger(SettingsType type) {
        return new Integer(getString(type));
    }

    public static Double getDouble(SettingsType type) {
        return new Double(getString(type));
    }

    public static Boolean getBoolean(SettingsType type) {
        return BoolType.True.getId().equals(getString(type));
    }

    public static BoolType getBoolType(SettingsType type) {
        return EnumUtils.byId(getString(type), BoolType.class);
    }

    public static String[] getStringArray(SettingsType type) {
        String value = getString(type);
        if (value == null) {
            return new String[0];
        }
        return value.split(",");
    }

    /**
     * 获取以英文逗号分开的日期范围
     *
     * @param type
     * @return
     */
    public static Date[] getDateRange(SettingsType type) {
        String value = getString(type);
        if (value == null) {
            return null;
        }
        return DateUtils.parseRangeDate(value);
    }

    /**
     * 获取日期范围
     * 日期配置格式必须为：yyyy-MM-dd HH:mm:ss
     * @param type
     * @return
     */
    public static Pair<Date, Date> getDateRange(SettingsType type, String separator) {
        String value = getString(type);
        if (value == null) {
            return null;
        }
        String[] split = StringUtils.split(value, separator);
        if(split.length != 2){
            return null;
        }
        return Pair.with(DateUtils.parse(split[0], DatePattern.YMD_HMS), DateUtils.parse(split[1], DatePattern.YMD_HMS));
    }

    /**
     * 获取日期
     * 需要日期配置格式为 yyyy-MM-dd HH:mm:ss
     */
    public static Date getDate(SettingsType type){
        String value = getString(type);
        if (value == null) {
            return null;
        }
        return DateUtils.parse(value, DatePattern.YMD_HMS);
    }

    /**
     * 获取日期毫秒数
     *
     * @param type
     * @return
     */
    public static Long getDateMills(SettingsType type) {
        String value = getString(type);
        if (value == null) {
            return 0L;
        }
        return DateUtils.parse(value, DatePattern.YMD_HMS).getTime();
    }

    @Override
    protected List<TSettings> selectAll() {
        List<TSettings> settings = SpringContextUtils.getBean(TSettingsMapper.class).selectAll();

        return settings;
    }

    @Override
    protected String getKey(TSettings config) {
        return config.getParamName();
    }

}
