package com.tuowan.yeliao.commons.data.dto.social;

import com.tuowan.yeliao.commons.data.enums.general.ClientTouchType;

public class ActivityDto {
    // 首页顶部
    private String icon;
    // 首页弹窗
    private String  pic;
    private Integer width;
    private Integer height;
    private ClientTouchType touchType;
    private String touchValue;

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getPic() {
        return pic;
    }

    public void setPic(String pic) {
        this.pic = pic;
    }

    public Integer getWidth() {
        return width;
    }

    public void setWidth(Integer width) {
        this.width = width;
    }

    public Integer getHeight() {
        return height;
    }

    public void setHeight(Integer height) {
        this.height = height;
    }

    public ClientTouchType getTouchType() {
        return touchType;
    }

    public void setTouchType(ClientTouchType touchType) {
        this.touchType = touchType;
    }

    public String getTouchValue() {
        return touchValue;
    }

    public void setTouchValue(String touchValue) {
        this.touchValue = touchValue;
    }
}
