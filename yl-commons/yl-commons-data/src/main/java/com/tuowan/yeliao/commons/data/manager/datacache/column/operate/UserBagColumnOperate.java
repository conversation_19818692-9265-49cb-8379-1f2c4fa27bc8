package com.tuowan.yeliao.commons.data.manager.datacache.column.operate;

import com.easyooo.framework.common.util.ListUtils;
import com.tuowan.yeliao.commons.core.exception.DataException;
import com.tuowan.yeliao.commons.data.entity.user.UUserBag;
import com.tuowan.yeliao.commons.data.manager.datacache.UserDataGoodsCacheManager;
import com.tuowan.yeliao.commons.data.manager.datacache.column.BaseBusiColumnOperate;
import com.tuowan.yeliao.commons.data.manager.datacache.column.BusiColumn;
import com.tuowan.yeliao.commons.data.persistence.config.TProdSocialGoodsMapper;
import com.tuowan.yeliao.commons.data.utils.StaticBeanUtils;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 背包操作类
 * <AUTHOR>
 *
 */
public abstract class UserBagColumnOperate extends BaseBusiColumnOperate<List<UUserBag>> {

	protected UserDataGoodsCacheManager userDataGoodsCacheManager = StaticBeanUtils.getUserDataGoodsCacheManager();
	protected TProdSocialGoodsMapper tProdSocialGoodsMapper = StaticBeanUtils.getTProdSocialGoodsMapper();

	@Override
	protected void bindBusiColumn(BusiColumn column) {
		super.bindBusiColumn(column);
		checkColumnOperateBindColumn();
	}
	/**
	 * 检查背包各个属性的操作类绑定的业务属性是否一致
	 */
	protected void checkColumnOperateBindColumn() {
		if (!this.getClass().getSimpleName().startsWith(column.name())) {
			throw new DataException("用户业务属性{}初始化错误,当前操作类限制只能是{}", column.name(), this.getClass().getSimpleName());
		}
	}
	
	/**
	 * redis有无缓存当前业务属性<br>
	 * 背包根据BagLoadSign判断，如果有这个属性说明redis已经缓存了背包相关的所有业务属性
	 */
	public boolean hasColumnInRedis(Map<String,String> fieldValueMap,Long programId) {
		if(fieldValueMap==null) {
			return false;
		}
		return fieldValueMap.containsKey(BusiColumn.BagLoadSign.name());
	}
	

	/**
	 * 比较两个set是否一致
	 */
	public boolean isEqualSet(Set<String> set1, Set<String> set2) {
		if (ListUtils.isEmpty(set1) && ListUtils.isEmpty(set2)) {
			return true;// 两个都为空
		}
		if (!ListUtils.isEmpty(set1) && !ListUtils.isEmpty(set2)) {
			// 两个都不为空
			if (set1.size() != set2.size()) {
				return false;
			}
			if (set1.containsAll(set2) && set2.containsAll(set1)) {
				return true;
			}
		}
		return false;
	}
}
