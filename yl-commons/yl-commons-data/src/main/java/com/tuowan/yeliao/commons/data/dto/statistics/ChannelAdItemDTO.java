package com.tuowan.yeliao.commons.data.dto.statistics;

/**
 * 渠道计划信息
 *
 * <AUTHOR>
 * @date 2022/3/30 17:38
 */
public class ChannelAdItemDTO {

    /**
     * 推广类型
     */
    private String typeCode;
    /**
     * 推广名称
     */
    private String typeName;
    /**
     * 渠道ID
     */
    private Integer channelId;
    /**
     * 渠道代码
     */
    private String channelCode;
    /**
     * 计划ID
     */
    private String adId;
    /**
     * 计划名称
     */
    private String adName;
    /**
     * 新客ROI
     */
    private Double roiValue;
    /**
     * 消耗金额
     */
    private Double costAmount;
    /**
     * 付费金额
     */
    private Double paidAmount;

    public String getTypeCode() {
        return typeCode;
    }

    public void setTypeCode(String typeCode) {
        this.typeCode = typeCode;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    public Integer getChannelId() {
        return channelId;
    }

    public void setChannelId(Integer channelId) {
        this.channelId = channelId;
    }

    public String getChannelCode() {
        return channelCode;
    }

    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode;
    }

    public String getAdId() {
        return adId;
    }

    public void setAdId(String adId) {
        this.adId = adId;
    }

    public String getAdName() {
        return adName;
    }

    public void setAdName(String adName) {
        this.adName = adName;
    }

    public Double getRoiValue() {
        return roiValue;
    }

    public void setRoiValue(Double roiValue) {
        this.roiValue = roiValue;
    }

    public Double getCostAmount() {
        return costAmount;
    }

    public void setCostAmount(Double costAmount) {
        this.costAmount = costAmount;
    }

    public Double getPaidAmount() {
        return paidAmount;
    }

    public void setPaidAmount(Double paidAmount) {
        this.paidAmount = paidAmount;
    }
}
