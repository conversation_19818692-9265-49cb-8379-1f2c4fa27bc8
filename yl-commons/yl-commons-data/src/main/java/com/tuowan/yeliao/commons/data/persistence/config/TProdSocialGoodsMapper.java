/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.commons.data.persistence.config;

import com.easyooo.framework.cache.annotations.GroupStrategy;
import com.easyooo.framework.sharding.annotation.Table;
import com.tuowan.yeliao.commons.data.entity.config.TProdSocialGoods;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@Table(value = "T_PROD_CHAT_GOODS", schema = "YL_CONFIG")
public interface TProdSocialGoodsMapper {
    int deleteByPrimaryKey(TProdSocialGoods record);

    int insert(TProdSocialGoods record);

    TProdSocialGoods selectByPrimaryKey(TProdSocialGoods record);

    int updateByPrimaryKeySelective(TProdSocialGoods record);

    int updateByPrimaryKey(TProdSocialGoods record);

    @GroupStrategy
    List<TProdSocialGoods> selectByGoodsType(TProdSocialGoods record);

    List<TProdSocialGoods> selectByParentGoodsId(@Param("parentGoodsId") Integer parentGoodsId);
}