package com.tuowan.yeliao.commons.data.dto.query;


import com.tuowan.yeliao.commons.core.enums.general.PackageType;
import com.tuowan.yeliao.commons.data.enums.user.SmsRechargeType;

/**
 * 短信用户DTO
 *
 * <AUTHOR>
 * @date 2021/6/30 14:50
 */
public class SmsUserDTO {

    /** 用户ID */
    private Long userId;
    /** 手机号 */
    private String mobile;
    /** 充值金额类型 0924 取消未充值用户*/
    private SmsRechargeType rechargeType;
    /** 零钱剩余 */
    private Long cash;
    /** 包名 */
    private PackageType packageType;

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public SmsRechargeType getRechargeType() {
        return rechargeType;
    }

    public void setRechargeType(SmsRechargeType rechargeType) {
        this.rechargeType = rechargeType;
    }

    public Long getCash() {
        return cash;
    }

    public void setCash(Long cash) {
        this.cash = cash;
    }

    public PackageType getPackageType() {
        return packageType;
    }

    public void setPackageType(PackageType packageType) {
        this.packageType = packageType;
    }
}
