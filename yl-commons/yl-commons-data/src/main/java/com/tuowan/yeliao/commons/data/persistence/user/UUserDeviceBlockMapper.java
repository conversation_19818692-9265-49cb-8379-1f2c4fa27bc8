/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.commons.data.persistence.user;

import com.easyooo.framework.sharding.annotation.Table;
import com.tuowan.yeliao.commons.data.entity.user.UUserDeviceBlock;
import org.springframework.stereotype.Repository;

@Repository
@Table(value = "U_USER_DEVICE_BLOCK", schema = "YL_BUSI")
public interface UUserDeviceBlockMapper {
    int deleteByPrimaryKey(UUserDeviceBlock record);

    int insert(UUserDeviceBlock record);

    UUserDeviceBlock selectByPrimaryKey(UUserDeviceBlock record);

    int updateByPrimaryKeySelective(UUserDeviceBlock record);

    int updateByPrimaryKey(UUserDeviceBlock record);
}