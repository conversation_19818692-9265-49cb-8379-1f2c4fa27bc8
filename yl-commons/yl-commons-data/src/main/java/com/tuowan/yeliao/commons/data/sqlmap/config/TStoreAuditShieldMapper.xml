<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tuowan.yeliao.commons.data.persistence.config.TStoreAuditShieldMapper">
  <sql id="Base_Column_List">
    channel, sex, shield_time, creator, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="TStoreAuditShield" resultType="TStoreAuditShield">
    select 
    <include refid="Base_Column_List" />
    from t_store_audit_shield
    where channel = #{channel}
      and sex = #{sex}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="TStoreAuditShield">
    delete from t_store_audit_shield
    where channel = #{channel}
      and sex = #{sex}
  </delete>
  <insert id="insert" parameterType="TStoreAuditShield">
    insert into t_store_audit_shield (channel, sex, shield_time, creator, create_time)
    values (#{channel}, #{sex}, #{shieldTime}, #{creator}, #{createTime})
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="TStoreAuditShield">
    update t_store_audit_shield
    <set>
      <if test="shieldTime != null">
        shield_time = #{shieldTime},
      </if>
      <if test="creator != null">
        creator = #{creator},
      </if>
      <if test="createTime != null">
        create_time = #{createTime},
      </if>
    </set>
    where channel = #{channel}
      and sex = #{sex}
  </update>
  <update id="updateByPrimaryKey" parameterType="TStoreAuditShield">
    update t_store_audit_shield
    set shield_time = #{shieldTime},
      creator = #{creator},
      create_time = #{createTime}
    where channel = #{channel}
      and sex = #{sex}
  </update>
</mapper>