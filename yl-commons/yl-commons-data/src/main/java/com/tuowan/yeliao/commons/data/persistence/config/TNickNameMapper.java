/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.commons.data.persistence.config;

import com.easyooo.framework.cache.annotations.GroupStrategy;
import com.easyooo.framework.sharding.annotation.Table;
import com.tuowan.yeliao.commons.data.entity.config.TNickName;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@Table(value = "T_NICK_NAME", schema = "YL_CONFIG")
public interface TNickNameMapper {
    int deleteByPrimaryKey(TNickName record);

    int insert(TNickName record);

    TNickName selectByPrimaryKey(TNickName record);

    int updateByPrimaryKeySelective(TNickName record);

    int updateByPrimaryKey(TNickName record);

    @GroupStrategy
    List<TNickName> selectBySexType(TNickName record);

    List<TNickName> selectList(TNickName record);

    int countByNickname(@Param("nickname") String nickname);
}