<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tuowan.yeliao.commons.data.persistence.config.TProdGiftAnimationMapper">
  <sql id="Base_Column_List">
    key_id, gift_id, count, count_upper_limit, animate_code, eff_time, exp_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="TProdGiftAnimation" resultType="TProdGiftAnimation">
    select 
    <include refid="Base_Column_List" />
    from t_prod_gift_animation
    where key_id = #{keyId}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="TProdGiftAnimation">
    delete from t_prod_gift_animation
    where key_id = #{keyId}
  </delete>
  <insert id="insert" parameterType="TProdGiftAnimation">
    insert into t_prod_gift_animation (key_id, gift_id, count, count_upper_limit, animate_code, eff_time, 
      exp_time)
    values (#{keyId}, #{giftId}, #{count}, #{countUpperLimit}, #{animateCode}, #{effTime}, 
      #{expTime})
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="TProdGiftAnimation">
    update t_prod_gift_animation
    <set>
      <if test="giftId != null">
        gift_id = #{giftId},
      </if>
      <if test="count != null">
        count = #{count},
      </if>
      <if test="countUpperLimit != null">
        count_upper_limit = #{countUpperLimit},
      </if>
      <if test="animateCode != null">
        animate_code = #{animateCode},
      </if>
      <if test="effTime != null">
        eff_time = #{effTime},
      </if>
      <if test="expTime != null">
        exp_time = #{expTime},
      </if>
    </set>
    where key_id = #{keyId}
  </update>
  <update id="updateByPrimaryKey" parameterType="TProdGiftAnimation">
    update t_prod_gift_animation
    set gift_id = #{giftId},
      count = #{count},
      count_upper_limit = #{countUpperLimit},
      animate_code = #{animateCode},
      eff_time = #{effTime},
      exp_time = #{expTime}
    where key_id = #{keyId}
  </update>
</mapper>