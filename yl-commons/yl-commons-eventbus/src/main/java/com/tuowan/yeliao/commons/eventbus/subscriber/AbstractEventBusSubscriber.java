package com.tuowan.yeliao.commons.eventbus.subscriber;

import com.easyooo.framework.common.util.EnumUtils;
import com.easyooo.framework.common.util.JsonUtils;
import com.easyooo.framework.common.util.MsgUtils;
import com.google.common.eventbus.AllowConcurrentEvents;
import com.google.common.eventbus.Subscribe;
import com.tuowan.yeliao.commons.context.GlobalContext;
import com.tuowan.yeliao.commons.context.GlobalUtils;
import com.tuowan.yeliao.commons.context.proxy.ProxyExecutors;
import com.tuowan.yeliao.commons.eventbus.enums.EventBusType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 事件总线监听器
 *
 * <AUTHOR>
 * @date 2022/3/10 16:46
 */
public abstract class AbstractEventBusSubscriber implements EventBusSubscriber {

    protected final Logger LOG = LoggerFactory.getLogger(this.getClass());

    /**
     * 接受事件消息
     *
     * @param context
     */
    @AllowConcurrentEvents
    @Subscribe
    public void receiveEvent(GlobalContext context) {
        try {
            EventBusType busType = EnumUtils.byName(context.getEventBusType(), EventBusType.class);
            ProxyExecutors.doProxy(context, () -> doProcessEvent(busType, context));
        } catch (Exception e) {
            LOG.error(MsgUtils.format("事件消息处理失败，消息内容：{}，原因：", JsonUtils.seriazileAsString(context)), e);
        }
    }

    /**
     * 处理具体的事件消息
     *
     * @param context
     */
    protected abstract void doProcessEvent(EventBusType busType, GlobalContext context);

    /**
     * 包装单个业务处理逻辑，避免当前业务出错影响其他业务
     *
     * @param title
     * @param callback
     */
    protected void doProcess(String title, ProcessCallback callback) {
        try {
            callback.execute();
        } catch (Exception e) {
            String context = JsonUtils.seriazileAsString(GlobalUtils.getGlobalContext());
            LOG.error(MsgUtils.format("业务 [{}] 执行失败，context：{}，原因：", title, context), e);
        }
    }

    protected interface ProcessCallback {
        void execute();
    }
}
