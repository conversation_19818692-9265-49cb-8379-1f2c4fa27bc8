/*!
 * Copyright 2018, Julun, Inc.
 */

package com.tuowan.yeliao.commons.open.rongim;

import com.easyooo.framework.common.util.JsonUtils;
import com.tuowan.yeliao.commons.config.configuration.impl.RongImConfig;
import io.rong.RongCloud;
import io.rong.models.message.*;
import io.rong.models.response.ResponseResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

/**
 * 融云日志记录工具类
 *
 * <AUTHOR>
 * @date 2019/3/13 16:59
 */
public class RongImLogUtil {

    private static Logger LOG = LoggerFactory.getLogger(RongImLogUtil.class);

    /**
     * 拆分发送目标
     *
     * @param targetIds
     * @param <T>
     * @return
     */
    public static  <T> List<List<T>> splitTargetIds(T[] targetIds) {
        List<List<T>> resultList = new ArrayList<>();
        List<T> tmpList = null;
        for (int i = 0; i < targetIds.length; i++) {
            if (i % RongImConfig.SEND_TARGET_LIMIT == 0) {
                tmpList = new ArrayList<>();
                resultList.add(tmpList);
            }
            tmpList.add(targetIds[i]);
        }
        return resultList;
    }

    /**
     * 记录发送日志，并计算上行量和下行量
     * // logMap.put("msgBody", contentJson);
     * 基于存储考虑，放弃存储消息体
     */
    public static void doSendMessageToRongCloud(RongCloud rongCloud, MessageModel mm) throws Exception {
        String[] targets = mm.getTargetId();
        ResponseResult result;
        try {
            if (mm instanceof SystemMessage) { // 系统消息
                result = rongCloud.message.system.send(mm);
            } else if (mm instanceof PrivateMessage) { // 单聊消息
                result = rongCloud.message.msgPrivate.send((PrivateMessage) mm);
            } else if (mm instanceof GroupMessage) { // 群组消息
                result = rongCloud.message.group.send((GroupMessage) mm);
            } else if (mm instanceof BroadcastMessage) { // 全站消息（备注：全站消息我们用【在线用户广播】发，真正的全站消息一天仅允许发送3次）
                result = rongCloud.message.system.onlineBroadcast((BroadcastMessage) mm);
            } else if (targets == null) {
                result = rongCloud.message.chatroom.broadcast((ChatroomMessage) mm);
            } else {
                result = rongCloud.message.chatroom.send((ChatroomMessage) mm);
            }
        } catch (Exception e) {
            throw e;
        }
        // 检测融云返回结果
        if (null != result && result.getCode() != 200) {
            throw new RongImException(result.getErrorMessage());
        }
    }

    /**
     * 计算本条消息的下行量，预估，可能会比实际的要多，因为用户心跳存在1分钟的延时
     */
    private static long findMessageDownCount(MessageModel mm, RongCloudTargetType type) {
        if (type == RongCloudTargetType.User) {
            return mm.getTargetId().length * 1L;
        }
        return -1;
    }

    private enum RongCloudTargetType {
        /**
         * 用户私信
         */
        User("U"),
        Broadcast("B"),
        Room("R"),
        ;
        private String id;

        RongCloudTargetType(String id) {
            this.id = id;
        }

        public String getId() {
            return id;
        }
    }

}
