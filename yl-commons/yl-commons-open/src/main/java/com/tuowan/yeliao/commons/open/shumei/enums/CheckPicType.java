package com.tuowan.yeliao.commons.open.shumei.enums;

/**
 * 数美图片检测的风险类型
 *
 * <AUTHOR>
 * @date 2021/12/22 13:59
 */
public enum CheckPicType {
    /**
     * 涉政识别
     */
    POLITICS,

    /**
     * 暴恐识别
     */
    VIOLENCE,

    /**
     * 违禁识别
     */
    BAN,

    /**
     * 色情识别
     */
    PORN,

    /**
     * 广告识别
     */
    AD,

    /**
     * 商企LOGO识别
     */
    LOGO,

    /**
     * 识别图片中所有文字
     */
    OCR,

    /**
     * 未成年人识别
     */
    MINOR,

    /**
     * 特殊画面识别
     */
    SCREEN,

    /**
     * 场景画面识别
     */
    SCENCE,

    /**
     * 二维码识别
     */
    QR,

    /**
     * 图像质量识别
     */
    QUALITY,

    /**
     * 人脸识别
     */
    FACE,

    /**
     * 公众人物识别
     */
    STAR,

    /**
     * 人像识别
     */
    PORTRAIT,

    /**
     * 颜值识别
     */
    BEAUTY,

    /**
     * 动物识别
     */
    ANIMAL,

    /**
     * 物品识别
     */
    OBJECT,

    /**
     * 人脸比对
     */
    FACECOMPARE;
}
