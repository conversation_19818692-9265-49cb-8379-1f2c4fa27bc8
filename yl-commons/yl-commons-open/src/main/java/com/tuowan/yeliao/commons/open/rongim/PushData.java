package com.tuowan.yeliao.commons.open.rongim;

import com.tuowan.yeliao.commons.data.enums.general.ClientTouchType;

/**
 * 融云应用推送消息扩展属性
 *
 * <AUTHOR>
 * @date 2020/9/2 15:34
 */
public class PushData {

    /** 推送标题(可选) */
    private String title;
    /** 推送图片 */
    private String pic;
    /** 推送消息点击跳转类型 */
    private ClientTouchType touchType;
    /** 推送消息点击跳转类型值 */
    private String touchValue;

    public PushData() {
    }

    public PushData(String title) {
        this.title = title;
    }

    public PushData(ClientTouchType touchType) {
        this.touchType = touchType;
    }

    public static PushData create(String title, String pic) {
        PushData data = new PushData();
        data.setTitle(title);
        data.setPic(pic);
        return data;
    }

    public PushData(String title, String pic, ClientTouchType touchType, String touchValue) {
        this.title = title;
        this.pic = pic;
        this.touchType = touchType;
        this.touchValue = touchValue;
    }

    public PushData(String title, ClientTouchType touchType, String touchValue) {
        this.title = title;
        this.touchType = touchType;
        this.touchValue = touchValue;
        if (touchType == null) {
            this.touchType = ClientTouchType.SystemNotice;
        }
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getPic() {
        return pic;
    }

    public void setPic(String pic) {
        this.pic = pic;
    }

    public ClientTouchType getTouchType() {
        return touchType;
    }

    public void setTouchType(ClientTouchType touchType) {
        this.touchType = touchType;
    }

    public String getTouchValue() {
        return touchValue;
    }

    public void setTouchValue(String touchValue) {
        this.touchValue = touchValue;
    }
}
