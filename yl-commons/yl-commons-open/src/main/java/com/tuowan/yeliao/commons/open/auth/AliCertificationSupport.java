package com.tuowan.yeliao.commons.open.auth;

import com.alibaba.nacos.common.utils.Pair;
import com.aliyun.cloudauth20190307.Client;
import com.aliyun.cloudauth20190307.models.*;
import com.aliyun.tearpc.models.Config;
import com.aliyun.teautil.models.RuntimeOptions;
import com.easyooo.framework.common.extend.SimpleMap;
import com.easyooo.framework.common.util.JsonUtils;
import com.easyooo.framework.common.util.StringUtils;
import com.tuowan.yeliao.commons.config.configuration.impl.AliyunConfig;
import com.tuowan.yeliao.commons.core.enums.errcode.ErrCodeType;
import com.tuowan.yeliao.commons.core.exception.BusiException;
import com.tuowan.yeliao.commons.core.exception.DataException;
import com.tuowan.yeliao.commons.core.exception.InternalException;
import com.tuowan.yeliao.commons.data.enums.user.UserAuthType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 阿里认证
 * 阿里认证接口文档地址：https://help.aliyun.com/document_detail/163102.html
 *
 * <AUTHOR>
 * @date 2020/9/10 9:31
 */
@Component
public class AliCertificationSupport implements InitializingBean {
    private final Logger LOG = LoggerFactory.getLogger(getClass());

    /**
     * 阿里云认证客户端
     */
    private Client client;
    /**
     * 运行时选项参数
     */
    private RuntimeOptions runtimeOptions;

    /**
     * 初始化认证（实名）
     *
     * @Param certName 真实姓名
     * @Param certNo 身份证号
     * @Param metaInfo 元数据，客户端调用SDK获取
     */
    public String initFaceVerify(String certName, String certNo, String metaInfo, String orderNo) {
        if (StringUtils.isEmpty(certName) || StringUtils.isEmpty(certNo)) {
            throw new DataException(ErrCodeType.InvalidArguments);
        }
        try {
            InitFaceVerifyRequest request = new InitFaceVerifyRequest();
            // 请输入场景ID+L。
            request.setSceneId(AliyunConfig.AUTH_SCENE_ID); // 控制台获取
            // 设置商户请求的唯一标识。
            request.setOuterOrderNo(orderNo);
            // 认证方案。
            request.setProductCode("ID_PRO");
            // 模式。
            request.setModel("LIVENESS");
            request.setCertType("IDENTITY_CARD");
            request.setCertName(certName);
            request.setCertNo(certNo);
            // MetaInfo环境参数。
            request.setMetaInfo(metaInfo);

            InitFaceVerifyResponse response = client.initFaceVerify(request, runtimeOptions);
            LOG.info("AliCertificationSupport-initFaceVerify-info response:{}", JsonUtils.seriazileAsString(response));
            if (!"200".equals(response.getCode())) {
                if ("401".equals(response.getCode())) {
                    throw new BusiException("请仔细核对姓名和身份证号！");
                }
                throw new InternalException(response.getMessage());
            }
            return response.getResultObject().getCertifyId();

        } catch (BusiException e) {
            throw new BusiException(e.getMessage());
        } catch (Exception e) {
            LOG.error("AliCertificationSupport-initFaceVerify-error", e);
        }
        throw new BusiException("服务内部错误，请稍后重试！");
    }


    /**
     * 初始化认证（实人）
     * metaInfo 格式："{\"zimVer\":\"3.0.0\",\"appVersion\": \"1\",\"bioMetaInfo\": \"4.1.0:11501568,0\","
     * + "\"appName\": \"com.aliyun.antcloudauth\",\"deviceType\": \"ios\",\"osVersion\": \"iOS 10.3.2\","
     * + "\"apdidToken\": \"\",\"deviceModel\": \"iPhone9,1\"}"
     */
    public String initHeadVerify(Long userId, String orderNo, String metaInfo, String headPicUrl) {
        if (Objects.isNull(userId) || StringUtils.isEmpty(orderNo) || StringUtils.isEmpty(metaInfo) || StringUtils.isEmpty(headPicUrl)) {
            throw new DataException(ErrCodeType.InvalidArguments);
        }
        try {
            InitFaceVerifyRequest request = new InitFaceVerifyRequest();
            // 请输入场景ID+L，控制台获取。
            request.setSceneId(AliyunConfig.AUTH_SCENE_ID);
            // 设置商户请求的唯一标识。
            request.setOuterOrderNo(orderNo);
            // 认证方案（固定值）。
            request.setProductCode("PV_FV");
            request.setUserId(userId.toString());
            // 模式（固定值）。
            request.setModel("LIVENESS");
            // MetaInfo环境参数，客户端上传。
            request.setMetaInfo(metaInfo);
            // 方式三：照片OSS的URL地址，公网可访问（拼接图片的完整地址）。
            request.setFaceContrastPictureUrl(headPicUrl);

            InitFaceVerifyResponse response = client.initFaceVerify(request, runtimeOptions);
            LOG.info("AliCertificationSupport-initHeadVerify-info response:{}", JsonUtils.seriazileAsString(response));
            if (!"200".equals(response.getCode())) {
                if ("419".equals(response.getCode())) {
                    throw new BusiException("请先上传本人头像！");
                }
                throw new BusiException(response.getMessage());
            }
            return response.getResultObject().getCertifyId();
        } catch (BusiException e) {
            throw new BusiException(e.getMessage());
        } catch (Exception e) {
            LOG.error("AliCertificationSupport-initHeadVerify-error reason:", e);
            throw new BusiException("服务内部错误，请稍后重试！");
        }
    }

    /**
     * 获取认证结果
     *
     * @return first 为验证结果
     * @return second 为通过图片的地址（仅在实人认证时返回）
     */
    public Pair<Boolean, String> queryFaceVerifyResult(UserAuthType type, String certifyId) {
        if (org.apache.commons.lang3.StringUtils.isEmpty(certifyId)) {
            throw new DataException(ErrCodeType.InvalidArguments);
        }
        try {
            // 通过以下代码创建API请求并设置参数。
            DescribeFaceVerifyRequest request = new DescribeFaceVerifyRequest();
            // 请输入场景ID+L。
            request.setSceneId(AliyunConfig.AUTH_SCENE_ID);
            request.setCertifyId(certifyId);

            DescribeFaceVerifyResponse response = client.describeFaceVerify(request, runtimeOptions);
            LOG.info("AliAuthSupport-queryFaceVerifyResult-info response:{}", JsonUtils.seriazileAsString(response));
            if (!"200".equals(response.getCode())) {
                throw new BusiException(response.getMessage());
            }
            // T：通过 F：不通过
            if (UserAuthType.RealName == type && "T".equalsIgnoreCase(response.getResultObject().getPassed())) {
                return Pair.with(true, null);
            }
            if (UserAuthType.RealHead == type && "T".equalsIgnoreCase(response.getResultObject().getPassed())) {
                SimpleMap simpleMap = JsonUtils.toSimpleMap(response.getResultObject().getMaterialInfo());
                simpleMap = simpleMap.getSimpleMap("facialPictureFront");
                return Pair.with(true, simpleMap.getString("pictureUrl"));
            }
            throw new BusiException(VerifySubCode.getMesg(type, response.getResultObject().getSubCode()));
        } catch (BusiException e) {
            throw new BusiException(e.getMessage());
        } catch (Exception e) {
            LOG.error("AliAuthSupport-queryFaceVerifyResult-error reason:", e);
            throw new BusiException("服务内部错误，请稍后重试！");
        }
    }

    /**
     * 实人认证 图片比对
     * 备注：该基本方法只关心图片的比对，过程中遇到问题会抛出 调用模块理应根据自己实际的业务情况处理或铺货异常
     * 例子： 修改头像这里调用了这个方法，判断用户上传的头像是否是真人头像，尽管这个方法由于网络问题或者欠费问题
     * 调用失败了，修改头像的流程也不应该被打扰，所以在修改头像调用的地方 应该铺货该方法抛出来的异常
     *
     * @param resourcePic 参照图
     * @param targetPic   需要比对的图
     */
    public boolean realHeadCompare(String orderNo, String resourcePic, String targetPic) {
        try {
            CompareFaceVerifyRequest request = new CompareFaceVerifyRequest();
            // 请输入场景ID+L 控制台获取。
            request.setSceneId(AliyunConfig.AUTH_SCENE_ID);
            request.setOuterOrderNo(orderNo);
            request.setProductCode("PV_FC"); // 固定值
            // 照片OSS的URL地址，公网可访问。
            request.setSourceFaceContrastPictureUrl(resourcePic);
            // 照片OSS的URL地址，公网可访问。
            request.setTargetFaceContrastPictureUrl(targetPic);

            CompareFaceVerifyResponse response = client.compareFaceVerify(request, runtimeOptions);
            LOG.info("AliCertificationSupport-realHeadCompare-info response:{}", JsonUtils.seriazileAsString(response));
            if (!"200".equals(response.getCode())) {
                throw new InternalException(response.getMessage());
            }
            return "T".equalsIgnoreCase(response.getResultObject().getPassed());
        } catch (Exception e) {
            LOG.error("AliCertificationSupport-realHeadCompare-error reason:", e);
        }
        throw new BusiException("服务内部错误，请稍后重试！");
    }


    @Override
    public void afterPropertiesSet() throws Exception {
        RuntimeOptions runtime = new RuntimeOptions();
        runtime.readTimeout = 10000;
        runtime.connectTimeout = 10000;
        runtimeOptions = runtime;

        Config config = new Config();
        config.setAccessKeyId(AliyunConfig.ACCESS_KEY_ID);
        config.setAccessKeySecret(AliyunConfig.ACCESS_KEY_SECRET);
        config.setEndpoint("cloudauth.cn-shanghai.aliyuncs.com");
        client = new Client(config);
    }

    private enum VerifySubCode {
        // 实名认证
        RealName_One("201", "姓名与身份证号不匹配！", UserAuthType.RealName),
        RealName_Two("202", "查询不到身份信息！", UserAuthType.RealName),
        RealName_Three("203", "查询不到照片或照片不可用！", UserAuthType.RealName),
        RealName_Four("204", "人脸比对不一致！", UserAuthType.RealName),
        RealName_Five("205", "活体检测存在风险！", UserAuthType.RealName),
        RealName_Six("206", "业务策略限制！", UserAuthType.RealName),
        RealName_Seven("207", "人脸与身份证人脸比对不一致！", UserAuthType.RealName),
        RealName_Eight("209", "权威比对源异常！", UserAuthType.RealName),

        // 实人认证
        RealHead_One("204", "人脸比对不一致", UserAuthType.RealHead),
        RealHead_Two("205", "活体检测存在风险", UserAuthType.RealHead),
        RealHead_Three("206", "业务策略限制", UserAuthType.RealHead),
        ;

        private String code;
        private String msg;
        private UserAuthType type;

        VerifySubCode(String code, String msg, UserAuthType type) {
            this.code = code;
            this.msg = msg;
            this.type = type;
        }

        public static String getMesg(UserAuthType type, String code) {
            for (VerifySubCode item : VerifySubCode.values()) {
                if (item.type == type && item.code.equals(code)) {
                    return item.msg;
                }
            }
            return "未知错误！请稍后重试！";
        }
    }

}
