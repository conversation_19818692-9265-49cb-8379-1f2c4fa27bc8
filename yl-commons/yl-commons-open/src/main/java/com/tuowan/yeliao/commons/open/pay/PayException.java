/*
 * @(#) PayException.java 1.0.0 2016年1月29日 上午10:33:07
 */
package com.tuowan.yeliao.commons.open.pay;

/**
 * 支付异常
 * 
 * <AUTHOR>
 */
public class PayException extends RuntimeException {
	
	private static final long serialVersionUID = 1L;
	
	private Object data;

	public PayException(String msg, Throwable e){
		super(msg, e);
	}
	
	public PayException(String msg){
		super(msg);
	}
	
	public Object getData() {
		return data;
	}
	
	public PayException setData(Object data) {
		this.data = data;
		
		return this;
	}
	
	
	
}
