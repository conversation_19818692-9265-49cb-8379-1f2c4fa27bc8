package com.tuowan.yeliao.commons.open.image;

import com.aliyuncs.AcsResponse;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.RpcAcsRequest;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.exceptions.ServerException;
import com.aliyuncs.imageaudit.model.v20191230.ScanImageRequest;
import com.aliyuncs.imageaudit.model.v20191230.ScanImageResponse;
import com.aliyuncs.profile.DefaultProfile;
import com.aliyuncs.utils.StringUtils;
import com.easyooo.framework.common.util.JsonUtils;
import com.tuowan.yeliao.commons.config.configuration.impl.AliyunConfig;
import com.tuowan.yeliao.commons.config.configuration.impl.AppConfig;
import com.tuowan.yeliao.commons.core.exception.BusiException;
import com.tuowan.yeliao.commons.core.exception.InternalException;
import com.tuowan.yeliao.commons.open.dto.AliAuditDTO;
import com.tuowan.yeliao.commons.open.dto.AliAuditItemDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * 阿里云 图片支持
 */
@Component
public class AliImageSupport implements InitializingBean {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    /**
     * 图片支持客户端
     */
    private IAcsClient client = null;

    /**
     * 图片审核
     *
     * @param picPath 图片的相对路径
     * @throws Exception resp:{"data":{"results":[{"dataId":"5955aff0-c435-47ff-bc57-53a056160970","imageURL":"https://mpxmall.oss-cn-hangzhou.aliyuncs.com/mall/goods/cd867965c63747b9ab6019a06128839a.png","subResults":[{"frames":[],"hintWordsInfoList":[],"label":"normal","logoDataList":[],"oCRDataList":[],"programCodeDataList":[],"rate":99.9,"scene":"porn","sfaceDataList":[],"suggestion":"pass"},{"frames":[],"hintWordsInfoList":[],"label":"normal","logoDataList":[],"oCRDataList":[],"programCodeDataList":[],"rate":100.0,"scene":"terrorism","sfaceDataList":[],"suggestion":"pass"}]}]},"requestId":"A093EDEB-2A4A-5292-B145-0B6E35A7B26B"}
     *                   Suggestion = pass review block
     */
    public AliAuditDTO imageAudit(String picPath) {
        if (StringUtils.isEmpty(picPath)) {
            throw new InternalException("图片地址不能为空！");
        }
        ScanImageRequest req = new ScanImageRequest();
        List<String> scenes = new ArrayList<String>();
        scenes.add("porn"); // 色情
        scenes.add("terrorism"); // 恐怖
        req.setScenes(scenes);
        List<ScanImageRequest.Task> tasks = new ArrayList<ScanImageRequest.Task>();
        com.aliyuncs.imageaudit.model.v20191230.ScanImageRequest.Task task = new ScanImageRequest.Task();
        task.setDataId(UUID.randomUUID().toString());
        task.setImageURL(AppConfig.FILE_URL + "/" + picPath); // 这里需要图片的绝对路径
        tasks.add(task);
        req.setTasks(tasks);

        try {
            ScanImageResponse resp = getAcsResponse(req);
            log.info("AliImageSupport-imageAudit-info resp:{}", JsonUtils.seriazileAsString(resp));
            List<AliAuditItemDTO> dtoList = new ArrayList<>();
            List<ScanImageResponse.Data.Result> results = resp.getData().getResults();
            for (ScanImageResponse.Data.Result result : results) {
                List<ScanImageResponse.Data.Result.SubResult> subResults = result.getSubResults();
                for (ScanImageResponse.Data.Result.SubResult subResult : subResults) {
                    dtoList.add(AliAuditItemDTO.build1(subResult.getSuggestion(), subResult.getLabel()));
                }
            }
            return AliAuditDTO.build1(dtoList);
        } catch (Exception e) {
            throw new BusiException("图片审核出错！");
        }
    }

    private <R extends RpcAcsRequest<T>, T extends AcsResponse> T getAcsResponse(R req) throws Exception {
        try {
            return client.getAcsResponse(req);
        } catch (ServerException e) {
            // 服务端异常
            log.error("AliImageSupport ServerException: errCode={}, errMsg={}", e.getErrCode(), e.getErrMsg());
            throw e;
        } catch (ClientException e) {
            // 客户端错误
            log.error("AliImageSupport ClientException: errCode={}, errMsg={}", e.getErrCode(), e.getErrMsg());
            throw e;
        } catch (Exception e) {
            log.error("AliImageSupport Exception: msg={}", e.getMessage());
            throw e;
        }
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        DefaultProfile profile = DefaultProfile.getProfile(
                "cn-shanghai",             //默认 cn-shanghai
                AliyunConfig.ACCESS_KEY_ID,        //您的AccessKeyID
                AliyunConfig.ACCESS_KEY_SECRET);   //您的AccessKeySecret
        client = new DefaultAcsClient(profile);
    }
}
