/*!
 * Copyright 2018, Julun, Inc.
 */

package com.tuowan.yeliao.commons.open.log.logflow;


import com.tuowan.yeliao.commons.core.enums.general.user.UserType;
import com.tuowan.yeliao.commons.data.enums.general.ProcessResult;
import com.tuowan.yeliao.commons.open.log.logflow.entity.ServiceLog;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 请求端日志信息统一封装
 *
 * <AUTHOR>
 * @date 2018/6/13 13:08
 */
public class LogInfoRequestPart {
    /** 当前服务器IP */
    private String serverIp;
    /** userAgent信息 */
    private String userAgent;

    /** 请求参数 */
    private String reqId;
    private Long reqTime;
    private String headerInfo;
    private String clientIp;
    private String reqUrl;
    private String reqBody;

    /** 业务参数 */
    private Long uid;
    private UserType userType;
    private Long pid;

    /** 执行结果 */
    private ProcessResult result;
    private Integer errorCode;
    private String errorMsg;
    /** 执行耗时单位纳秒 */
    private Long execTime;

    /** JSON 字符串，不能序列化到数据库 */
    private transient String allHeaders;

    /** 是否包含外部服务调用 */
    private transient boolean containExternalInvoke;

    /** 业务日志 */
    private List<ServiceLog> serviceLogs = new ArrayList<>();

    /** 业务扩展参数 */
    private Map<String, Object> extMap = new HashMap<>();

    /** 用户归因使用 */
    private Long guestId;

    public String getServerIp() {
        return serverIp;
    }

    public void setServerIp(String serverIp) {
        this.serverIp = serverIp;
    }


    public String getUserAgent() {
        return userAgent;
    }

    public void setUserAgent(String userAgent) {
        this.userAgent = userAgent;
    }

    public String getReqId() {
        return reqId;
    }

    public void setReqId(String reqId) {
        this.reqId = reqId;
    }

    public Long getReqTime() {
        return reqTime;
    }

    public void setReqTime(Long reqTime) {
        this.reqTime = reqTime;
    }


    public String getHeaderInfo() {
        return headerInfo;
    }

    public void setHeaderInfo(String headerInfo) {
        this.headerInfo = headerInfo;
    }

    public String getClientIp() {
        return clientIp;
    }

    public void setClientIp(String clientIp) {
        this.clientIp = clientIp;
    }

    public String getReqUrl() {
        return reqUrl;
    }

    public void setReqUrl(String reqUrl) {
        this.reqUrl = reqUrl;
    }

    public Long getUid() {
        return uid;
    }

    public void setUid(Long uid) {
        this.uid = uid;
    }

    public UserType getUserType() {
        return userType;
    }

    public void setUserType(UserType userType) {
        this.userType = userType;
    }

    public Long getPid() {
        return pid;
    }

    public void setPid(Long pid) {
        this.pid = pid;
    }

    public String getReqBody() {
        return reqBody;
    }

    public void setReqBody(String reqBody) {
        this.reqBody = reqBody;
    }

    public ProcessResult getResult() {
        return result;
    }

    public void setResult(ProcessResult result) {
        this.result = result;
    }

    public Integer getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(Integer errorCode) {
        this.errorCode = errorCode;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    public Long getExecTime() {
        return execTime;
    }

    public void setExecTime(Long execTime) {
        this.execTime = execTime;
    }

    public String getAllHeaders() {
        return allHeaders;
    }

    public void setAllHeaders(String allHeaders) {
        this.allHeaders = allHeaders;
    }

    public List<ServiceLog> getServiceLogs() {
        return serviceLogs;
    }

    public boolean isContainExternalInvoke() {
        return containExternalInvoke;
    }

    public void setContainExternalInvoke(boolean containExternalInvoke) {
        this.containExternalInvoke = containExternalInvoke;
    }

    public void setServiceLogs(List<ServiceLog> serviceLogs) {
        this.serviceLogs = serviceLogs;
    }

    public Map<String, Object> getExtMap() {
        return extMap;
    }

    public void setExtMap(Map<String, Object> extMap) {
        this.extMap = extMap;
    }

    public Long getGuestId() {
        return guestId;
    }

    public void setGuestId(Long guestId) {
        this.guestId = guestId;
    }

    @Override
    public String toString() {
        return "LogInfoRequestPart{" +
                "serverIp='" + serverIp + '\'' +
                ", reqId=" + reqId +

                ", reqTime=" + reqTime +
                ", lmHeaderInfo='" + headerInfo + '\'' +
                ", clientIp='" + clientIp + '\'' +
                ", reqUrl='" + reqUrl + '\'' +
                ", uid=" + uid +
                ", guestId=" + guestId +
                ", userType=" + userType +
                ", pid=" + pid +
                ", reqBody='" + reqBody + '\'' +
                ", result=" + result +
                ", errorCode=" + errorCode +
                ", errorMsg='" + errorMsg + '\'' +
                ", execTime=" + execTime +
                ", allHeaders='" + allHeaders + '\'' +
                ", serviceLogs=" + serviceLogs +
                '}';
    }
}
