package com.tuowan.yeliao.commons.web.common.handler;

import com.alibaba.fastjson.support.spring.FastJsonJsonView;
import com.easyooo.framework.common.util.MsgUtils;
import com.easyooo.framework.validate.ValidException;
import com.tuowan.yeliao.commons.config.UnifiedConfig;
import com.tuowan.yeliao.commons.context.config.HeaderType;
import com.tuowan.yeliao.commons.context.config.Request;
import com.tuowan.yeliao.commons.core.enums.errcode.ErrCodeType;
import com.tuowan.yeliao.commons.core.exception.GeneralException;
import com.tuowan.yeliao.commons.core.exception.WebException;
import com.tuowan.yeliao.commons.open.log.logflow.BusiflowLogBindUtils;
import com.tuowan.yeliao.commons.open.log.logflow.LogInfoRequestPart;
import com.tuowan.yeliao.commons.web.common.util.ApiDataUtils;
import com.tuowan.yeliao.commons.web.common.view.PlainTextView;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerExceptionResolver;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

/**
 * 默认异常处理
 *
 * <AUTHOR>
 * @date 2018/6/12 14:58
 */
public class DefaultHandlerExceptionResolver implements HandlerExceptionResolver {

    /**
     * 内部提示性异常显示的堆栈条数
     */
    private static final Integer LOG_WARN_EXCEPTION_STACK_LIMIT = 3;

    private Logger LOG = LoggerFactory.getLogger(this.getClass());

    @Override
    public ModelAndView resolveException(HttpServletRequest request, HttpServletResponse response, Object handler, Exception e) {
        Request byRequest;
        if (handler instanceof HandlerMethod) {
            Method method = ((HandlerMethod) handler).getMethod();
            byRequest = method.getAnnotation(Request.class);
        } else {
            LOG.error(MsgUtils.format("Unauthorized request, reqUri: {}, reason: ", request.getRequestURI()), e);
            throw new WebException(ErrCodeType.Unauthorized);
        }
        ErrCodeType codeType;
        Object[] args = null;
        String message;
        if (e instanceof ValidException) {
            // 表单验证异常
            codeType = ErrCodeType.FormValidateError;
            message = e.getMessage();
        } else if (e instanceof GeneralException) {
            // 自定义异常
            GeneralException ex = (GeneralException) e;
            codeType = ex.getCodeType();
            message = MsgUtils.format(ex.getMessage(), ex.getArgs());
            args = ex.getArgs();
        } else {
            // 未知异常
            codeType = ErrCodeType.Unknown;
            message = codeType.getMessage();
        }
        String reqUrl = request.getRequestURL().toString();
        // 记录错误日志
        logError(codeType, reqUrl, e);
        // 业务流日志标记业务执行失败
        BusiflowLogBindUtils.failure(codeType.getErrCode(), message);

        Map<String, Object> attrMap = new HashMap<>(2);
        attrMap.put("code", codeType.getErrCode());
        attrMap.put("message", message);
        // 如果白名单请求，说明是内部服务调用，需传递错误类型和参数，交给上一层处理
        if (HeaderType.Whitelist == byRequest.header()) {
            attrMap.put("codeType", codeType);
            attrMap.put("args", args);
        }
        // 将返回值加密，如果返回null，说明不需要加密
        byte[] data = ApiDataUtils.encryptData(attrMap, response);
        if (data == null) {
            FastJsonJsonView jsonView = new FastJsonJsonView();
            jsonView.setAttributesMap(attrMap);
            return new ModelAndView(jsonView);
        }
        return new ModelAndView(new PlainTextView(data));
    }

    /**
     * 输出异常信息
     *
     * @param codeType
     * @param url
     * @param e
     */
    private void logError(ErrCodeType codeType, String url, Exception e) {
        switch (codeType) {
            case Unknown: {
                if (url.endsWith("/error")) {
                    return;
                }
                if (e.getMessage() != null && e.getMessage().contains("Broken pipe")) {
                    return;
                }
                LogInfoRequestPart part = BusiflowLogBindUtils.get();
                if (part == null) {
                    LOG.error("Internal error, reason: ", e);
                    break;
                }
                StringBuffer buffer = new StringBuffer("A unknown exception occurs in the ");
                buffer.append(url);
                buffer.append("\nreqId: ").append(part.getReqId());
                buffer.append("\nheaderInfo: ").append(part.getHeaderInfo());
                buffer.append("\nreqBody: ").append(BusiflowLogBindUtils.getRequestBody());
                // 暂时处理上报至阿里云日志具体原因不显示的问题
                if (UnifiedConfig.isProdEnv() && e.getCause() != null) {
                    buffer.append("\ncausedBy: ").append(e.getCause().getMessage());
                }
                LOG.error(buffer.toString(), e);
                break;
            }
            case FormValidateError: {
                LogInfoRequestPart part = BusiflowLogBindUtils.get();
                StringBuffer buffer = new StringBuffer("Form validate exception in the ");
                buffer.append(url);
                buffer.append("\nreqId: ").append(part.getReqId());
                buffer.append("\nheader: ").append(part.getHeaderInfo());
                LOG.error(buffer.toString(), e);
                break;
            }
            default: {
                StringBuffer buf = new StringBuffer("\tInvokeStack: ");
                for (int i = 0; i < e.getStackTrace().length && i < LOG_WARN_EXCEPTION_STACK_LIMIT; i++) {
                    StackTraceElement ste = e.getStackTrace()[i];
                    buf.append(ste.getFileName());
                    buf.append("#");
                    buf.append(ste.getMethodName());
                    buf.append(":");
                    buf.append(ste.getLineNumber());
                    buf.append(" -> ");
                }
                LOG.info("Tips: {} Url: {} \n{}", e.getMessage(), url, buf.toString());
                break;
            }
        }
    }
}
