package com.tuowan.yeliao.commons.web.common.util;

import com.easyooo.framework.common.util.*;
import com.tuowan.yeliao.commons.config.UnifiedConfig;
import com.tuowan.yeliao.commons.config.configuration.impl.ApiKeyConfig;
import com.tuowan.yeliao.commons.core.enums.general.ClientType;
import com.tuowan.yeliao.commons.data.dto.common.ApiDataDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpOutputMessage;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.security.PrivateKey;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * 接口数据处理工具类
 *
 * <AUTHOR>
 * @date 2021/3/10 16:50
 */
public class ApiDataUtils {

    private final static Logger LOG = LoggerFactory.getLogger(ApiDataUtils.class);

    /** 请求标识ID，客户端类型-+UUID，例如I-2a9f3426195d4c339d1bfdfe2752834d，如果为空则不需要解密 */
    private static final String X_TID = "x-tid";
    /** 数据解密密钥(经RSA加密的字符串) */
    private static final String X_ARG = "x-arg";
    /** 请求头信息(如果请求标识不为空，则需要解密) */
    private static final String X_INF = "x-inf";

    private static ThreadLocal<ApiDataDTO> data = new ThreadLocal<>();

    /** 客户端ApiKey定义 */
    private static Map<ClientType, PrivateKey> apiKeyMap;

    static {
        apiKeyMap = new HashMap<>();
        apiKeyMap.put(ClientType.H5, ApiKeyConfig.H5);
        apiKeyMap.put(ClientType.iOS, ApiKeyConfig.IOS);
        apiKeyMap.put(ClientType.Android, ApiKeyConfig.ANDROID);
        apiKeyMap.put(ClientType.Robot, ApiKeyConfig.ROBOT_PRI);
    }

    /**
     * 解析请求头信息
     *
     * @param request
     * @return
     */
    public static String parseHeaderInfo(HttpServletRequest request) {
        String tid = request.getHeader(X_TID);
        String headerInfo = request.getHeader(X_INF);
        // 如果非生产环境，当请求标识为空时，则无需解密数据
        if ((!UnifiedConfig.isProdEnv()) && StringUtils.isEmpty(tid)) {
            data.set(ApiDataDTO.create(headerInfo));
            return headerInfo;
        }
        // cms不进行加密解密
        if (StringUtils.isNotEmpty(headerInfo) && headerInfo.contains("&t=C")) {
            data.set(ApiDataDTO.create(headerInfo));
            return headerInfo;
        }
        // h5不进行加密解密
        if (StringUtils.isNotEmpty(headerInfo) && headerInfo.contains("&t=H")) {
            data.set(ApiDataDTO.create(headerInfo));
            return headerInfo;
        }
        // 数据加密密钥
        String dataKey = request.getHeader(X_ARG);
        if (StringUtils.isEmpty(dataKey)) {
            LOG.debug("没有获取到x-arg参数");
            return null;
        }
        // 根据客户端类型获取私钥
        PrivateKey privateKey = ApiDataUtils.getPrivateKey(tid);
        if (privateKey == null) {
            LOG.info("没有获取到私钥：{}", tid);
            return null;
        }
        // 根据私钥解密数据加密字符串
        dataKey = RSAUtils.decrypt(dataKey, privateKey);
        if (dataKey == null) {
            LOG.info("解密失败");
            return null;
        }
        headerInfo = EncryptUtils.decryptByAESWithECB(headerInfo, dataKey);
        data.set(ApiDataDTO.create(tid, dataKey, headerInfo));
        return headerInfo;
    }

    /**
     * 获取当前请求头信息
     *
     * @return
     */
    public static String getHeaderInfo() {
        ApiDataDTO dto = data.get();
        if (dto == null) {
            return null;
        }
        return dto.getHeaderInfo();
    }

    /**
     * 移除自定义请求头参数
     *
     * @param headerMap
     */
    public static void removeHeaderKeys(Map<String, String> headerMap) {
        headerMap.remove(X_ARG);
        headerMap.remove(X_TID);
        headerMap.remove(X_INF);
    }

    /**
     * 解密数据
     *
     * @param content
     * @return
     */
    public static String decryptData(String content) {
        ApiDataDTO dto = data.get();
        if (dto == null || dto.getTid() == null) {
            return content;
        }
        if (content == null || "{}".equals(content)) {
            return "{}";
        }
        String data = JsonUtils.getJsonString(content, "data");
        return EncryptUtils.decryptByAESWithECB(data, dto.getDataKey());
    }

    /**
     * 设置接口响应请求标识
     *
     * @param object
     * @param message
     */
    public static byte[] encryptData(Object object, HttpOutputMessage message) {
        ApiDataDTO dto = data.get();
        if (dto == null || dto.getTid() == null) {
            return null;
        }
        message.getHeaders().add(X_TID, dto.getTid());
        message.getHeaders().put("Content-Type", Arrays.asList("text/plain"));
        return EncryptUtils.encryptByAESWithECB(JsonUtils.seriazileAsString(object), dto.getDataKey()).getBytes();
    }

    /**
     * 设置接口响应请求标识
     *
     * @param object
     * @param response
     */
    public static byte[] encryptData(Object object, HttpServletResponse response) {
        ApiDataDTO dto = data.get();
        if (dto == null || dto.getTid() == null) {
            return null;
        }
        response.addHeader(X_TID, dto.getTid());
        response.setContentType("text/plain");
        return EncryptUtils.encryptByAESWithECB(JsonUtils.seriazileAsString(object), dto.getDataKey()).getBytes();
    }

    /**
     * 获取当前的请求标识
     *
     * @return
     */
    public static String getTid() {
        return data.get().getTid();
    }

    /**
     * 释放线程共享数据
     */
    public static void release() {
        if (data.get() == null) {
            return;
        }
        data.remove();
    }

    /**
     * 根据请求标识获取私钥
     *
     * @param tid
     * @return
     */
    private static PrivateKey getPrivateKey(String tid) {
        if (StringUtils.isEmpty(tid)) {
            return null;
        }
        // 请求标识第一个字母表示客户端类型
        ClientType clientType = EnumUtils.byId(tid.charAt(0) + "", ClientType.class);
        if (clientType == null) {
            return null;
        }
        return apiKeyMap.get(clientType);
    }
}
