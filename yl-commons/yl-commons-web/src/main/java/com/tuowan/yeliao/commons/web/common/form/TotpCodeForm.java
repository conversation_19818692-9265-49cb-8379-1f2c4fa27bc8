package com.tuowan.yeliao.commons.web.common.form;

import com.easyooo.framework.validate.config.LMNotEmpty;

/**
 * 动态口令表单
 *
 * <AUTHOR>
 * @date 2022/1/19 13:26
 */
public class TotpCodeForm implements Form {

    /** 动态口令 */
    @LMNotEmpty
    private String code;
    /** 操作类型 0、删除索引 1、创建索引 2、初始化数据 */
    @LMNotEmpty
    private String type;

    private String data;

    public static TotpCodeForm build(String type, String code) {
        TotpCodeForm form = new TotpCodeForm();
        form.setType(type);
        form.setCode(code);
        return form;
    }

    public static TotpCodeForm build(String type, String code, String data) {
        TotpCodeForm form = new TotpCodeForm();
        form.setType(type);
        form.setCode(code);
        form.setData(data);
        return form;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }
}
