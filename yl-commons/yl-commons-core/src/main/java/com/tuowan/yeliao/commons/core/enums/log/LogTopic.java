/*!
 * Copyright 2018, Julun, Inc.
 */

package com.tuowan.yeliao.commons.core.enums.log;

/**
 * 日志主题进行分类，
 *
 * <AUTHOR>
 * @date 2018/6/20 16:12
 */
public enum LogTopic {
    /** 流式日志主题 */
    Req,
    /** 消息队列(主动发送) */
    MQ,
    /** 流式日志消费端主题 */
    ReqConsumeResult,
    /** 消费者信息错误 */
    ErrorConsumer,
    /** Job */
    ErrorJob,
    /** 短信API，已废弃 since hotfix-19.0-smsPush */
    ApiLogSms,
    /** 新版短信API，since hotfix-19.0-smsPush */
    ApiLogSmsNew,
    /** http短信状态报告 */
    HttpSmsReport,
    /** 大汉三通短信状态报告 */
    DhstSmsReport,
    /** 数美文本过滤API */
    ApiLogShumeiText,
    /** 融云消息API */
    ApiRongCloudMessage,
    /** 用户直播间观看日志 */
    RoomUserWatchLog,
    /** Jpush API */
    ApiJpushLog,
    /** 用户直播间点击 */
    UserRoomClick,
    /** 用户经验异动 */
    UserExpChange,
    /** 用户魅力值异动*/
    CharmExpChange,
    /** 用户归因日志 */
    UserOne,
    /** 阿里认证日志 */
    AliCertification,
    ;
    private String lowerCaseName;

    private LogTopic() {
        lowerCaseName = this.name().toLowerCase();
    }

    // store名称只支持小写
    public String getLowerCaseName() {
        return lowerCaseName;
    }
}
