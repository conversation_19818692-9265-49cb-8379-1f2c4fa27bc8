package com.tuowan.yeliao.commons.core.constant;

/**
 * 字段名称定义，可用于Redis hash结构的Key定义
 *
 * <AUTHOR>
 * @date 2020/7/10 19:49
 */
public interface FieldConstant {

    // 通用
    /** 通话类型 */
    String CALL_TYPE = "callType";
    /** 开始时间 */
    String START_TIME = "startTime";
    /** 下一次时间 */
    String NEXT_TIME = "nextTime";
    /** 上一次扣费持续到多久 */
    String EXPIRE_TIME = "expireTime";
    /** 付费用户ID */
    String BILL_USER_ID = "billUserId";
    /** 金币 */
    String BEANS = "beans";

    /** 在线时长 */
    String ONLINE_DURATION = "onlineDuration";


    // 女用户
    /** 女用户收到消息数 */
    String FEMALE_RECEIVE_MSG_NUM = "femaleReceiveMsgNum";

    /** 女用户有效回复消息数 */
    String FEMALE_VALID_REPLY_NUM = "femaleValidReplyNum";

    /** 女用户主动搭讪次数 */
    String FEMALE_ACT_CU_NUM = "femaleActCuNum";

    /** 女用户系统搭讪次数 */
    String FEMALE_SYS_CU_NUM = "femaleSysCuNum";

    /** 女用户视频邀请次数 */
    String FEMALE_VIDEO_INVITE_NUM = "femaleVideoInviteNum";

    /** 女用户语音邀请次数 */
    String FEMALE_VOICE_INVITE_NUM = "femaleVoiceInviteNum";

    /** 女用户视频匹配曝光次数 */
    String FEMALE_VM_BG_NUM = "femaleVmBgNum";

    /** 女用户视频缘分曝光次数 */
    String FEMALE_VF_BG_NUM = "femaleVfBgNum";

    /** 每天第一次漏接通话ID */
    String DAY_FIRST_MISS_CALL_ID = "firstMissCallId";

    /** 每天新增主动私聊男用户 */
    String FEMALE_ACT_MSG_NEW_MALE = "femaleActMsgNewMale";

    // 【视频】
    /** 视频通话次数（响铃了） */
    String FEMALE_VC_TIMES = "femaleVcTimes";
    /** 视频通话接通次数 */
    String FEMALE_VC_SUC_TIMES = "femaleVcSucTimes";
    /** 视频通话时长（秒） */
    String FEMALE_VC_DURATION = "femaleVcDuration";
    /** 视频通话成功人数 */
    String FEMALE_VC_USERS = "femaleVcUsers";
    // 视频速配
    /** 匹配成功次数（响铃了） */
    String FEMALE_VM_TIMES = "femaleVmTimes";
    /** 匹配接通次数 */
    String FEMALE_VM_SUC_TIMES = "femaleVmSucTimes";
    /** 匹配通话时长 */
    String FEMALE_VM_DURATION = "femaleVmDuration";
    /** 匹配通话成功人数 */
    String FEMALE_VM_USERS = "femaleVmUsers";
    // 视频邀请
    /** 邀请成功次数（响铃了）*/
    String FEMALE_VI_TIMES = "femaleViTimes";
    /** 邀请接通次数 */
    String FEMALE_VI_SUC_TIMES = "femaleViSucTimes";
    /** 邀请通话时长 */
    String FEMALE_VI_DURATION = "femaleViDuration";
    /** 邀请通话成功人数 */
    String FEMALE_VI_USERS = "femaleViUsers";
    // 视频缘分
    /** 视频缘分成功次数（响铃了）*/
    String FEMALE_VF_TIMES = "femaleVfTimes";
    /** 视频缘分接通次数 */
    String FEMALE_VF_SUC_TIMES = "femaleVfSucTimes";
    /** 视频缘分通话时长 */
    String FEMALE_VF_DURATION = "femaleVfDuration";
    /** 视频缘分通话成功人数 */
    String FEMALE_VF_USERS = "femaleVfUsers";

    // 【语音】
    /** 语音通话次数（响铃了） */
    String FEMALE_OC_TIMES = "femaleOcTimes";
    /** 语音通话接通次数 */
    String FEMALE_OC_SUC_TIMES = "femaleOcSucTimes";
    /** 语音通话时长 */
    String FEMALE_OC_DURATION = "femaleOcDuration";
    /** 语音通话成功人数 */
    String FEMALE_OC_USERS = "femaleOcUsers";
    // 语音邀请
    /** 邀请成功次数（响铃了）*/
    String FEMALE_OI_TIMES = "femaleOiTimes";
    /** 邀请接通次数 */
    String FEMALE_OI_SUC_TIMES = "femaleOiSucTimes";
    /** 邀请通话时长 */
    String FEMALE_OI_DURATION = "femaleOiDuration";
    /** 邀请通话成功人数 */
    String FEMALE_OI_USERS = "femaleOiUsers";

    /** 主动挂断次数 《包含 正常通话 + 匹配 + 邀请 + 缘分》*/
    String FEMALE_ACT_HU_TIMES = "femaleActHuTimes";
    /** 10秒内通话次数 */
    String FEMALE_LOW_10S_CALL_TIMES = "femaleLow10sCallTimes";
    /** 60秒内通话次数 */
    String FEMALE_LOW_60S_CALL_TIMES = "femaleLow60sCallTimes";
    /** 拒接通话次数 */
    String FEMALE_RF_CALL_TIMES = "femaleRfCallTimes";

    // 【礼物】
    /** 收到金币礼物个数 */
    String FEMALE_BEAN_GIFT_NUM = "femaleBeanGiftNum";
    /** 收到银币礼物个数 */
    String FEMALE_SILVER_GIFT_NUM = "femaleSilverGiftNum";

    // 男用户
    /** 视频速配发起人ID */
    String VIDEO_MATCH_USER = "user";
    /** 视频速配接可接收人合集 */
    String VIDEO_MATCH_FRIENDS = "friends";

    /** 男用户今日派发视频缘分标识 */
    String DIS_VIDEO_FATE = "disVideoFate";
}
