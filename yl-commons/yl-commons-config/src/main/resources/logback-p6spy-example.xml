<?xml version="1.0" encoding="UTF-8"?>
<!-- 
    P6Spy SQL日志配置示例
    将此配置添加到您的logback-spring.xml或logback.xml中
-->
<configuration>
    
    <!-- 控制台输出配置 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <!-- SQL日志文件输出配置 -->
    <appender name="SQL_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/sql.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>logs/sql.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <maxHistory>30</maxHistory>
            <totalSizeCap>3GB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} - %msg%n</pattern>
        </encoder>
    </appender>

    <!-- 环境特定配置 -->
    <springProfile name="dev">
        <!-- 开发环境：控制台和文件都输出SQL日志 -->
        <logger name="p6spy" level="INFO" additivity="false">
            <appender-ref ref="CONSOLE"/>
            <appender-ref ref="SQL_FILE"/>
        </logger>
        
        <!-- 显示详细的数据库连接信息 -->
        <logger name="com.tuowan.yeliao.commons.config.mybatis" level="INFO"/>
    </springProfile>

    <springProfile name="test">
        <!-- 测试环境：只输出到文件 -->
        <logger name="p6spy" level="INFO" additivity="false">
            <appender-ref ref="SQL_FILE"/>
        </logger>
        
        <!-- 显示数据库连接信息 -->
        <logger name="com.tuowan.yeliao.commons.config.mybatis" level="INFO"/>
    </springProfile>

    <springProfile name="prod">
        <!-- 生产环境：不输出SQL日志 -->
        <logger name="p6spy" level="OFF"/>
        
        <!-- 生产环境下只显示错误级别的数据库信息 -->
        <logger name="com.tuowan.yeliao.commons.config.mybatis" level="ERROR"/>
    </springProfile>

    <!-- 根日志级别 -->
    <root level="INFO">
        <appender-ref ref="CONSOLE"/>
    </root>

</configuration>
