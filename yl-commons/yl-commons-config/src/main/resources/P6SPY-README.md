# P6Spy SQL监控配置说明

## 概述

本项目已集成P6Spy 3.9.1，用于在开发和测试环境下监控SQL语句的执行情况，包括SQL语句内容和执行时长。

## 功能特性

- ✅ **环境隔离**: 仅在dev和test环境下启用，生产环境不受影响
- ✅ **自动检测**: 自动检测p6spy依赖是否存在，无依赖时自动降级
- ✅ **环境配置**: 支持不同环境使用不同的配置文件
- ✅ **性能监控**: 显示SQL执行时间，便于性能优化
- ✅ **日志集成**: 与项目现有的SLF4J日志系统集成

## 环境配置

### 开发和测试环境 (dev/test)
- 配置文件: `spy.properties`
- 统一的配置，适用于所有非生产环境
- 日志格式: `[SQL] 时间 | 执行时间: XXXms | 连接: XX | SQL: XXX`

### 生产环境 (prod)
- 不启用p6spy，保持原有性能

## 使用方法

### 1. 激活环境
使用Maven profiles激活对应环境：

```bash
# 开发环境 (默认)
mvn clean compile -Pdev

# 测试环境
mvn clean compile -Ptest

# 生产环境 (不包含p6spy)
mvn clean compile -Pprod
```

### 2. 查看SQL日志
启动应用后，SQL语句会自动输出到日志中：

```
[SQL] 2025-07-04 10:30:15 | 执行时间: 25ms | 连接: 1 | SQL: SELECT * FROM t_user WHERE user_id = 12345
[SQL] 2025-07-04 10:30:16 | 执行时间: 5ms | 连接: 1 | SQL: UPDATE t_user SET last_login_time = '2025-07-04 10:30:16' WHERE user_id = 12345
```

## 配置文件说明

### spy.properties (统一配置)
- 适用于所有非生产环境（dev和test）
- 统一的日志格式和配置
- 简化维护，避免配置分散

## 自定义配置

如需修改配置，可以编辑`spy.properties`文件：

1. **修改执行时间阈值**:
   ```properties
   # 只记录执行时间超过50ms的SQL
   executionThreshold=50
   ```

2. **修改日志格式**:
   ```properties
   customLogMessageFormat=自定义格式: %(currentTime) | %(executionTime)ms | %(sqlSingleLine)
   ```

3. **过滤特定SQL**:
   ```properties
   filter=true
   exclude=SELECT 1,SHOW TABLES
   ```

## 注意事项

1. **性能影响**: p6spy会对性能产生一定影响，因此只在非生产环境启用
2. **日志量**: 开发环境下会产生大量SQL日志，注意日志文件大小
3. **敏感信息**: SQL日志可能包含敏感数据，注意日志安全
4. **依赖管理**: p6spy依赖只在dev和test profile中包含，生产环境不会引入

## 故障排除

### 1. p6spy未生效
- 检查是否使用了正确的Maven profile
- 确认p6spy依赖已正确引入
- 查看启动日志中是否有p6spy相关信息

### 2. 配置文件未找到
- 确认spy.properties文件在classpath中
- 检查文件名和路径是否正确

### 3. 日志未输出
- 检查日志级别配置
- 确认使用了SLF4J appender
- 查看是否被excludecategories过滤

## 版本信息

- P6Spy版本: 3.9.1
- 支持的数据库: MySQL, PostgreSQL, Oracle等
- Java版本要求: JDK 8+
