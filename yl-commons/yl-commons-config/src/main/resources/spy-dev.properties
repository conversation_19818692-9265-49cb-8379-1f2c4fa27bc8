#################################################################
# P6Spy Options File for Development Environment               #
# 开发环境专用的P6Spy配置文件                                    #
#################################################################

# 模块列表
modulelist=com.p6spy.engine.spy.P6SpyFactory,com.p6spy.engine.logging.P6LogFactory

# 自动刷新每个语句
autoflush = true

# 日期格式
dateformat=yyyy-MM-dd HH:mm:ss

# 不打印堆栈跟踪（开发环境下通常不需要）
stacktrace=false

# 不重新加载配置文件
reload=false

# 使用SLF4J日志记录器，这样可以与项目的日志配置集成
appender=com.p6spy.engine.spy.appender.Slf4JLogger

# 使用自定义格式
logMessageFormat=com.p6spy.engine.spy.appender.CustomLineFormat

# 通过JMX暴露选项
jmx=true

# 排除的日志类别（开发环境下只关注SQL语句和错误）
excludecategories=info,debug,result,batch

# 自定义日志消息格式 - 开发环境下显示详细信息
customLogMessageFormat=[SQL-DEV] %(currentTime) | 执行时间: %(executionTime)ms | 连接: %(connectionId) | SQL: %(sqlSingleLine)

# 执行时间阈值（毫秒）- 开发环境下记录所有SQL
executionThreshold=0

# 过滤器设置 - 开发环境下不过滤
filter=false
