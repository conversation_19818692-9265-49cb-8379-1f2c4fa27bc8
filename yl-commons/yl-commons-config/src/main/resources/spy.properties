#################################################################
# P6Spy Options File                                            #
# See documentation for detailed instructions                  #
# http://p6spy.readthedocs.io/en/latest/configandusage.html   #
#################################################################

#################################################################
# MODULES                                                       #
#                                                               #
# Module list adapts the modular functionality of P6Spy.      #
# Only modules listed are active.                             #
# (default is com.p6spy.engine.logging.P6LogFactory and       #
# com.p6spy.engine.spy.P6SpyFactory)                          #
# Please note that the core module (P6SpyFactory) can't be    #
# deactivated.                                                 #
# Unlike the other properties, activation of the changes on   #
# this one requires reload.                                    #
#################################################################
modulelist=com.p6spy.engine.spy.P6SpyFactory,com.p6spy.engine.logging.P6LogFactory

#################################################################
# CORE (P6SPY) PROPERTIES                                      #
#################################################################

# A comma separated list of JDBC drivers to load and register.
# (default is empty)
#
# Note: This is normally only needed when using P6Spy in an
# application server environment with a JNDI data source or when
# using a JDBC driver that does not implement the JDBC 4.0 API
# (specifically automatic registration).
driverlist=

# for flushing per statement
# (default is false)
autoflush=true

# sets the date format using Java's SimpleDateFormat routine.
# In case property is not set, milliseconds since 1970 (unix time) is used (default is empty)
dateformat=yyyy-MM-dd HH:mm:ss

# prints a stack trace for every statement logged
stacktrace=false
# if stacktrace=true, specifies the stack trace to print
stacktraceclass=

# determines if property file should be reloaded
# Please note: reload means forgetting all the previously set
# settings (even those set during runtime - via JMX)
# and starting with the clean table
# (default is false)
reload=false

# determines how often should be reloaded in seconds
# (default is 60)
reloadinterval=60

# specifies the appender to use for logging
# Please note: reload means forgetting all the previously set
# settings (even those set during runtime - via JMX)
# and starting with the clean table
# (only the properties read from the configuration file)
# (default is com.p6spy.engine.spy.appender.FileAppender)
appender=com.p6spy.engine.spy.appender.Slf4JLogger

# name of logfile to use, note Windows users should make sure to use forward slashes in their pathname (e:/test/spy.log)
# (used for com.p6spy.engine.spy.appender.FileAppender only)
# (default is spy.log)
logfile=spy.log

# append to the p6spy log file. if this is set to false the
# log file is truncated every time. (file logger only)
# (default is true)
append=true

# class to use for formatting log messages (default is: com.p6spy.engine.spy.appender.SingleLineFormat)
logMessageFormat=com.p6spy.engine.spy.appender.CustomLineFormat

# Custom log message format used ONLY IF logMessageFormat is set to com.p6spy.engine.spy.appender.CustomLineFormat
# default is %(currentTime)|%(executionTime)|%(category)|connection%(connectionId)|%(sqlSingleLine)
# Available placeholders:
#   %(connectionId)            the id of the connection
#   %(currentTime)             the current time expressing in milliseconds
#   %(executionTime)           the time in milliseconds that the operation took to complete
#   %(category)                the category of the operation
#   %(effectiveSql)            the SQL statement as submitted to the driver
#   %(effectiveSqlSingleLine)  the SQL statement as submitted to the driver, with all new lines removed
#   %(sql)                     the SQL statement with all bind variables replaced with actual values
#   %(sqlSingleLine)           the SQL statement with all bind variables replaced with actual values, with all new lines removed
customLogMessageFormat=%(currentTime) | %(executionTime)ms | %(category) | connection%(connectionId) | %(sqlSingleLine)

# format that is used for logging of the java.util.Date implementations (has to be compatible with java.text.SimpleDateFormat)
# (default is dd-MMM-yy)
databaseDialectDateFormat=yyyy-MM-dd

# format that is used for logging of the java.sql.Time implementations (has to be compatible with java.text.SimpleDateFormat)
# (default is hh:mm:ss a)
databaseDialectTimeFormat=HH:mm:ss

# format that is used for logging of the java.sql.Timestamp implementations (has to be compatible with java.text.SimpleDateFormat)
# (default is dd-MMM-yy hh:mm:ss a)
databaseDialectTimestampFormat=yyyy-MM-dd HH:mm:ss

# whether to expose options via JMX or not
# (default is true)
jmx=true

# if exposing options via jmx (see option above), what should be the prefix used?
# jmx naming pattern constructed is: com.p6spy(.<jmxPrefix>)?:name=<optionsClassName>
# please note, if there is already such a name in use it would be unregistered first (the last registered wins)
# (default is none)
jmxPrefix=

# if set to true, the execution time will be measured in nanoseconds as opposed to milliseconds
# (default is false)
useNanoTime=false

#################################################################
# DataSource replacement                                        #
#                                                               #
# Replace the real DataSource class in your application server #
# configuration with the name com.p6spy.engine.spy.P6DataSource#
# (that provides also connection pooling and xa support).      #
# then add the JNDI name and class name of the real            #
# DataSource here                                               #
#                                                               #
# Values set in this item cannot be reloaded using the         #
# reloadproperties variable. Once it is loaded, it remains     #
# in memory until the application is restarted.               #
#                                                               #
#################################################################
#realdatasource=/RealMySqlDS
#realdatasourceclass=com.mysql.jdbc.jdbc2.optional.MysqlDataSource

#################################################################
# DataSource properties                                         #
#                                                               #
# If you are using the DataSource support to intercept calls   #
# to a DataSource that does not use P6Spy's driver class,      #
# then you can use the following properties to set up the      #
# DataSource that P6Spy gets data from.                        #
#                                                               #
# The two standard DataSource properties are:                  #
#                                                               #
# driverName - the name of the JDBC driver class               #
# url - the JDBC URL                                           #
#                                                               #
# If your DataSource class needs values for other properties,  #
# such as your database user ID and password, you can          #
# specify them here:                                            #
#                                                               #
# user - the database user ID                                  #
# password - the database password                             #
#################################################################
#driverName=com.mysql.jdbc.Driver
#url=********************************
#user=root
#password=

#################################################################
# JNDI DataSource lookup                                        #
#                                                               #
# If you are using the DataSource support outside of an        #
# application server, you will probably need to define the     #
# JNDI Context environment.                                     #
#                                                               #
# If the P6Spy code will be executing inside an app server then#
# do not use these properties, and the DataSource lookup will  #
# use the naming context defined by the app server.            #
#                                                               #
# The two standard elements of the naming environment are:     #
#                                                               #
# initialContextFactory - the class name of the                #
#   JNDI initial context factory                               #
# providerURL - the URL of the JNDI provider                   #
#                                                               #
# If you need additional elements, you can specify them here:  #
#################################################################
#initialContextFactory=org.apache.naming.java.javaURLContextFactory
#providerURL=

#################################################################
# P6 LOGGING SPECIFIC PROPERTIES                               #
#################################################################

# filter what is logged
# please note this is a precondition for usage of: include/exclude/sqlexpression
# (default is false)
filter=false

# comma separated list of strings to include
# please note that special characters escaping (used in java) has to be done for the provided regular expression
# (default is empty)
include=

# comma separated list of strings to exclude
# please note that special characters escaping (used in java) has to be done for the provided regular expression
# (default is empty)
exclude=

# sql expression to evaluate if using regex
# please note that special characters escaping (used in java) has to be done for the provided regular expression
# (default is empty)
sqlexpression=

#list of categories to exclude: error, info, batch, debug, statement,
#commit, rollback, result and resultset are valid values
# (default is info,debug,result,resultset,batch)
excludecategories=info,debug,result,resultset,batch

# Execution threshold applies to the standard logging of P6Spy.
# While the standard logging logs out every statement
# regardless of its execution time, this feature puts a time
# condition on that logging. Only statements that have taken
# longer than the time specified (in milliseconds) will be
# logged. This way it is possible to see only statements that
# have exceeded some high water mark.
# This time is reloadable.
#
# executionThreshold=integer time (milliseconds)
# (default is 0)
executionThreshold=0
