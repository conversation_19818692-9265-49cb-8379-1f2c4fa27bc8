package com.tuowan.yeliao.commons.config.configuration.impl;

import com.easyooo.framework.common.util.PropUtils;
import com.tuowan.yeliao.commons.config.UnifiedConfig;

import java.util.Properties;

/**
 * 昂网配置
 *
 * <AUTHOR>
 * @date 2018/6/13 14:42
 */
public class JmSmsConfig {

    /** 验证码模板 */
    public static final String TEMPLATE;
    /** 女用户审核通过模板 */
    public static final String PASS_TEMPLATE;
    /** 工猫电签验证码 */
    public static final String INTERVIEW_TEMPLATE;
    /** 企业代码 */
    public static final String SPID;
    /**  登录账号 */
    public static final String USERNAME;
    /** 登录密码 */
    public static final String PASSWORD;
    /** 通道接入码 */
    public static final String CHANNEL_CODE;

    static {
        Properties props = UnifiedConfig.getProperties();
        TEMPLATE = PropUtils.getString(props, "jmsms.template");
        PASS_TEMPLATE = PropUtils.getString(props, "jmsms.passTemplate");
        INTERVIEW_TEMPLATE = PropUtils.getString(props, "jmsms.interviewTemplate");
        SPID = PropUtils.getString(props, "jmsms.spid");
        USERNAME = PropUtils.getString(props, "jmsms.username");
        PASSWORD = PropUtils.getString(props, "jmsms.password");
        CHANNEL_CODE = PropUtils.getString(props, "jmsms.ac");
    }
}
