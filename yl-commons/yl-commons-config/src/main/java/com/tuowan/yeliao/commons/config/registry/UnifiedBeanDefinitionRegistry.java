package com.tuowan.yeliao.commons.config.registry;

import com.easyooo.framework.common.util.EnumUtils;
import com.easyooo.framework.support.bean.BeanCreateInfo;
import com.easyooo.framework.support.bean.BeanDefinitionFactory;
import com.easyooo.framework.support.redis.jedis.JedisClientFactoryBean;
import com.tuowan.yeliao.commons.config.UnifiedConfig;
import com.tuowan.yeliao.commons.config.configuration.ServiceConfig;
import com.tuowan.yeliao.commons.config.configuration.impl.RedisSourceConfig;
import com.tuowan.yeliao.commons.config.enums.RedisType;
import com.tuowan.yeliao.commons.config.redis.RedisDataSource;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanDefinitionHolder;
import org.springframework.beans.factory.config.ConfigurableListableBeanFactory;
import org.springframework.beans.factory.support.BeanDefinitionReaderUtils;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.beans.factory.support.BeanDefinitionRegistryPostProcessor;
import org.springframework.context.annotation.ClassPathBeanDefinitionScanner;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 统一配置实例自动注册器
 *
 * <AUTHOR>
 * @date 2022/4/13 13:22
 */
@Component
public class UnifiedBeanDefinitionRegistry implements BeanDefinitionRegistryPostProcessor {

    @Override
    public void postProcessBeanDefinitionRegistry(BeanDefinitionRegistry beanDefinitionRegistry) throws BeansException {
        // 注册Redis实例对象
        boolean isProdEnv = UnifiedConfig.isProdEnv();
        Class<?> beanClass = JedisClientFactoryBean.class;
        RedisSourceConfig config = UnifiedConfig.getRedisSourceConfig();
        for (String name : config.getNames()) {
            RedisType redisType = EnumUtils.byName(name, RedisType.class);
            RedisDataSource dataSource = new RedisDataSource(redisType, config.getProps(), isProdEnv);
            BeanCreateInfo beanInfo = new BeanCreateInfo(beanClass, redisType.getBeanName(), dataSource.toProps());
            BeanDefinitionHolder definitionHolder = BeanDefinitionFactory.createBeanDefinition(beanInfo);
            BeanDefinitionReaderUtils.registerBeanDefinition(definitionHolder, beanDefinitionRegistry);
        }

        doScanPackages(beanDefinitionRegistry);
    }

    @Override
    public void postProcessBeanFactory(ConfigurableListableBeanFactory configurableListableBeanFactory) throws BeansException {

    }

    private void doScanPackages(BeanDefinitionRegistry beanDefinitionRegistry) {
        // 自动扫描注册业务和数据层对象
        ClassPathBeanDefinitionScanner scanner = new ClassPathBeanDefinitionScanner(beanDefinitionRegistry);
        List<String> basePackages = new ArrayList<>();
        // 数据层
        for (String basePackage : ServiceConfig.DATA_BASE_PACKAGES) {
            basePackages.add(basePackage);
        }
        scanner.scan(basePackages.toArray(new String[0]));
    }
}
