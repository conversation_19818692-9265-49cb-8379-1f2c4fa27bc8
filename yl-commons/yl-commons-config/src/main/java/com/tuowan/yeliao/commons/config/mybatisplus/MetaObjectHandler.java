package com.tuowan.yeliao.commons.config.mybatisplus;

import org.apache.ibatis.reflection.MetaObject;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * 元数据处理器
 * <AUTHOR>
 * @since 2025-07-04
 */
@Component
@ConditionalOnProperty(name = "mybatis-plus.enabled", havingValue = "true", matchIfMissing = true)
public class MetaObjectHandler implements com.baomidou.mybatisplus.core.handlers.MetaObjectHandler {

    /**
     * 插入时自动填充
     */
    @Override
    public void insertFill(MetaObject metaObject) {
        Date now = new Date();

        // 自动填充创建时间
        this.strictInsertFill(metaObject, "createTime", Date.class, now);

        // 自动填充更新时间
        this.strictInsertFill(metaObject, "updateTime", Date.class, now);

        // 自动填充逻辑删除字段
        this.strictInsertFill(metaObject, "deleted", Integer.class, 0);

        // 支持 LocalDateTime
        LocalDateTime localNow = LocalDateTime.now();
        this.strictInsertFill(metaObject, "createTime", LocalDateTime.class, localNow);
        this.strictInsertFill(metaObject, "updateTime", LocalDateTime.class, localNow);
    }

    /**
     * 更新时自动填充
     */
    @Override
    public void updateFill(MetaObject metaObject) {
        Date now = new Date();

        // 自动填充更新时间
        this.strictUpdateFill(metaObject, "updateTime", Date.class, now);

        // 支持 LocalDateTime
        LocalDateTime localNow = LocalDateTime.now();
        this.strictUpdateFill(metaObject, "updateTime", LocalDateTime.class, localNow);
    }
}
