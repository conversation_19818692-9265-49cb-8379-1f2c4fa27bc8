package com.tuowan.yeliao.commons.config.redis.template;

import com.easyooo.framework.support.redis.RedisTemplate;
import com.tuowan.yeliao.commons.config.enums.RedisType;
import com.tuowan.yeliao.commons.config.redis.condition.SysRedisCondition;
import org.springframework.context.annotation.Conditional;
import org.springframework.stereotype.Component;

/**
 * 系统缓存(包括Seq、Ons、Lock)
 *
 * <AUTHOR>
 * @date 2018/8/7 20:47
 */
@Component
@Conditional(SysRedisCondition.class)
public class SysRedisTemplate extends RedisTemplate {

    @Override
    public String redisBeanName() {
        return RedisType.Sys.getBeanName();
    }
}
