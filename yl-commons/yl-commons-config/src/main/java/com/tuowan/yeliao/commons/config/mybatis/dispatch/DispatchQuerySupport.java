package com.tuowan.yeliao.commons.config.mybatis.dispatch;

import com.easyooo.framework.sharding.annotation.Table;
import com.easyooo.framework.sharding.routing.RoutingContextExecutor;
import org.apache.ibatis.session.ResultContext;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionTemplate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 分发查询支持类，子类可注入该类完成分发查询，通常用于数据量非常大时分批执行，调用者可以实现数据阻塞式的执行或并发去执行
 *
 * <AUTHOR>
 * @date 2018/8/15 15:22
 */
@Component
public class DispatchQuerySupport implements ApplicationContextAware {

    private Logger logger = LoggerFactory.getLogger(getClass());

    /**
     * 默认从结果集中获取的记录条数
     */
    private static final int DEFAULT_LIMIT = 1000;

    private SqlSessionTemplate sqlSessionTemplate;

    /**
     * 默认每次提取 {@value #DEFAULT_LIMIT}
     *
     * @param state       查询命令
     * @param parameter   命令需要的参数
     * @param dataHandler 数据处理器
     * @throws Throwable
     */
    public <T> void doQuery(final Statement state, final Object parameter, final DataHandler<T> dataHandler) {
        doQuery(state, parameter, DEFAULT_LIMIT, dataHandler);
    }

    /**
     * 默认每次提取 {@value #DEFAULT_LIMIT}，命令无参数执行
     *
     * @param state       查询命令枚举类
     * @param dataHandler 数据处理器
     * @throws Throwable
     */
    public <T> Integer doQuery(final Statement state, final DataHandler<T> dataHandler) {
        return doQuery(state, null, DEFAULT_LIMIT, dataHandler);
    }

    public <T> Integer doQuery(final Statement state, final Integer limit, final DataHandler<T> dataHandler) {
        return doQuery(state, null, limit, dataHandler);
    }

    /**
     * 完整的查询分发处理函数
     */
    public <T> Integer doQuery(final Statement state, final Object parameter, final Integer limit, final DataHandler<T> dataHandler) {
        Class<?> clazz = state.getMapperClass();
        if (!clazz.isAnnotationPresent(Table.class)) {
            throw new RuntimeException("未定义表名，无法路由数据源");
        }
        Table table = clazz.getAnnotation(Table.class);
        try {
            return (int) RoutingContextExecutor.doProxy(clazz.getName(), table.schema(), table.value(), () -> {
                final String selectId = buildSelectId(state);
                DefaultResultHandler<T> mybatisHandler = new DefaultResultHandler<>(dataHandler, limit);
                sqlSessionTemplate.select(selectId, parameter, mybatisHandler);

                // last limit fetch data
                List<T> result = mybatisHandler.getLastResult();
                int length = result.size();
                if (length > 0) {
                    dataHandler.fetchRows(result, length);
                }
                return mybatisHandler.getFetchCount();
            });
        } catch (Throwable e) {
            logger.error("分发查询发生错误，原因：", e);
            throw new RuntimeException(e);
        }
    }

    /**
     * MyBatis result handler
     */
    private class DefaultResultHandler<T> implements ResultHandler {
        private DataHandler<T> dataHandler;
        private int limit;
        private int fetchCount;
        private List<T> result;

        public DefaultResultHandler(DataHandler<T> dataHandler, int limit) {
            this.dataHandler = dataHandler;
            this.limit = limit;

            result = new ArrayList<>(limit);
        }

        @Override
        public void handleResult(ResultContext context) {
            try {
                result.add((T) context.getResultObject());
                fetchCount++;
                // data handler
                if (context.getResultCount() % limit == 0) {
                    dataHandler.fetchRows(result, fetchCount);
                    result = new ArrayList<>(limit);
                }
            } catch (Exception ex) {
                logger.error(String.format("Perform Datahandler error occurs, "
                        + "the current row number: %d", context.getResultCount()), ex);
            }
        }

        public List<T> getLastResult() {
            return result;
        }

        public int getFetchCount() {
            return fetchCount;
        }
    }

    private String buildSelectId(Statement state) {
        return String.format("%s.%s", state.getMapperClass().getName(), state.getSelectId());
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        SqlSessionFactory sessionFactory = applicationContext.getBean("sqlSessionFactory", SqlSessionFactory.class);
        this.sqlSessionTemplate = new SqlSessionTemplate(sessionFactory);
    }
}
