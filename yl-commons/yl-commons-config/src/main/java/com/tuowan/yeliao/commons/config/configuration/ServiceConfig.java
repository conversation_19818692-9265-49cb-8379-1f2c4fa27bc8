package com.tuowan.yeliao.commons.config.configuration;


import com.easyooo.framework.common.util.StringUtils;
import com.tuowan.yeliao.commons.config.utils.YamlUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Properties;

/**
 * 服务配置
 *
 * <AUTHOR>
 */
public class ServiceConfig {

    /** 实体层包名路径，多个以英文逗号隔开，用于实体别名注册 */
    public static final String ENTITY_BASE_PACKAGES = getString("config.entity.basePackages", "com.tuowan.yeliao.commons.data.entity");

    /** 数据层包名路径，多个以英文逗号隔开 */
    public static final String[] DATA_BASE_PACKAGES = getStringArray("config.data.basePackages", "com.tuowan.yeliao.commons.data");

    /** 业务层事务切面表达式(内部服务需覆盖此配置，例如consumer和scheduling) */
    public static final String SERVICE_TRANS_EXP = getString("config.service.transExpression", "execution(public * com.tuowan.yeliao.*.service..*.*(..)) && @annotation(com.tuowan.yeliao.commons.web.proxy.config.BusiCode)");

    /** 消费者包名路径 */
    public static final String CONSUMER_BASE_PACKAGE = getString("config.consumer.basePackage", null);

    /** 业务层代理切面表达式(对外接口服务启用该切面) */
    public static final String SERVICE_POINTCUT_EXP = "execution(public * com.tuowan.yeliao.*.service..*.*(..)) && @annotation(com.tuowan.yeliao.commons.web.proxy.config.BusiCode)";

    /** 数据持久层包名路径 */
    public static final String[] DATA_PERSISTENCE_PACKAGES = getDataPersistencePackages();

    /** 数据持久层映射文件路径 */
    public static final String[] DATA_SQL_MAP_LOCATIONS = getDataSqlMapLocations();

    /** 数据持久层切面表达式 */
    public static final String DATA_PERSISTENCE_POINTCUT_EXP = getDataPersistencePointcutExp();

    private static Properties props;

    private static String getString(String key, String defaultValue) {
        if (props == null) {
            props = YamlUtils.loadYaml("application.yaml");
        }
        return props.getProperty(key, defaultValue);
    }

    private static String[] getStringArray(String key, String defaultValue) {
        return StringUtils.split(getString(key, defaultValue), ",");
    }

    private static String evalUri(String value) {
        return value.replace(".", "/");
    }

    private static String[] getDataPersistencePackages() {
        String[] values = DATA_BASE_PACKAGES;
        String[] packages = new String[values.length];
        for (int i = 0; i < values.length; i++) {
            packages[i] = values[i] + ".persistence";
        }
        return packages;
    }

    private static String getDataPersistencePointcutExp() {
        String[] packages = DATA_PERSISTENCE_PACKAGES;
        List<String> expList = new ArrayList<>();
        for (String pkg : packages) {
            expList.add("execution(* " + pkg + "..*Mapper.*(..))");
        }
        return StringUtils.join(expList, " or ");
    }

    private static String[] getDataSqlMapLocations() {
        String[] values = DATA_BASE_PACKAGES;
        String[] locations = new String[values.length];
        for (int i = 0; i < values.length; i++) {
            locations[i] = "classpath:/" + evalUri(values[i]) + "/sqlmap/**/*.xml";
        }
        return locations;
    }
}
