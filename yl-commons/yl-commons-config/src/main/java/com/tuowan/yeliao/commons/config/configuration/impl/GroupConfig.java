package com.tuowan.yeliao.commons.config.configuration.impl;

import com.easyooo.framework.common.util.PropUtils;
import com.tuowan.yeliao.commons.config.UnifiedConfig;

import java.util.Properties;

/**
 * 集团相关配置
 *
 * <AUTHOR>
 * @date 2020/6/29 9:56
 */
public class GroupConfig {
    /** 测试环境-黑名单请求地址 */
    public static final String BLACK_LIST_REQUEST_URL_TEST;
    /** 线上环境-黑名单请求地址 */
    public static final String BLACK_LIST_REQUEST_URL_PROD;
    /** 测试环境-黑名单请求地址(黑名单二期) */
    public static final String BLACK_LIST_HOST_TEST;
    /** 线上环境-黑名单请求地址（黑名单二期） */
    public static final String BLACK_LIST_HOST_PROD;

    public static final String APP_ID_PORD;

    public static final String SECRET_PORD;

    public static final String APP_ID_TEST;

    public static final String SECRET_TEST;


    static {
        Properties props = UnifiedConfig.getProperties();
        BLACK_LIST_REQUEST_URL_TEST = PropUtils.getString(props, "group.blacklistUrlTest");
        BLACK_LIST_REQUEST_URL_PROD = PropUtils.getString(props, "group.blacklistUrlProd");
        BLACK_LIST_HOST_TEST = PropUtils.getString(props, "group.blacklistHostTest");
        BLACK_LIST_HOST_PROD = PropUtils.getString(props, "group.blacklistHostProd");
        APP_ID_PORD = PropUtils.getString(props, "group.appIdProd");
        SECRET_PORD = PropUtils.getString(props, "group.secretProd");
        APP_ID_TEST = PropUtils.getString(props, "group.appIdTest");
        SECRET_TEST = PropUtils.getString(props, "group.secretTest");

    }
}
