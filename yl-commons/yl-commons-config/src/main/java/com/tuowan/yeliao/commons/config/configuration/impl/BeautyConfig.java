package com.tuowan.yeliao.commons.config.configuration.impl;

import com.easyooo.framework.common.util.PropUtils;
import com.easyooo.framework.common.util.StringUtils;
import com.tuowan.yeliao.commons.config.UnifiedConfig;

import java.util.Properties;

/**
 * 宇宙美颜配置类
 *
 * <AUTHOR>
 * @date 2023/6/26 10:11
 */
public class BeautyConfig {

    /** 商户代码 */
    public static final String TOKEN;


    static {
        Properties props = UnifiedConfig.getProperties();
        TOKEN = PropUtils.getString(props, "beauty.token");
    }
}
