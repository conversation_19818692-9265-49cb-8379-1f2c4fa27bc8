package com.tuowan.yeliao.commons.config.redis.condition;


import com.tuowan.yeliao.commons.config.UnifiedConfig;
import com.tuowan.yeliao.commons.config.enums.RedisType;
import org.springframework.context.annotation.Condition;
import org.springframework.context.annotation.ConditionContext;
import org.springframework.core.type.AnnotatedTypeMetadata;

/**
 * 日志缓存启用条件
 *
 * <AUTHOR>
 * @date 2020/6/16 21:20
 */
public class LogRedisCondition implements Condition {

    @Override
    public boolean matches(ConditionContext conditionContext, AnnotatedTypeMetadata annotatedTypeMetadata) {
        return UnifiedConfig.getRedisSourceConfig().contains(RedisType.Log);
    }
}
