package com.tuowan.yeliao.commons.config.configuration.impl;

import com.easyooo.framework.common.util.PropUtils;
import com.tuowan.yeliao.commons.config.UnifiedConfig;

import java.util.Properties;

/**
 * P6Spy SQL监控配置类
 *
 * <AUTHOR>
 * @date 2025-07-04
 */
public class P6spyConfig {

    /**
     * P6Spy是否启用
     */
    public static final boolean ENABLED;

    static {
        // 生产环境禁用，dev环境默认启用，test环境可通过配置控制
        if (UnifiedConfig.isProdEnv()) {
            ENABLED = false;
        } else {
            Properties props = UnifiedConfig.getProperties();
            ENABLED = UnifiedConfig.isDevEnv() || PropUtils.getBoolean(props, "p6spy", "enabled");
        }
    }

    /**
     * 检查P6Spy是否启用
     *
     * @return true if enabled, false otherwise
     */
    public static boolean isEnabled() {
        return ENABLED;
    }
}
