package com.tuowan.yeliao.commons.config.configuration.impl;

import com.easyooo.framework.common.util.PropUtils;
import com.tuowan.yeliao.commons.config.UnifiedConfig;

import java.util.Properties;

/**
 * P6Spy SQL监控配置类
 *
 * <AUTHOR>
 * @date 2025-07-04
 */
public class P6spyConfig {

    /** P6Spy是否启用 */
    public static final boolean ENABLED;

    static {
        Properties props = UnifiedConfig.getProperties();
        // 默认在非生产环境启用，生产环境禁用
        String defaultEnabled = UnifiedConfig.isProdEnv() ? "false" : "true";
        ENABLED = PropUtils.getBoolean(props, "p6spy", "enabled");
    }

    /**
     * 检查P6Spy是否启用
     * 
     * @return true if enabled, false otherwise
     */
    public static boolean isEnabled() {
        return ENABLED;
    }
}
