package com.tuowan.yeliao.commons.context.response;

import java.io.Serializable;
import java.util.List;

/**
 * 用户ID返回值
 *
 * <AUTHOR>
 * @date 2021/7/9 13:38
 */
public class UserIdsResponse implements Serializable {

    /** 用户ID */
    private List<Long> userIds;

    public static UserIdsResponse build(List<Long> userIds){
        UserIdsResponse response = new UserIdsResponse();
        response.setUserIds(userIds);
        return response;
    }

    public List<Long> getUserIds() {
        return userIds;
    }

    public void setUserIds(List<Long> userIds) {
        this.userIds = userIds;
    }
}
