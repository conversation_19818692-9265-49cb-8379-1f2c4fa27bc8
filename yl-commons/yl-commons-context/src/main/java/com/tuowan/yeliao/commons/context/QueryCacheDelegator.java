package com.tuowan.yeliao.commons.context;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
/**
 * 查询本地缓存代理
 */
public abstract class QueryCacheDelegator<T> {
	
	/**
	 * 本地缓存不存在，从数据源获得
	 * @return
	 */
	public abstract T queryDataSource();
	
	public Type getType() {
		Type superClass = getClass().getGenericSuperclass();
		if (superClass instanceof ParameterizedType) {
			Type type = ((ParameterizedType) superClass).getActualTypeArguments()[0];
			return type;
		}
		throw new RuntimeException("无法解析Type");	
	}
}
