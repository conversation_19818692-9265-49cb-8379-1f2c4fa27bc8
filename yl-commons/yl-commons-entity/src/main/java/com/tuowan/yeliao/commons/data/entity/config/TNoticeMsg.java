/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.commons.data.entity.config;

import com.easyooo.framework.cache.annotations.KeyProperty;
import com.tuowan.yeliao.commons.core.enums.general.BoolType;
import com.tuowan.yeliao.commons.data.enums.config.NoticeMsgType;
import com.tuowan.yeliao.commons.data.enums.general.ClientTouchType;
import org.apache.ibatis.type.Alias;

import java.util.Date;

@Alias("TNoticeMsg")
public class TNoticeMsg {
    /** 消息编号 */
    @KeyProperty
    private Long noticeId;

    /** 匹配规则：比如保级时间不一样提示内容也不一样
            
            公测版：需要设置公测投放时间和失效时间。
            
            当公测结束之后，需要自动或者手动切换位标准版本 */
    private String matchRule;

    /** 消息类型(NoticeMsgType)：活动消息Activity(A)，优惠消息Discount(D） */
    private NoticeMsgType noticeType;

    /** 消息内容：恭喜，您已成功升级到${royalLevel}级。请继续保持。
            
            每一个消息内容可用参数都会不一样，具体需要在发消息的时候
            确定。 */
    private String contentTpl;

    /** 消息图片，可用于推送富文本的图片，也可以是消息中心的封面图 */
    private String pic;

    /** 消息标题 */
    private String title;

    /** 详情文本：查看详细 */
    private String detailText;

    /** 打开类型(ClientTouchType)：活动页面Url(L)，直播间Room(R)、充值首页Recharge(C)、首页Home(H) */
    private ClientTouchType touchType;

    /** 打开类型值 */
    private String touchValue;

    /** 打开类型值规则Code */
    private String touchValueRule;

    /** 是否发送推送通知：T/F */
    private BoolType isPush;

    /** 是否显示在消息中心：T/F */
    private BoolType isInCenter;

    /** 计划推送时间：如果小于当前时间，则立即推送 */
    private Date planTime;

    /** 计划推送人数 */
    private Integer planUserCount;

    /** 实际推送时间 */
    private Date realTime;

    /** 实际推送人数 */
    private Integer realUserCount;

    /** 消息失效截止时间：活动类消息有用，客户端可以显示已经失效 */
    private Date noticeExpTime;

    /** 发送状态(NoticeSendStatus)：等待Wait(W)、处理Process(P)、取消Cancel(C)、中断Abort(A)、完成Done(D) */
    private String sendStatus;

    /** 创建人 */
    private Long creator;

    /** 创建时间 */
    private Date createTime;

    public TNoticeMsg() {
        
    }

    /** 根据主键初始化实例 **/
    public TNoticeMsg(Long noticeId) {
        this.noticeId = noticeId;
    }

    public Long getNoticeId() {
        return noticeId;
    }

    public void setNoticeId(Long noticeId) {
        this.noticeId = noticeId;
    }

    public String getMatchRule() {
        return matchRule;
    }

    public void setMatchRule(String matchRule) {
        this.matchRule = matchRule == null ? null : matchRule.trim();
    }

    public NoticeMsgType getNoticeType() {
        return noticeType;
    }

    public void setNoticeType(NoticeMsgType noticeType) {
        this.noticeType = noticeType;
    }

    public String getContentTpl() {
        return contentTpl;
    }

    public void setContentTpl(String contentTpl) {
        this.contentTpl = contentTpl == null ? null : contentTpl.trim();
    }

    public String getPic() {
        return pic;
    }

    public void setPic(String pic) {
        this.pic = pic == null ? null : pic.trim();
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title == null ? null : title.trim();
    }

    public String getDetailText() {
        return detailText;
    }

    public void setDetailText(String detailText) {
        this.detailText = detailText == null ? null : detailText.trim();
    }

    public ClientTouchType getTouchType() {
        return touchType;
    }

    public void setTouchType(ClientTouchType touchType) {
        this.touchType = touchType;
    }

    public String getTouchValue() {
        return touchValue;
    }

    public void setTouchValue(String touchValue) {
        this.touchValue = touchValue == null ? null : touchValue.trim();
    }

    public String getTouchValueRule() {
        return touchValueRule;
    }

    public void setTouchValueRule(String touchValueRule) {
        this.touchValueRule = touchValueRule == null ? null : touchValueRule.trim();
    }

    public BoolType getIsPush() {
        return isPush;
    }

    public void setIsPush(BoolType isPush) {
        this.isPush = isPush;
    }

    public BoolType getIsInCenter() {
        return isInCenter;
    }

    public void setIsInCenter(BoolType isInCenter) {
        this.isInCenter = isInCenter;
    }

    public Date getPlanTime() {
        return planTime;
    }

    public void setPlanTime(Date planTime) {
        this.planTime = planTime;
    }

    public Integer getPlanUserCount() {
        return planUserCount;
    }

    public void setPlanUserCount(Integer planUserCount) {
        this.planUserCount = planUserCount;
    }

    public Date getRealTime() {
        return realTime;
    }

    public void setRealTime(Date realTime) {
        this.realTime = realTime;
    }

    public Integer getRealUserCount() {
        return realUserCount;
    }

    public void setRealUserCount(Integer realUserCount) {
        this.realUserCount = realUserCount;
    }

    public Date getNoticeExpTime() {
        return noticeExpTime;
    }

    public void setNoticeExpTime(Date noticeExpTime) {
        this.noticeExpTime = noticeExpTime;
    }

    public String getSendStatus() {
        return sendStatus;
    }

    public void setSendStatus(String sendStatus) {
        this.sendStatus = sendStatus == null ? null : sendStatus.trim();
    }

    public Long getCreator() {
        return creator;
    }

    public void setCreator(Long creator) {
        this.creator = creator;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}