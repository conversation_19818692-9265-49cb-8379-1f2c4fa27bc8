package com.tuowan.yeliao.commons.data.enums.general;

import com.easyooo.framework.common.util.EnumUtils;

public enum TaskStatus implements EnumUtils.IDEnum {

    Finished("F", "已完成"),
    Doing("D", "进行中"),
    Incomplete("I", "未完成"),
    ;

    private String id;
    private String desc;

    TaskStatus(String id, String desc) {
        this.id = id;
        this.desc = desc;
    }

    @Override
    public String getId() {
        return id;
    }

    @Override
    public String getDesc() {
        return desc;
    }
}
