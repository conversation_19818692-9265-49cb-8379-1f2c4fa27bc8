package com.tuowan.yeliao.commons.data.enums.general;

import com.easyooo.framework.common.util.EnumUtils;

/**
 * 装扮中心枚举定义
 *
 * <AUTHOR>
 * @date 2021/11/2 09:43
 */
public enum DressUpType implements EnumUtils.IDEnum {

    HeadFrame("H", "头像框"),

    ChatBubble("B", "消息气泡"),

    Medal("M", "勋章"),

    Car("C", "座驾"),

   ;

    private String id;
    private String desc;

    DressUpType(String id, String desc) {
        this.id = id;
        this.desc = desc;
    }

    @Override
    public String getId() {
        return this.id;
    }

    @Override
    public String getDesc() {
        return this.desc;
    }

}
