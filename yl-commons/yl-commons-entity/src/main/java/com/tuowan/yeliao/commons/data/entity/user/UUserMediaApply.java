/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.commons.data.entity.user;

import com.easyooo.framework.cache.annotations.KeyProperty;
import java.util.Date;

import com.tuowan.yeliao.commons.data.enums.general.AuditStatus;
import com.tuowan.yeliao.commons.data.enums.user.MediaApplyStatus;
import com.tuowan.yeliao.commons.data.enums.user.MediaType;
import org.apache.ibatis.type.Alias;

@Alias("UUserMediaApply")
public class UUserMediaApply {
    /** 记录ID */
    @KeyProperty
    private Long applyId;

    /** 记录ID */
    private Long recordId;

    /** 用户ID */
    private Long userId;

    /** 媒体分类 */
    private MediaType type;

    /** 媒体内容 */
    private String mediaValue;

    /** 媒体扩展信息 */
    private String mediaExt;

    /** 排序值 */
    private Integer mediaIdx;

    /** 审核状态 */
    private MediaApplyStatus auditStatus;

    /** 状态描述 */
    private String statusDesc;

    /** 创建时间 */
    private Date createTime;

    public UUserMediaApply() {
        
    }

    /** 根据主键初始化实例 **/
    public UUserMediaApply(Long applyId) {
        this.applyId = applyId;
    }

    public Long getApplyId() {
        return applyId;
    }

    public void setApplyId(Long applyId) {
        this.applyId = applyId;
    }

    public Long getRecordId() {
        return recordId;
    }

    public void setRecordId(Long recordId) {
        this.recordId = recordId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public MediaType getType() {
        return type;
    }

    public void setType(MediaType type) {
        this.type = type;
    }

    public String getMediaValue() {
        return mediaValue;
    }

    public void setMediaValue(String mediaValue) {
        this.mediaValue = mediaValue == null ? null : mediaValue.trim();
    }

    public String getMediaExt() {
        return mediaExt;
    }

    public void setMediaExt(String mediaExt) {
        this.mediaExt = mediaExt == null ? null : mediaExt.trim();
    }

    public Integer getMediaIdx() {
        return mediaIdx;
    }

    public void setMediaIdx(Integer mediaIdx) {
        this.mediaIdx = mediaIdx;
    }

    public MediaApplyStatus getAuditStatus() {
        return auditStatus;
    }

    public void setAuditStatus(MediaApplyStatus auditStatus) {
        this.auditStatus = auditStatus;
    }

    public String getStatusDesc() {
        return statusDesc;
    }

    public void setStatusDesc(String statusDesc) {
        this.statusDesc = statusDesc;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}