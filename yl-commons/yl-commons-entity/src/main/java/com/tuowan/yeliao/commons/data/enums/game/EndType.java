package com.tuowan.yeliao.commons.data.enums.game;

import com.easyooo.framework.common.util.EnumUtils;

/**
 * 游戏结束类型
 *
 * <AUTHOR>
 * @date 2021/6/7 20:01
 */
public enum EndType implements EnumUtils.IDEnum {

    /**
     * 正常结束
     */
    Normal("N", "正常结束"),
    /**
     * 程序关闭
     */
    Close("C", "程序关闭"),
    /**
     * 新一局游戏开始
     */
    NewGame("G", "新一局游戏开始"),
    ;

    private String id;
    private String desc;

    EndType(String id, String desc) {
        this.id = id;
        this.desc = desc;
    }

    @Override
    public String getId() {
        return this.id;
    }

    @Override
    public String getDesc() {
        return this.desc;
    }
}
