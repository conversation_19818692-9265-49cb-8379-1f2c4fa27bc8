package com.tuowan.yeliao.commons.data.enums.config;

import com.easyooo.framework.common.util.EnumUtils;

/**
 * 互动语料类型
 *
 * <AUTHOR>
 * @date 2020/8/17 15:22
 */
public enum ChatVariableType implements EnumUtils.IDEnum {

    Variable("V", "有变量话术"),
    NoVariable("N", "无变量话术"),;

    private String id;
    private String desc;

    ChatVariableType(String id, String desc) {
        this.id = id;
        this.desc = desc;
    }

    @Override
    public String getId() {
        return id;
    }

    @Override
    public String getDesc() {
        return desc;
    }
}
