package com.tuowan.yeliao.commons.data.enums.general;

import com.easyooo.framework.common.util.EnumUtils;

public enum DealStatus implements EnumUtils.IDEnum {
    Wait("W", "未处理"),
    Done("D", "已处理"),
    Revoke("R", "已撤销");

    private String id;
    private String desc;

    DealStatus(String id, String desc) {
        this.id = id;
        this.desc = desc;
    }

    @Override
    public String getId() {
        return this.id;
    }

    @Override
    public String getDesc() {
        return this.desc;
    }
}
