package com.tuowan.yeliao.commons.data.enums.config;

import com.easyooo.framework.common.util.EnumUtils;

/**
 * 白名单类型枚举
 *
 * <AUTHOR>
 * @date 2020/12/4 19:58
 */
public enum WhitelistType implements EnumUtils.IDEnum {

    UserId("UserId", "用户ID"),
    Device("Device", "设备"),
    City("City", "城市名称"),
    ;

    private String id;
    private String desc;

    WhitelistType(String id, String desc) {
        this.id = id;
        this.desc = desc;
    }

    @Override
    public String getId() {
        return this.id;
    }

    @Override
    public String getDesc() {
        return this.desc;
    }
}
