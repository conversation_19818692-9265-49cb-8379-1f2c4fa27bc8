package com.tuowan.yeliao.commons.data.enums.user;

import com.easyooo.framework.common.util.EnumUtils;

/**
 * 购车状态枚举
 *
 * <AUTHOR>
 * @date 2020/7/3 14:53
 */
public enum CarStatus implements EnumUtils.IDEnum {

    Buy_H("BH", "已购车", "（豪华型）"),
    Buy_M("BM", "已购车", "（中高端型）"),
    Buy_D("BD", "已购车", "（经济型）"),
    NotBuy("N", "暂未购车", null),
    ;

    private String id;
    private String notes;
    private String desc;

    CarStatus(String id, String desc, String notes) {
        this.id = id;
        this.desc = desc;
        this.notes = notes;
    }

    @Override
    public String getId() {
        return this.id;
    }

    public String getNotes() {
        return notes;
    }

    @Override
    public String getDesc() {
        String notes = this.notes == null ? "" : this.notes;
        return this.desc + notes;
    }
}
