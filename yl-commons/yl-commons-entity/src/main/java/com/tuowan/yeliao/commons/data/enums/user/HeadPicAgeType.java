package com.tuowan.yeliao.commons.data.enums.user;

import com.easyooo.framework.common.util.EnumUtils;

/**
 * 头像年龄枚举定义
 *
 * <AUTHOR>
 * @date 2021/9/23 17:17
 */
public enum HeadPicAgeType implements EnumUtils.IDEnum {

    High("High", "高年龄",45),
    Middle("Middle", "中年龄",35),
    Low("Low", "低年龄",1),
    ;

    private String id;
    private String desc;
    /** 最小年龄限制 */
    private Integer minAge;

    HeadPicAgeType(String id, String desc,Integer minAge) {
        this.id = id;
        this.desc = desc;
        this.minAge = minAge;
    }

    @Override
    public String getId() {
        return this.id;
    }

    @Override
    public String getDesc() {
        return this.desc;
    }

    public Integer getMinAge() {
        return minAge;
    }

    /**
     * 注册年龄获取对应的类型
     *
     * @param age
     * @return
     */
    public static HeadPicAgeType getAgeSexType(Integer age) {
        if (null == age) {
            return HeadPicAgeType.Low;
        }
        for (HeadPicAgeType ageType : HeadPicAgeType.values()) {
            if (age >= ageType.getMinAge()) {
                return ageType;
            }
        }
        return HeadPicAgeType.Low;
    }
}
