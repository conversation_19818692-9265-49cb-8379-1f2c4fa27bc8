package com.tuowan.yeliao.commons.data.enums.config;

import com.easyooo.framework.common.util.EnumUtils;

/**
 * 学历枚举
 *
 * <AUTHOR>
 * @date 2020/12/23 13:32
 */
public enum EducationType implements EnumUtils.IDEnum {

    HighSchool("H", "高中及以下"),
    <PERSON><PERSON>("S", "专科"),
    Undergraduate("U", "本科"),
    Master("M", "硕士"),
    Doctor("D", "博士"),
    PostDoctor("P", "博士后"),
    ;

    private String id;
    private String desc;

    EducationType(String id, String desc) {
        this.id = id;
        this.desc = desc;
    }

    @Override
    public String getId() {
        return this.id;
    }

    @Override
    public String getDesc() {
        return this.desc;
    }
}
