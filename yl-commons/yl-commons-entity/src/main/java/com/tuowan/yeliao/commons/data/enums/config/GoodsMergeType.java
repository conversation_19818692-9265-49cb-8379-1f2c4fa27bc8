package com.tuowan.yeliao.commons.data.enums.config;

import com.easyooo.framework.common.util.EnumUtils;

/**
 * 物品合并方式枚举定义
 *
 * <AUTHOR>
 * @date 2018/7/5 14:00
 */
public enum GoodsMergeType implements EnumUtils.IDEnum {

    AppendTime("AT", "时间累加"),

    AppendCount("AC", "数量累加");

    private String id;
    private String desc;

    GoodsMergeType(String id, String desc) {
        this.id = id;
        this.desc = desc;
    }

    @Override
    public String getId() {
        return this.id;
    }

    @Override
    public String getDesc() {
        return this.desc;
    }
}
