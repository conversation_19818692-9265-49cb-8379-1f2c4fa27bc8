package com.tuowan.yeliao.commons.data.enums.user;

import com.easyooo.framework.common.util.EnumUtils;
import com.tuowan.yeliao.commons.core.constant.GlobalConstant;

/**
 * 聊主等级枚举定义
 *
 * <AUTHOR>
 * @date 2020/9/19 17:20
 */
public enum ChatMasterLevel implements EnumUtils.IDEnum {

    // 女用户用
    A("A", "荣耀女神", 5000, true, true, 5000L, GlobalConstant.RONGYAO_GODDESS_PIC),

    B("B", "钻石女神", 4000, true, true, 4000L, GlobalConstant.ZUANSHI_GODDESS_PIC),

    C("C", "黄金女神", 3000, true, true, 3000L, GlobalConstant.HUANGJIN_GODDESS_PIC),

    D("D", "青铜女神", 2000, true, true, 2000L, GlobalConstant.QINGTONG_GODDESS_PIC),

    E("E", "见习女神", 1000, true, true, 1000L, GlobalConstant.JIANXI_GODDESS_PIC),

    F("F", "淘汰女神", 0, true, false, 0L, GlobalConstant.TAOTAI_GODDESS_PIC),

    // 男用户用
    M("M", "默认等级男", -1, false, false, 0L, null),
    MS("MS", "S等级男", 150, false, false, 0L, null),
    MA("MA", "A等级男", 150, false, false, 0L, null),
    MB("MB", "B等级男", 100, false, false, 0L, null),
    ;

    private String id;
    private String desc;
    /**
     * 是否为聊主
     */
    private boolean isChatMaster;
    /**
     * 是否进行自动评级
     */
    private boolean autoConfirmLevel;
    /**
     * 等级值，用户级别大小比较，数值越大，级别越高
     */
    private Integer level;
    /**
     * 聊主等级对应分值,一般用于排序
     */
    private Long score;
    /**
     * 图片
     */
    private String pic;

    ChatMasterLevel(String id, String desc, Integer level, boolean isChatMaster, boolean autoConfirmLevel, Long score, String pic) {
        this.id = id;
        this.desc = desc;
        this.level = level;
        this.isChatMaster = isChatMaster;
        this.autoConfirmLevel = autoConfirmLevel;
        this.score = score;
        this.pic = pic;
    }

    @Override
    public String getId() {
        return this.id;
    }

    @Override
    public String getDesc() {
        return this.desc;
    }

    public Integer getLevel() {
        return level;
    }

    public boolean isChatMaster() {
        return isChatMaster;
    }

    public boolean isAutoConfirmLevel() {
        return autoConfirmLevel;
    }

    public Long getScore() {
        return score;
    }

    public String getPic() {
        return pic;
    }

    public static boolean isAboveLevel(ChatMasterLevel currLevel, ChatMasterLevel targetLevel) {
        return currLevel.getLevel() >= targetLevel.getLevel();
    }
}
