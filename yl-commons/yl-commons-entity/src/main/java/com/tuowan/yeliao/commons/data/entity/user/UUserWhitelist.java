/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.commons.data.entity.user;

import com.easyooo.framework.cache.annotations.KeyProperty;
import com.tuowan.yeliao.commons.data.enums.config.WhitelistType;
import com.tuowan.yeliao.commons.data.enums.config.WhitelistUseType;
import org.apache.ibatis.type.Alias;

import java.util.Date;

@Alias("UUserWhitelist")
public class UUserWhitelist {
    /** 白名单成员，可能是用户ID，设备号等 */
    @KeyProperty
    private String member;

    /** 白名单用途类型 */
    @KeyProperty
    private WhitelistUseType useType;

    /** 白名单类型 */
    private WhitelistType type;

    /** 创建时间 */
    private Date createTime;

    /** 加入白名单的原因 */
    private String joinReason;

    /** 创建人 */
    private Long creator;

    public UUserWhitelist() {
        
    }

    /** 根据主键初始化实例 **/
    public UUserWhitelist(String member, WhitelistUseType useType) {
        this.member = member;
        this.useType = useType;
    }

    public String getMember() {
        return member;
    }

    public void setMember(String member) {
        this.member = member == null ? null : member.trim();
    }

    public WhitelistUseType getUseType() {
        return useType;
    }

    public void setUseType(WhitelistUseType useType) {
        this.useType = useType;
    }

    public WhitelistType getType() {
        return type;
    }

    public void setType(WhitelistType type) {
        this.type = type;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getJoinReason() {
        return joinReason;
    }

    public void setJoinReason(String joinReason) {
        this.joinReason = joinReason == null ? null : joinReason.trim();
    }

    public Long getCreator() {
        return creator;
    }

    public void setCreator(Long creator) {
        this.creator = creator;
    }
}