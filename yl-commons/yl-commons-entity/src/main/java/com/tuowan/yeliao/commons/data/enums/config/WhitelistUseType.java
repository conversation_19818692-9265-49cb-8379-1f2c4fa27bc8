package com.tuowan.yeliao.commons.data.enums.config;

import com.easyooo.framework.common.util.EnumUtils;

/**
 * 白名单用途类型枚举
 *
 * <AUTHOR>
 * @date 2020/12/4 19:58
 */
public enum WhitelistUseType implements EnumUtils.IDEnum {

    NoInvitePresent("NoInvitePresent", "没有邀友提成的用户名单"),
    VideoHighSexRate("VideoHighSexRate", "视频鉴黄允许高性感指数的用户白名单"),
    HideInviteRank("HideInviteRank", "隐藏邀友榜单的用户白名单"),
    ManageFamilyMember("ManageFamilyMember", "可以管理家族成员的官方人员"),
    H5Recharge("H5Recharge", "H5充值人员"),
    OfficialRecharge("OfficialRecharge", "官方人员（充值返80%）", true),
    AllHotFamily("AllHotFamily", "查看所有热门家族白名单"),
    FamilyTabCity("FamilyTabCity", "定位到同城家族的城市"),
    BanWithDraw("BanWithDraw","封禁用户提现"),
    LimitRecharge("LimitRecharge", "限制充值");



    private String id;
    private String desc;
    // 是否管理员权限，如果true, 则只有后台超级管理员可以操作
    private boolean admin;

    WhitelistUseType(String id, String desc) {
        this.id = id;
        this.desc = desc;
        this.admin = false;
    }


    WhitelistUseType(String id, String desc, boolean admin) {
        this.id = id;
        this.desc = desc;
        this.admin = admin;
    }

    @Override
    public String getId() {
        return this.id;
    }

    @Override
    public String getDesc() {
        return this.desc;
    }

    public boolean isAdmin() {
        return admin;
    }
}
