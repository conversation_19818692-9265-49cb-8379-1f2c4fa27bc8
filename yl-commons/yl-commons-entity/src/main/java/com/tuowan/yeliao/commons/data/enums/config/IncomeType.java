package com.tuowan.yeliao.commons.data.enums.config;

import com.easyooo.framework.common.util.EnumUtils;

/**
 * 收入枚举
 *
 * <AUTHOR>
 * @date 2020/12/23 13:32
 */
public enum IncomeType implements EnumUtils.IDEnum {

    Secret("S", "保密"),
    Five("F", "5万以下"),
    Ten("T", "5万~10万"),
    Twenty("W", "10万~20万"),
    Thirty("H", "20万~30万"),
    Fifty("I", "30万~50万"),
    Million("M", "50万~100万"),
    More("O", "100万以上");

    private String id;
    private String desc;

    IncomeType(String id, String desc) {
        this.id = id;
        this.desc = desc;
    }

    @Override
    public String getId() {
        return this.id;
    }

    @Override
    public String getDesc() {
        return this.desc;
    }
}
