/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.commons.data.entity.config;

import com.easyooo.framework.cache.annotations.Cache;
import com.easyooo.framework.cache.annotations.Group;
import com.easyooo.framework.cache.annotations.KeyProperty;
import com.tuowan.yeliao.commons.data.enums.user.SexType;
import org.apache.ibatis.type.Alias;

@Alias("THeadPic")
@Cache(expire = 30 * 24 * 3600)
public class THeadPic {
    /** 图片ID */
    @KeyProperty
    private Integer id;

    /** 头像地址 */
    private String headPic;

    /** 适用的性别类型 */
    @Group
    private SexType sexType;

    /** 适用的头像年龄 */
    private Integer age;

    public THeadPic() {
        
    }

    /** 根据主键初始化实例 **/
    public THeadPic(Integer id) {
        this.id = id;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getHeadPic() {
        return headPic;
    }

    public void setHeadPic(String headPic) {
        this.headPic = headPic == null ? null : headPic.trim();
    }

    public SexType getSexType() {
        return sexType;
    }

    public void setSexType(SexType sexType) {
        this.sexType = sexType;
    }

    public Integer getAge() {
        return age;
    }

    public void setAge(Integer age) {
        this.age = age;
    }
}