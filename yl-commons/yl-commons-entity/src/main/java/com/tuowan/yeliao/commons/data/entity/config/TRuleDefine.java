/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.commons.data.entity.config;

import com.easyooo.framework.cache.annotations.Cache;
import com.easyooo.framework.cache.annotations.KeyProperty;
import org.apache.ibatis.type.Alias;

import java.util.Date;

@Alias("TRuleDefine")
@Cache
public class TRuleDefine {
    /** 规则代码，必须是一个方法名，比如getUserInfo、
            isMatchSendGiftMsg，返回结果get，返回boolean统一用isXxx */
    @KeyProperty
    private String code;

    /** 规则名称 */
    private String name;

    /** 所属项目：Consumer消费者，BusiWeb业务Web，
            JobJob程序，ActWeb活动Web，或Common通用 取字典类 */
    private String byProject;

    /** 所属表名：真实表名，或Common通用，取系统字典 */
    private String byTable;

    /** 规则内容：标准的Java代码 */
    private String content;

    /** 版本号 */
    private Integer version;

    /** 创建人用户编号 */
    private Long creator;

    /** 创建时间 */
    private Date createTime;

    public TRuleDefine() {
        
    }

    /** 根据主键初始化实例 **/
    public TRuleDefine(String code) {
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code == null ? null : code.trim();
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    public String getByProject() {
        return byProject;
    }

    public void setByProject(String byProject) {
        this.byProject = byProject == null ? null : byProject.trim();
    }

    public String getByTable() {
        return byTable;
    }

    public void setByTable(String byTable) {
        this.byTable = byTable == null ? null : byTable.trim();
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content == null ? null : content.trim();
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public Long getCreator() {
        return creator;
    }

    public void setCreator(Long creator) {
        this.creator = creator;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}