package com.tuowan.yeliao.commons.data.enums.user;

import com.easyooo.framework.common.util.EnumUtils;

public enum UserBothKeyMark implements EnumUtils.IDEnum {

    FirstPrivateChat("FPC", "首次发送私聊"),

    ;
    private String id;
    private String desc;

    UserBothKeyMark(String id, String desc) {
        this.id = id;
        this.desc = desc;
    }

    @Override
    public String getId() {
        return id;
    }

    @Override
    public String getDesc() {
        return desc;
    }
}
