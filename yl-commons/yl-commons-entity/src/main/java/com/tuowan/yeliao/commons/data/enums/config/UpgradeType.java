package com.tuowan.yeliao.commons.data.enums.config;

/**
 * 版本更新枚举
 *
 * <AUTHOR> WeiGuo
 * @date 2018/8/7 8:59
 */
public enum UpgradeType {
    Force("FC", "强制更新"),
    Recommend("RD", "推荐更新"),
    None("NO", "无更新");

    private String id;
    private String desc;

    UpgradeType(String id, String desc) {
        this.id = id;
        this.desc = desc;
    }

    public String getId() {
        return id;
    }

    public String getDesc() {
        return desc;
    }
}
