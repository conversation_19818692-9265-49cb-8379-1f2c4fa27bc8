/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.commons.data.entity.config;

import com.easyooo.framework.cache.annotations.Cache;
import com.easyooo.framework.cache.annotations.Group;
import com.easyooo.framework.cache.annotations.KeyProperty;
import com.tuowan.yeliao.commons.data.enums.config.AwardType;
import org.apache.ibatis.type.Alias;

import java.util.Date;

@Alias("TAwardDetail")
@Cache(expire = 30 * 24 * 3600)
public class TAwardDetail {
    /** 奖励内容ID */
    @KeyProperty
    private Integer detailId;

    /** 所属奖励内容代码 */
    @Group
    private String parentCode;

    /** 奖励类型(AwardType)：用户经验值UserExp(UE)、贵族经验值RoyalExp(RE)、主播经验值AnchorExp(AE)、金币Beans(BN)、财富折现礼物BeansBuyGift(BG)、主播奖惩Cash(CS)、
            物品Goods(GD)、礼物Gift(GF) */
    private AwardType awardType;

    /** 奖励类型值，和类型配套适用,如果是物品，则该值填物品ID，如果是礼物则该值为GiftId，如果没有子类型，则填类型值 */
    private String awardTypeValue;

    /** 数量：无论是按数量还是有效天，都使用该字段
            如果奖励类型是物品ID，需要反查物品类型的合并方式。增加到背包。
            其它奖励类型都是直接修改用户的属性值。 */
    private Integer count;

    /** 过期有效天：按数量管理的物品才可能需要这个字段 */
    private Integer expDays;

    /** 截止时间：按数量管理的物品可以设置截止时间 */
    private Date endTime;

    /** 扩展参数配置，例如：beansBuyGift=100,130&time=10 */
    private String extJsonCfg;

    public TAwardDetail() {
        
    }

    public TAwardDetail(String parentCode) {
        this.parentCode = parentCode;
    }

    /** 根据主键初始化实例 **/
    public TAwardDetail(Integer detailId) {
        this.detailId = detailId;
    }

    public Integer getDetailId() {
        return detailId;
    }

    public void setDetailId(Integer detailId) {
        this.detailId = detailId;
    }

    public String getParentCode() {
        return parentCode;
    }

    public void setParentCode(String parentCode) {
        this.parentCode = parentCode == null ? null : parentCode.trim();
    }

    public AwardType getAwardType() {
        return awardType;
    }

    public void setAwardType(AwardType awardType) {
        this.awardType = awardType;
    }

    public String getAwardTypeValue() {
        return awardTypeValue;
    }

    public void setAwardTypeValue(String awardTypeValue) {
        this.awardTypeValue = awardTypeValue == null ? null : awardTypeValue.trim();
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public Integer getExpDays() {
        return expDays;
    }

    public void setExpDays(Integer expDays) {
        this.expDays = expDays;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public String getExtJsonCfg() {
        return extJsonCfg;
    }

    public void setExtJsonCfg(String extJsonCfg) {
        this.extJsonCfg = extJsonCfg;
    }
}