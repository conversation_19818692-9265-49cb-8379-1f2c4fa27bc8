/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.commons.data.entity.user;

import com.easyooo.framework.cache.annotations.KeyProperty;
import com.tuowan.yeliao.commons.data.enums.user.SignType;
import org.apache.ibatis.type.Alias;

import java.util.Date;

@Alias("UUserSign")
public class UUserSign {
    /** 用户ID */
    @KeyProperty
    private Long userId;

    /** 签到日期 */
    @KeyProperty
    private Date signDate;

    /** 本轮签到开始日期 */
    private Date startDate;

    /** 签到时间 */
    private Date createTime;

    /** 签到类型 */
    private SignType signType;

    public UUserSign() {
        
    }

    /** 根据主键初始化实例 **/
    public UUserSign(Long userId, Date signDate) {
        this.userId = userId;
        this.signDate = signDate;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Date getSignDate() {
        return signDate;
    }

    public void setSignDate(Date signDate) {
        this.signDate = signDate;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public SignType getSignType() {
        return signType;
    }

    public void setSignType(SignType signType) {
        this.signType = signType;
    }
}