/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.commons.data.entity.config;

import com.easyooo.framework.cache.annotations.Cache;
import com.easyooo.framework.cache.annotations.KeyProperty;
import com.easyooo.framework.cache.annotations.MiniTable;
import org.apache.ibatis.type.Alias;

@Alias("TVoice")
@Cache
@MiniTable
public class TVoice {
    /**
     * 记录ID
     */
    @KeyProperty
    private Long logId;

    /**
     * 语音标题
     */
    private String voiceTitle;

    /**
     * 语音内容
     */
    private String voiceContent;

    public TVoice() {

    }

    /**
     * 根据主键初始化实例
     **/
    public TVoice(Long logId) {
        this.logId = logId;
    }

    public Long getLogId() {
        return logId;
    }

    public void setLogId(Long logId) {
        this.logId = logId;
    }

    public String getVoiceTitle() {
        return voiceTitle;
    }

    public void setVoiceTitle(String voiceTitle) {
        this.voiceTitle = voiceTitle == null ? null : voiceTitle.trim();
    }

    public String getVoiceContent() {
        return voiceContent;
    }

    public void setVoiceContent(String voiceContent) {
        this.voiceContent = voiceContent == null ? null : voiceContent.trim();
    }
}