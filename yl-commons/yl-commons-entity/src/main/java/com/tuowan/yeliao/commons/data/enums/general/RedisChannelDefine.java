/*!
 * Copyright 2018, Julun, Inc.
 */

package com.tuowan.yeliao.commons.data.enums.general;

import com.easyooo.framework.common.util.EnumUtils;

/**
 * Redis PubSub订阅类型定义
 *
 * <AUTHOR>
 * @date 2018/9/19 19:42
 */
public enum RedisChannelDefine implements EnumUtils.IDEnum {

    /**
     *
     */
    TSetting("Setting", "全局配置", "com.tuowan.yeliao.commons.data.support.config.impl.SettingsConfig"),

    TSocialTag("SocialTag", "交友标签配置", "com.tuowan.yeliao.commons.data.support.config.impl.SocialTagConfig"),

    TBusinessNotify("BusinessNotify", "业务通知", "com.tuowan.yeliao.commons.data.support.config.impl.BusinessNotifyConfig"),

    TPromotionConfig("PromotionConfig", "推广配置", "com.tuowan.yeliao.commons.data.support.config.impl.PromotionConfig"),

    TMerchant("Merchant", "充值商户", "com.tuowan.yeliao.commons.data.support.config.impl.MerchantConfig"),

    TFemaleLevelNum("TFemaleLevelNum", "女用户数字等级", "com.tuowan.yeliao.commons.data.support.config.impl.FemaleLevelNumConfig"),

    TMaleLevel("TMaleLevel", "男用户等级配置", "com.tuowan.yeliao.commons.data.support.config.impl.MaleLevelConfig"),

    TAnimalMenu("TAnimalMenu", "动物园游戏配置", "com.tuowan.yeliao.commons.data.support.config.impl.GameAnimalConfig"),
    ;

    private String id;
    private String desc;
    private String className;

    RedisChannelDefine(String id, String desc, String className) {
        this.id = id;
        this.desc = desc;
        this.className = className;
    }

    @Override
    public String getId() {
        return this.id;
    }

    @Override
    public String getDesc() {
        return this.desc;
    }

    public String getClassName() {
        return className;
    }
}
