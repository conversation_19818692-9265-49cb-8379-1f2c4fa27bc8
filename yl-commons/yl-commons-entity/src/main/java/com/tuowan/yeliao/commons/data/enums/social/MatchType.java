package com.tuowan.yeliao.commons.data.enums.social;

import com.easyooo.framework.common.util.EnumUtils;

public enum MatchType implements EnumUtils.IDEnum {
    VF("vf", "视频缘分"),
    VM("vm", "视频速配"),
    SC("sc", "系统搭讪"),
    FC("fc", "一键搭讪"),
    ;

    private String id;
    private String desc;

    MatchType(String id, String desc) {
        this.id = id;
        this.desc = desc;
    }

    @Override
    public String getId() {
        return id;
    }

    @Override
    public String getDesc() {
        return desc;
    }
}
