package com.tuowan.yeliao.commons.data.enums.acct;

import com.easyooo.framework.common.util.EnumUtils;

public enum RechargeMatchRule implements EnumUtils.IDEnum {
    FirstRecharge("FC", "首冲"),
    SecondRecharge("SC", "次冲"),
    MoreSecondRecharge("MSC", "不是首冲和次冲"),
    ;

    private String id;
    private String desc;

    RechargeMatchRule(String id, String desc) {
        this.id = id;
        this.desc = desc;
    }

    @Override
    public String getId() {
        return id;
    }

    @Override
    public String getDesc() {
        return desc;
    }
}
