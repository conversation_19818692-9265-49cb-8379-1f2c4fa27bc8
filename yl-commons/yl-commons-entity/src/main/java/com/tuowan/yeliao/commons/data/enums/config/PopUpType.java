package com.tuowan.yeliao.commons.data.enums.config;

import com.easyooo.framework.common.util.EnumUtils;

public enum PopUpType implements EnumUtils.IDEnum {

    ActInvite("ACTI","邀友活动弹窗", "INVITE"),
    SignInMale("SM", "签到男", "SIGNI<PERSON>"),
    SignInFemale("SF", "签到女", "SIGNIN"),
    RechargeMale("CGM", "充值男", "RGWD"),
    WithDrawFemale("PMO", "提现女", "RGWD"),
    PigBankFemale("PF", "存钱罐女", "PIGBANK"),
    LuckDraw("LD", "幸运转盘抽奖", "LuckDraw"),
    ;

    private String id;
    private String desc;
    private String group;

    PopUpType(String id, String desc, String group) {
        this.id = id;
        this.desc = desc;
        this.group = group;
    }

    @Override
    public String getId() {
        return id;
    }

    @Override
    public String getDesc() {
        return desc;
    }

    public String getGroup() {
        return group;
    }
}
