package com.tuowan.yeliao.commons.data.enums.ccs;

import com.easyooo.framework.common.util.EnumUtils;

/**
 * 客服消息类型枚举定义
 *
 * <AUTHOR>
 * @date 2020/4/23 15:59
 */
public enum MsgType implements EnumUtils.IDEnum {

    Sys("S", "系统消息", "[系统消息]"),
    Text("T", "文本消息", null),
    Pic("P", "图片消息", "[图片]"),
    Video("V", "视频消息", "[视频]");

    private String id;
    private String desc;
    private String content;

    MsgType(String id, String desc, String content) {
        this.id = id;
        this.desc = desc;
        this.content = content;
    }

    @Override
    public String getId() {
        return this.id;
    }

    @Override
    public String getDesc() {
        return this.desc;
    }

    public String getContent() {
        return content;
    }
}
