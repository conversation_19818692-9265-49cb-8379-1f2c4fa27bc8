package com.tuowan.yeliao.commons.data.enums.user;

import com.easyooo.framework.common.util.EnumUtils;

/**
 * Vip 触发类型
 *
 * <AUTHOR>
 * @date 2022/2/15 10:48
 */
public enum VipTriggerType implements EnumUtils.IDEnum {

    Visit("Visit", "访客条目"),

    ChatTips("ChatTips", "引导开通VIP的私信tips"),

    ChatBanner("ChatBanner", "引导开通VIP的私信输入框Banner"),

    ChatNotEnough("ChatNotEnough", "私信余额不足"),

    AudioFinish("AudioFinish", "音视频通话结束"),

    AudioTips("AudioTips", "引导开通VIP的音视频Tips"),

    FirstChatUp("FirstChatUp", "首次搭讪"),

    ChatUpNotEnough("ChatUpNotEnough", "搭讪余额不足"),

    AudioPrice("AudioPrice", "音视频价格"),

    Center("Center", "会员中心"),

    Banner("Banner", "广告Banner"),

    MyVisitor("MyVisitor", "我的界面访客");



    private String id;
    private String desc;

    VipTriggerType(String id, String desc) {
        this.id = id;
        this.desc = desc;
    }

    @Override
    public String getId() {
        return this.id;
    }

    @Override
    public String getDesc() {
        return this.desc;
    }
}
