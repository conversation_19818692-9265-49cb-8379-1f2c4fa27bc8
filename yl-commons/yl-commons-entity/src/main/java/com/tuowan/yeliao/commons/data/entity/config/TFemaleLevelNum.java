/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.commons.data.entity.config;

import com.easyooo.framework.cache.annotations.KeyProperty;
import java.util.Date;

import com.tuowan.yeliao.commons.core.enums.general.BoolType;
import com.tuowan.yeliao.commons.data.enums.user.FemaleLevel;
import org.apache.ibatis.type.Alias;

@Alias("TFemaleLevelNum")
public class TFemaleLevelNum {
    /** 自增表主键 */
    @KeyProperty
    private Integer id;

    /** 等级标识 */
    private FemaleLevel level;

    /** 昨日最小收益 */
    private Integer minIncome1;

    /** 今日最小收益 */
    private Integer minIncome2;

    /** 注册时间小于多少小时 */
    private Integer cTimeL;

    /** 注册时间大于多少小时 */
    private Integer cTimeG;

    /** 是否需要真人认证 */
    private BoolType isRp;

    /** 私聊收益占比 */
    private Double msgRate;

    /** 休息间隔 */
    private Integer restIt;

    /** 今日限制 */
    private Integer dayLimit;

    /** 15分钟限制 */
    private Integer minuLimit;

    /** 主动打招呼限制 */
    private Integer activeLimit;

    /** 主动发起音视频限制 */
    private Integer actCallLimit;

    /** 主动拨打所需亲密度 */
    private Integer actCallIntimate;

    /** 主动私聊限制 */
    private Integer actMsgLimit;

    /** 未回复消息上限 */
    private Integer noReplyLimit;

    /** 一键搭讪小时限制 */
    private Integer fcuHourLimit;

    /** 一键搭讪日限制 */
    private Integer fcuDayLimit;

    /** 回复率-时间限制（秒） */
    private Integer rpRateSeconds;

    /** 回复率-人数限制 */
    private Integer rpRatePeople;

    /** 描述 */
    private String desc;

    /** 顺序 */
    private Integer order;

    /** 是否默认 */
    private BoolType isDef;

    /** 操作人 */
    private Long operator;

    /** 最近修改时间 */
    private Date updateTime;

    /** 创建时间 */
    private Date createTime;

    public TFemaleLevelNum() {
        
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public FemaleLevel getLevel() {
        return level;
    }

    public void setLevel(FemaleLevel level) {
        this.level = level;
    }

    public Integer getMinIncome1() {
        return minIncome1;
    }

    public void setMinIncome1(Integer minIncome1) {
        this.minIncome1 = minIncome1;
    }

    public Integer getMinIncome2() {
        return minIncome2;
    }

    public void setMinIncome2(Integer minIncome2) {
        this.minIncome2 = minIncome2;
    }

    public Integer getcTimeL() {
        return cTimeL;
    }

    public void setcTimeL(Integer cTimeL) {
        this.cTimeL = cTimeL;
    }

    public Integer getcTimeG() {
        return cTimeG;
    }

    public void setcTimeG(Integer cTimeG) {
        this.cTimeG = cTimeG;
    }

    public BoolType getIsRp() {
        return isRp;
    }

    public void setIsRp(BoolType isRp) {
        this.isRp = isRp;
    }

    public Double getMsgRate() {
        return msgRate;
    }

    public void setMsgRate(Double msgRate) {
        this.msgRate = msgRate;
    }

    public Integer getRestIt() {
        return restIt;
    }

    public void setRestIt(Integer restIt) {
        this.restIt = restIt;
    }

    public Integer getDayLimit() {
        return dayLimit;
    }

    public void setDayLimit(Integer dayLimit) {
        this.dayLimit = dayLimit;
    }

    public Integer getMinuLimit() {
        return minuLimit;
    }

    public void setMinuLimit(Integer minuLimit) {
        this.minuLimit = minuLimit;
    }

    public Integer getActiveLimit() {
        return activeLimit;
    }

    public void setActiveLimit(Integer activeLimit) {
        this.activeLimit = activeLimit;
    }

    public Integer getActCallLimit() {
        return actCallLimit;
    }

    public void setActCallLimit(Integer actCallLimit) {
        this.actCallLimit = actCallLimit;
    }

    public Integer getActCallIntimate() {
        return actCallIntimate;
    }

    public void setActCallIntimate(Integer actCallIntimate) {
        this.actCallIntimate = actCallIntimate;
    }

    public Integer getActMsgLimit() {
        return actMsgLimit;
    }

    public void setActMsgLimit(Integer actMsgLimit) {
        this.actMsgLimit = actMsgLimit;
    }

    public Integer getNoReplyLimit() {
        return noReplyLimit;
    }

    public void setNoReplyLimit(Integer noReplyLimit) {
        this.noReplyLimit = noReplyLimit;
    }

    public Integer getFcuHourLimit() {
        return fcuHourLimit;
    }

    public void setFcuHourLimit(Integer fcuHourLimit) {
        this.fcuHourLimit = fcuHourLimit;
    }

    public Integer getFcuDayLimit() {
        return fcuDayLimit;
    }

    public void setFcuDayLimit(Integer fcuDayLimit) {
        this.fcuDayLimit = fcuDayLimit;
    }

    public Integer getRpRateSeconds() {
        return rpRateSeconds;
    }

    public void setRpRateSeconds(Integer rpRateSeconds) {
        this.rpRateSeconds = rpRateSeconds;
    }

    public Integer getRpRatePeople() {
        return rpRatePeople;
    }

    public void setRpRatePeople(Integer rpRatePeople) {
        this.rpRatePeople = rpRatePeople;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public Integer getOrder() {
        return order;
    }

    public void setOrder(Integer order) {
        this.order = order;
    }

    public BoolType getIsDef() {
        return isDef;
    }

    public void setIsDef(BoolType isDef) {
        this.isDef = isDef;
    }

    public Long getOperator() {
        return operator;
    }

    public void setOperator(Long operator) {
        this.operator = operator;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}