package com.tuowan.yeliao.commons.data.enums.general;

/**
 * Redis要分片存储的数据类型美剧
 *
 * <AUTHOR>
 * @date 2020/7/11 14:42
 */
public enum RedisShardType {

    UserAlive("在线心跳", 6),
    HomeUserOffline("首页用户离线", 6),
    UserActionStat("用户行为数据统计", 10),
    FictitiousVisitor("虚拟访客添加队列", 4),
    NetCallDeductQueue("语音通话扣费队列", 6),
    NetCallTimeoutQueue("语音通话超时队列", 6),
    SystemNotice("系统通知", 6),
    PostHeatChange("动态<获得>热度值变化", 6),
    PostBaseHeatDecrement("动态<基础>热度值递减", 6),
    FateMatchRedPacketWaitReceiveQueue("缘分匹配红包待领取队列", 6),
    NetCallMatchMaleQueue("通话匹配男性队列", 6),
    CpAnniversaryGuideMark("情侣纪念日引导标记", 6),
    LastFamilyVisitTime("最近一次游客家族访问时间", 10),
    LastFamilyVisitId("最近一次访问的游客家族ID", 10),
    RedPacketTimeQueue("红包定时发放队列", 6),
    ChatUpDispatchQueue("主动搭讪任务分发队列", 6),
    UserSignInRemind("用户签到提醒", 6),
    FateMatchDispatchQueue("缘分红包派发队列", 8),
    FateMatchRedPacketQueue("缘分匹配红包待领取队列", 8),
    MaleMatchQueue("男用户系统搭讪匹配队列", 10),
    UserFirstMsgForDay("女用户每天首次与男用户私信交互缓存", 10),
    ;

    private String desc;
    private Integer shardNum;

    RedisShardType(String desc, Integer shardNum) {
        this.desc = desc;
        this.shardNum = shardNum;
    }

    public String getDesc() {
        return desc;
    }

    public Integer getShardNum() {
        return shardNum;
    }
}
