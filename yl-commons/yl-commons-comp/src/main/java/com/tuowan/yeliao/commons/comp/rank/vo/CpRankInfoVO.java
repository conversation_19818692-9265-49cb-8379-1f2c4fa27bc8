package com.tuowan.yeliao.commons.comp.rank.vo;


import com.tuowan.yeliao.commons.data.dto.user.UserBusiDTO;
import com.tuowan.yeliao.commons.data.enums.user.SexType;

/**
 * 通用cp榜信息封装
 *
 * <AUTHOR>
 * @date 2021/8/11 14:44
 */
public class CpRankInfoVO {

    /**
     * 关系ID
     */
    private String relationId;
    /**
     * 男用户信息
     */
    private UserBusiDTO maleUserInfo;
    /**
     * 女用户信息
     */
    private UserBusiDTO femaleUserInfo;
    /**
     * 比分
     */
    private Long score;
    /**
     * 排名
     */
    private String ranking;
    /**
     * 其他说明
     */
    private String remark;

    public static CpRankInfoVO create(String relationId, UserBusiDTO user1, UserBusiDTO user2, Long score) {
        CpRankInfoVO vo = new CpRankInfoVO();
        vo.setRelationId(relationId);
        if (user1 != null) {
            if (user1.getSex() == SexType.Male) {
                vo.setMaleUserInfo(user1);
            } else {
                vo.setFemaleUserInfo(user1);
            }
        }
        if (user2 != null) {
            if (user2.getSex() == SexType.Male) {
                vo.setMaleUserInfo(user2);
            } else {
                vo.setFemaleUserInfo(user2);
            }
        }
        vo.setScore(score);
        if(score <= 0){
            vo.setRanking("未上榜");
        }
        return vo;
    }

    public String getRelationId() {
        return relationId;
    }

    public void setRelationId(String relationId) {
        this.relationId = relationId;
    }

    public UserBusiDTO getMaleUserInfo() {
        return maleUserInfo;
    }

    public void setMaleUserInfo(UserBusiDTO maleUserInfo) {
        this.maleUserInfo = maleUserInfo;
    }

    public UserBusiDTO getFemaleUserInfo() {
        return femaleUserInfo;
    }

    public void setFemaleUserInfo(UserBusiDTO femaleUserInfo) {
        this.femaleUserInfo = femaleUserInfo;
    }

    public Long getScore() {
        return score;
    }

    public void setScore(Long score) {
        this.score = score;
    }

    public String getRanking() {
        return ranking;
    }

    public void setRanking(String ranking) {
        this.ranking = ranking;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
