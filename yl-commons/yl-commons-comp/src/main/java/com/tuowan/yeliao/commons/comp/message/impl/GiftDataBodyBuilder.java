/*
 * Copyright © 2014 YAOCHEN Corporation, All Rights Reserved
 */
package com.tuowan.yeliao.commons.comp.message.impl;

import com.easyooo.framework.common.util.JsonUtils;
import com.tuowan.yeliao.commons.comp.message.DataBodyBuilder;
import com.tuowan.yeliao.commons.comp.message.vo.GiftMessageVO;
import com.tuowan.yeliao.commons.core.enums.business.BusinessDataKey;
import com.tuowan.yeliao.commons.data.dto.social.ChatTipsInfoDTO;
import com.tuowan.yeliao.commons.data.dto.social.GiftDTO;
import com.tuowan.yeliao.commons.data.entity.config.TMessage;
import com.tuowan.yeliao.commons.data.entity.config.TProdSocialGift;
import com.tuowan.yeliao.commons.data.enums.config.MessageType;
import com.tuowan.yeliao.commons.data.persistence.config.TProdSocialGiftMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

import static com.tuowan.yeliao.commons.context.GlobalUtils.extValue;
import static com.tuowan.yeliao.commons.context.GlobalUtils.formValue;

/**
 * 文本消息构造器实现
 *
 * <AUTHOR>
 */
@Component
public class GiftDataBodyBuilder implements DataBodyBuilder<GiftMessageVO> {

    @Autowired
    private TProdSocialGiftMapper tProdSocialGiftMapper;

    public GiftDataBodyBuilder(){
    }

    @Override
    public GiftMessageVO buildDataBody(TMessage message, Map<String, Object> contextMap) {
        GiftMessageVO vo = new GiftMessageVO();
        Integer giftId = extValue(BusinessDataKey.ConsumeGiftId);
        Integer count = extValue(BusinessDataKey.ConsumeCount);
        TProdSocialGift gift = tProdSocialGiftMapper.selectByPrimaryKey(new TProdSocialGift(giftId));
        vo.setGiftInfo(GiftDTO.build4(gift.getGiftId(), gift.getGiftName(), gift.getPic(), count));
        vo.setTipsInfo(JsonUtils.deserializeAsObject(extValue(BusinessDataKey.ChatTipsInfo), ChatTipsInfoDTO.class));
        return vo;
    }

    @Override
    public boolean isSupported(TMessage message) {
        return message.getMsgType() == MessageType.Gift;
    }
}
