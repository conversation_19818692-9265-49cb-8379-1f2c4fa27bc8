package com.tuowan.yeliao.commons.comp.message;

import com.easyooo.framework.common.util.StringUtils;

import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 消息模板解析器
 *
 * <AUTHOR>
 * @date 2019/8/7 20:26
 */
public class MsgTextTplPaser {

    static final Pattern PATTERN = Pattern.compile("(\\$\\{[a-zA-Z_0-9\\.]+\\})");

    public static String parse(String textTpl, Map<String,Object> contextMap, Map<String, Object> paramMap){
        if(StringUtils.isEmpty(textTpl)){
            return null;
        }
        // tpl params
        StringBuffer newTextTpl = new StringBuffer();
        Matcher matcher = PATTERN.matcher(textTpl);
        while (matcher.find()) {
            String el = matcher.group();
            String propPath = el.replaceAll("\\$|\\{|\\}", "");
            String[] props = propPath.split("\\.");
            Object value = getValueByPropertyPath(contextMap, props);
            if (value == null || "".equals(value)) {
                // 删除没有参数值的参数
                matcher.appendReplacement(newTextTpl, "");
            } else {
                paramMap.put(el, value);
            }
        }
        matcher.appendTail(newTextTpl);

        return newTextTpl.toString();
    }

    @SuppressWarnings("unchecked")
    private static Object getValueByPropertyPath(Map<String, Object> contextMap, String[] props) {
        Map<String, Object> current = contextMap;
        for (int i = 0; i < props.length; i++) {
            Object o = current.get(props[i].trim());
            if (o == null) {
                return null;
            }
            if (i == props.length - 1) {
                return o;
            } else {
                current = (Map<String, Object>) o;
            }
        }
        return null;
    }



}
