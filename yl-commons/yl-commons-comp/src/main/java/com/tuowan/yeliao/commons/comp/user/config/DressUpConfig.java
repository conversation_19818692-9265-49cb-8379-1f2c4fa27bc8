package com.tuowan.yeliao.commons.comp.user.config;

import java.util.Arrays;
import java.util.List;

/**
 * 装扮配置
 *
 * <AUTHOR>
 * @date 2021/11/3 16:52
 */
public class DressUpConfig {

    /** 默认头像框 */
    public static String DEFAULT_HEAD_FRAME = "config/goods/default_frame.png";

    /** 默认气泡显示图片 */
    public static String DEFAULT_BUBBLE_SHOW_PIC = "config/goods/default_bubble.png";


    /** 不显示在装扮所有列表的物品ID合集 */
    public static List<Integer> NOT_SHOW_PRODIDS = Arrays.asList(518,521,501,502,528);

}
