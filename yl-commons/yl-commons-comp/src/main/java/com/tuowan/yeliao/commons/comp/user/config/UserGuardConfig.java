package com.tuowan.yeliao.commons.comp.user.config;


import com.tuowan.yeliao.commons.core.enums.business.SocialGiftDefine;

/**
 * 用户守护相关配置
 *
 * <AUTHOR>
 * @date 2021/4/29 9:56
 */
public class UserGuardConfig {

    /** 守护天使有效天数 */
    public static int GUARD_ANGEL_DAYS = 30;
    /** 守护之心有效天数 */
    public static int GUARD_HEAT_DAYS = 10;

    public static int getGuardValidDays(Integer giftId) {
        if (SocialGiftDefine.GuardAngle.getGiftId().equals(giftId)) {
            return UserGuardConfig.GUARD_ANGEL_DAYS;
        }
        if (SocialGiftDefine.GuardHeat.getGiftId().equals(giftId)) {
            return UserGuardConfig.GUARD_HEAT_DAYS;
        }
        throw new UnsupportedOperationException("守护不支持当前礼物ID：" + giftId);
    }


}
