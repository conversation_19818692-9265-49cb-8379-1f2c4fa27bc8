package com.tuowan.yeliao.commons.comp.headline.impl;

import com.easyooo.framework.common.enums.DatePattern;
import com.easyooo.framework.common.util.DateUtils;
import com.easyooo.framework.common.util.ListUtils;
import com.easyooo.framework.common.util.StringUtils;
import com.tuowan.yeliao.commons.comp.headline.IHomeHeadLineMessage;
import com.tuowan.yeliao.commons.comp.headline.dto.UserNoticeMsgDTO;
import com.tuowan.yeliao.commons.core.enums.business.BusinessDataKey;
import com.tuowan.yeliao.commons.data.enums.social.HeadLineMsgType;
import com.tuowan.yeliao.commons.data.manager.user.UserInfoManager;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.tuowan.yeliao.commons.context.GlobalUtils.extValue;


/**
 * 首页头条消息基础实现
 *
 * <AUTHOR>
 * @date 2021/1/13 15:07
 */
public abstract class BaseHeadLineImpl implements IHomeHeadLineMessage {

    @Autowired
    private UserInfoManager userInfoManager;

    @Override
    public UserNoticeMsgDTO getBaseMessage(HeadLineMsgType msgType, Long userId, Long friendId, String paramValue) {
        return null;
    }

    @Override
    public Map<String, Object> buildInitContextMap(List<Long> targetUserIds, UserNoticeMsgDTO dto) {
        Map<String, Object> map = new HashMap<>();
        map.put("userId", dto.getUserId());
        map.put("nickname", dto.getNickname());
        map.put("headPic", dto.getHeadPic());
        if (null != dto.getFriendId()) {
            map.put("friendId", dto.getFriendId());
            map.put("friendNickname", dto.getFriendNickname());
        }
        if (null != dto.getFamilyId()) {
            map.put("familyId", dto.getFamilyId());
        }
        if (StringUtils.isNotEmpty(dto.getFamilyName())) {
            map.put("familyName", dto.getFamilyName());
        }
        // 播放次数，默认都是3次
        map.put("playTimes", 3);
        // 是否置顶头条
        map.put("top", dto.isTop());
        if (StringUtils.isNotEmpty(dto.getHeadLineMsgType().getMsgIcon())) {
            extValue(BusinessDataKey.MsgIcon, dto.getHeadLineMsgType().getMsgIcon());
        }
        // 背景图
        map.put("bgPic", dto.getBgPic());
        map.put("smallBgPic", dto.getSmallBgPic());
        // V2背景图
        map.put("bgPicV2", dto.getBgPicV2());
        map.put("smallBgPicV2", dto.getSmallBgPicV2());
        // webp资源类型
        map.put("webpType", dto.getWebpType());
        // 前缀图标
        map.put("runwayPic", dto.getRunwayPic());
        if (null != dto.getCreateTime()) {
            // 插入时间
            map.put("createTime", DateUtils.toString(dto.getCreateTime(), DatePattern.HM));
        }
        extValue(BusinessDataKey.OverwriteBusiCode, dto.getHeadLineMsgType().getId());
        extValue(BusinessDataKey.CustomMap, map);
        extValue(BusinessDataKey.UserIds, ListUtils.toLongArray(targetUserIds));
        return map;
    }
}
