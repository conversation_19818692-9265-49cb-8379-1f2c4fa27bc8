package com.tuowan.yeliao.commons.comp.user;

import com.alibaba.fastjson.JSONObject;
import com.easyooo.framework.common.extend.SimpleMap;
import com.easyooo.framework.common.http.HttpPoolManager;
import com.easyooo.framework.common.http.HttpRequestExt;
import com.easyooo.framework.common.util.JsonUtils;
import com.easyooo.framework.common.util.MapUtils;
import com.easyooo.framework.common.util.StringUtils;
import com.tuowan.yeliao.commons.comp.user.dto.ReportUserToGroupDTO;
import com.tuowan.yeliao.commons.config.configuration.impl.AppConfig;
import com.tuowan.yeliao.commons.config.configuration.impl.GroupConfig;
import com.tuowan.yeliao.commons.config.enums.Env;
import com.tuowan.yeliao.commons.config.enums.MiniLockType;
import com.tuowan.yeliao.commons.config.lock.MiniLockTemplate;
import com.tuowan.yeliao.commons.config.redis.RedisKey;
import com.tuowan.yeliao.commons.config.redis.template.BusiRedisTemplate;
import com.tuowan.yeliao.commons.core.enums.redis.BusiKeyDefine;
import com.tuowan.yeliao.commons.core.exception.BusiException;
import com.tuowan.yeliao.commons.core.http.HttpPoolManagerFactory;
import com.tuowan.yeliao.commons.data.enums.config.SettingsType;
import com.tuowan.yeliao.commons.data.support.config.impl.SettingsConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 用户黑名单（电话、身份证）组件
 */
@Component
public class UserBlackListComponent {
    protected final Logger LOG = LoggerFactory.getLogger(this.getClass());

    private static final Integer MOBILE_IN = 2000;
    private static final Integer ID_NUM_IN = 2001;
    private static final Integer INVALID_MOBILE = 1001;
    private static final Integer INVALID_ID_NUM = 1002;

    private static String getTokenUrl = "/api/get-token";

    private static String reportUserUrl = "/api/sync/import";

    @Autowired
    private BusiRedisTemplate busiRedisTemplate;
    @Autowired
    private MiniLockTemplate miniLockTemplate;

    /**
     * @title 用户是否在集团黑名单中
     * 2000 手机号在黑名单中
     * 2001 身份证号在黑名单中
     *
     * @param mobile 电话号码
     * @param idNum 身份证号
     * @return true在黑名单中， false不在黑名单中
     */
    public boolean isGroupBlack(String mobile, String idNum) {
        // 全局校验开关
        if (!SettingsConfig.getBoolean(SettingsType.IsGroupBlackUserCheck)) {
            return false;
        }
        StringBuilder build = new StringBuilder();
        if (StringUtils.isNotEmpty(mobile)) {
            build.append("&phone=").append(mobile);
        }
        if (StringUtils.isNotEmpty(idNum)) {
            build.append("&id_num=").append(idNum);
        }
        if (StringUtils.isEmpty(build.toString())) {
            // 都为空放行
            return false;
        }
        String checkUrl = AppConfig.ENV == Env.PROD ? GroupConfig.BLACK_LIST_REQUEST_URL_PROD : GroupConfig.BLACK_LIST_REQUEST_URL_TEST;
        String paramUrl = checkUrl + '?' + build.substring(1);
        try {
            HttpPoolManager groupBlackListPool = HttpPoolManagerFactory.getGroupBlackListPool();
            String resultJson = groupBlackListPool.get(paramUrl);
            LOG.info("UserBlackListComponent-isGroupBlack-info result:{}", resultJson);
            if (StringUtils.isNotEmpty(resultJson)) {
                SimpleMap resultMap = JsonUtils.toSimpleMap(resultJson);
                Integer code = resultMap.getInteger("code");
                if (MOBILE_IN.equals(code) || ID_NUM_IN.equals(code)) {
                    return true;
                }
                if(INVALID_MOBILE.equals(code)){
                    throw new BusiException("手机号格式不正确，请确认后重新输入！");
                }
                if(INVALID_ID_NUM.equals(code)){
                    throw new BusiException("身份证号格式不正确，请确认后重新输入！");
                }
            }
        } catch (BusiException e){
            throw e;
        } catch (Exception e) {
            LOG.error("UserBlackListComponent-isGroupBlack-error reason:", e);
        }
        return false;
    }
    /**
     * 上报黑名单用户给集团
     *
     */
    public void reportUserToGroup(ReportUserToGroupDTO dto){
        // 主机地址
        String host = AppConfig.ENV == Env.PROD ?  GroupConfig.BLACK_LIST_HOST_PROD : GroupConfig.BLACK_LIST_HOST_TEST;
        String requestUrl = host + reportUserUrl;
        //拿到token
        String token =  getToken();
        String authToken = "Bearer " +token;
        //连接
        HttpPoolManager groupBlackListPool = HttpPoolManagerFactory.getGroupBlackListPool();
        //请求头
        HttpRequestExt httpRequestExt = new HttpRequestExt(MapUtils.gmap("Content-Type", "application/json" ,"Authorization" , authToken));
        //参数封装
        //data
        List<JSONObject> datalist = new ArrayList<>();
        JSONObject detail  =new JSONObject();
        detail.put("violation_classification", dto.getViolationClassification());
        detail.put("source_product", dto.getSourceProduct());
        detail.put("penalty_time", dto.getPenaltyTime());
        detail.put("account_id", dto.getAccountId());
        detail.put("phone", dto.getPhone());
        detail.put("account_nickname", dto.getAccountNickname());
        detail.put("account_gender", dto.getAccountGender());
        detail.put("registration_time", dto.getRegistrationTime());
        detail.put("id_num", dto.getIdNum());
        detail.put("real_name", dto.getRealName());
        datalist.add(detail);

        JSONObject params = new JSONObject();
        params.put("operator", dto.getOperatorName());
        params.put("data",datalist);

        String resp = null;
        try {
            resp = groupBlackListPool.post(requestUrl, params ,httpRequestExt);
            LOG.info("UserBlackListComponent-reportUserToGroup-reqUid:{},resp:{}", dto.getAccountId(), resp);
        } catch (Exception e) {
            LOG.error("UserBlackListComponent-reportUserToGroup-sendRequestFail-uid:{}", dto.getAccountId(), e);
        }

        try {
            JSONObject jsonObject = JSONObject.parseObject(resp);
            Integer code = jsonObject.getInteger("code");
            // 请求成功
            if (code == 0) {
                return;
            }
            String msg = jsonObject.getString("message");
            LOG.error("UserBlackListComponent-reportUserToGroup-requestFail-uid:{},msg:{}",dto.getAccountId(),msg);
        } catch (Exception e) {
            LOG.error("UserBlackListComponent-reportUserToGroup-parObjectFail-uid:{}", dto.getAccountId(), e);
        }
    }

    /**
     * 从缓存中获取token
     * @return token
     */
    private String getToken() {
        RedisKey redisKey = buildGroupToken();
        String tokenCache = busiRedisTemplate.get(redisKey);
        if (StringUtils.isNotEmpty(tokenCache)) {
            return tokenCache;
        }
        return miniLockTemplate.execute(MiniLockType.GetGroupToken, () -> {
            String newToken;
            // 判断缓存中是否已有值
            newToken = busiRedisTemplate.get(redisKey);
            if(StringUtils.isNotEmpty(newToken)){
                return newToken;
            }
            newToken = rebuildToken();
            // 放入缓存
            saveToken(newToken);
            return newToken;
        });
    }

    /**
     * 重新远程调用获取token并保存
     * @return token
     */
    private String rebuildToken(){
        // 主机地址
        String host = AppConfig.ENV == Env.PROD ?  GroupConfig.BLACK_LIST_HOST_PROD : GroupConfig.BLACK_LIST_HOST_TEST;
        String requestUrl = host + getTokenUrl;

        String appId = AppConfig.ENV == Env.PROD ?  GroupConfig.APP_ID_PORD : GroupConfig.APP_ID_TEST;
        String secret = AppConfig.ENV == Env.PROD ?  GroupConfig.SECRET_PORD : GroupConfig.SECRET_TEST;
        HttpPoolManager groupBlackListPool = HttpPoolManagerFactory.getGroupBlackListPool();
        HttpRequestExt httpRequestExt = new HttpRequestExt(MapUtils.gmap("Content-Type", "application/json"));
        // 请求参数
        Map<String, Object> param = MapUtils.gmap("app_id", appId, "secret", secret);

        String resultJson = null;
        try {
            resultJson = groupBlackListPool.post(requestUrl, param, httpRequestExt);
            LOG.info("UserBlackListComponent-getToken-resultJson:{}",resultJson);
        } catch (Exception e) {
            LOG.error("UserBlackListComponent-getToken-fail",e);
        }
        String token = null;
        try {
            JSONObject jsonObject = JSONObject.parseObject(resultJson);
            String data = jsonObject.getString("data");
            JSONObject dataObj = JSONObject.parseObject(data);
            token = dataObj.getString("token");
        } catch (Exception e) {
            LOG.error("UserBlackListComponent-getToken-parsError",e);
        }
        return token;
    }
    /**
     * 保存token到缓存
     */
    private void saveToken(String token) {
        RedisKey redisKey = buildGroupToken();
        busiRedisTemplate.set(redisKey,token);
    }
    /**
     * 集团token缓存key
     */
    private RedisKey buildGroupToken() {
        return RedisKey.create(BusiKeyDefine.GroupToken);
    }
}
