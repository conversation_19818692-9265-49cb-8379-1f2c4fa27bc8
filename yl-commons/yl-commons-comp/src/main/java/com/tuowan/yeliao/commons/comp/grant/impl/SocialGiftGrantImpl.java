/*!
 * Copyright 2018, Julun, Inc.
 */

package com.tuowan.yeliao.commons.comp.grant.impl;

import com.easyooo.framework.common.util.DateUtils;
import com.easyooo.framework.common.util.StringUtils;
import com.tuowan.yeliao.commons.comp.grant.Grant;
import com.tuowan.yeliao.commons.comp.grant.dto.GrantDetailDTO;
import com.tuowan.yeliao.commons.comp.grant.dto.GrantProdDTO;
import com.tuowan.yeliao.commons.comp.user.UserSocialBagComponent;
import com.tuowan.yeliao.commons.core.exception.ComponentException;
import com.tuowan.yeliao.commons.data.entity.config.TAward;
import com.tuowan.yeliao.commons.data.entity.config.TAwardDetail;
import com.tuowan.yeliao.commons.data.entity.config.TProdSocialGift;
import com.tuowan.yeliao.commons.data.enums.config.AwardGrantType;
import com.tuowan.yeliao.commons.data.enums.config.AwardType;
import com.tuowan.yeliao.commons.data.enums.general.Status;
import com.tuowan.yeliao.commons.data.persistence.config.TProdSocialGiftMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Date;

/**
 * 社交礼物奖励实现
 *
 * <AUTHOR>
 * @date 2020/8/28 21:00
 */
@Component
public class SocialGiftGrantImpl implements Grant {

    private final Logger LOG = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private TProdSocialGiftMapper tProdSocialGiftMapper;
    @Autowired
    private UserSocialBagComponent userSocialBagComponent;

    @Override
    public GrantDetailDTO doGrant(AwardGrantType grantType, Long userId, TAward cfg, TAwardDetail d, Integer awardTimes, boolean unAward, String remark) {
        if (StringUtils.isBlank(d.getAwardTypeValue())) {
            throw new ComponentException("奖励配置{}的明细项目{}配置错误", d.getParentCode(), d.getDetailId());
        }
        Integer giftId;
        try {
            giftId = Integer.valueOf(d.getAwardTypeValue());
        } catch (Exception e) {
            LOG.error("奖励配置错误，原因：", e);
            throw new ComponentException("奖励配置{}的明细项目{}配置错误", d.getParentCode(), d.getDetailId());
        }
        TProdSocialGift gift = tProdSocialGiftMapper.selectByPrimaryKey(new TProdSocialGift(giftId));
        if (gift == null || Status.Enable != gift.getStatus()) {
            throw new ComponentException("奖励配置{}的明细项目{}配置错误,物品不存在或已禁用", d.getParentCode(), d.getDetailId());
        }
        // 数量
        int cnt = d.getCount() * awardTimes;
        Date expDate = d.getEndTime();
        if (d.getExpDays() != null) {
            expDate = DateUtils.plusDays(DateUtils.nowTime(), d.getExpDays());
        }
        if (!unAward) {
            if (cnt > 0) {
                userSocialBagComponent.addGift(userId, giftId, cnt, expDate);
            } else if (cnt < 0) {
                userSocialBagComponent.reduceGift(userId, giftId, -cnt);
            }
        }
        GrantProdDTO prod = GrantProdDTO.create(gift, cnt * 1L, expDate);
        return new GrantDetailDTO(gift.getGiftName(), prod.getAwardCount(), Arrays.asList(prod), d.getExtJsonCfg());
    }


    @Override
    public AwardType getSupportType() {
        return AwardType.SocialGift;
    }
}
