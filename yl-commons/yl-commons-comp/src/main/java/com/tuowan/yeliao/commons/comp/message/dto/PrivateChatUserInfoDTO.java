package com.tuowan.yeliao.commons.comp.message.dto;


import com.tuowan.yeliao.commons.core.enums.general.BoolType;
import com.tuowan.yeliao.commons.core.enums.general.user.UserType;
import com.tuowan.yeliao.commons.data.enums.user.SexType;

/**
 * 私聊用户信息封装
 *
 * <AUTHOR>
 * @date 2020/9/30 11:27
 */
public class PrivateChatUserInfoDTO {

    /** 用户ID */
    private Long userId;
    /** 用户类型 */
    private UserType userType;
    /** 头像 */
    private String headPic;
    /** 昵称 */
    private String nickname;
    /** 本条消息费用 */
    private Long fee;
    /** 性别 */
    private SexType sexType;
    /** 亲密度等级 */
    private Integer intimateLevel;
    /** 是否使用聊天券 */
    private BoolType useChatTicket;
    /** 聊天费用退回说明 */
    private String expiredText;
    /** 是否互相心动 */
    private BoolType heartTouch;
    /** 消息提示 */
    private String tips;
    // 接收方用户信息
    private PrivateChatUserInfoDTO targetUserObj;

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public UserType getUserType() {
        return userType;
    }

    public void setUserType(UserType userType) {
        this.userType = userType;
    }

    public String getHeadPic() {
        return headPic;
    }

    public void setHeadPic(String headPic) {
        this.headPic = headPic;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public Long getFee() {
        return fee;
    }

    public void setFee(Long fee) {
        this.fee = fee;
    }

    public SexType getSexType() {
        return sexType;
    }

    public void setSexType(SexType sexType) {
        this.sexType = sexType;
    }

    public Integer getIntimateLevel() {
        return intimateLevel;
    }

    public void setIntimateLevel(Integer intimateLevel) {
        this.intimateLevel = intimateLevel;
    }

    public PrivateChatUserInfoDTO getTargetUserObj() {
        return targetUserObj;
    }

    public void setTargetUserObj(PrivateChatUserInfoDTO targetUserObj) {
        this.targetUserObj = targetUserObj;
    }

    public BoolType getUseChatTicket() {
        return useChatTicket;
    }

    public void setUseChatTicket(BoolType useChatTicket) {
        this.useChatTicket = useChatTicket;
    }

    public String getExpiredText() {
        return expiredText;
    }

    public void setExpiredText(String expiredText) {
        this.expiredText = expiredText;
    }

    public BoolType getHeartTouch() {
        return heartTouch;
    }

    public void setHeartTouch(BoolType heartTouch) {
        this.heartTouch = heartTouch;
    }

    public String getTips() {
        return tips;
    }

    public void setTips(String tips) {
        this.tips = tips;
    }
}
