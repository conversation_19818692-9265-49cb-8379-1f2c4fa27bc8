package com.tuowan.yeliao.commons.comp.grant.impl;

import com.easyooo.framework.common.util.JsonUtils;
import com.easyooo.framework.common.util.MapUtils;
import com.easyooo.framework.common.util.MsgUtils;
import com.tuowan.yeliao.commons.comp.grant.Grant;
import com.tuowan.yeliao.commons.comp.grant.dto.GrantDetailDTO;
import com.tuowan.yeliao.commons.comp.notice.NoticeComponent;
import com.tuowan.yeliao.commons.core.exception.DataException;
import com.tuowan.yeliao.commons.data.entity.config.TAward;
import com.tuowan.yeliao.commons.data.entity.config.TAwardDetail;
import com.tuowan.yeliao.commons.data.entity.config.TProdActualGoods;
import com.tuowan.yeliao.commons.data.enums.config.AwardGrantType;
import com.tuowan.yeliao.commons.data.enums.config.AwardType;
import com.tuowan.yeliao.commons.data.enums.config.NoticeSysType;
import com.tuowan.yeliao.commons.data.enums.general.ClientTouchType;
import com.tuowan.yeliao.commons.data.manager.config.SocialProdManager;
import com.tuowan.yeliao.commons.data.utils.HtmlUrlUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 真实物品奖励发放
 *
 * <AUTHOR>
 * @date 2022/15/59 16:35
 */
@Component
public class ActualGoodsGrantImpl implements Grant {
    private final Logger LOG = LoggerFactory.getLogger(this.getClass());
    private static final String NOTICE_SUB_TITLE = "恭喜抽中${goodsName}";
    private static final String NOTICE_CONTENT = "恭喜您在抽奖活动中抽中了${goodsName}*${num}，3个工作日内将有客服联系您发放奖品。";

    @Autowired
    private SocialProdManager socialProdManager;
    @Autowired
    private NoticeComponent noticeComponent;

    @Override
    public GrantDetailDTO doGrant(AwardGrantType grantType, Long userId, TAward cfg, TAwardDetail d, Integer awardTimes, boolean unAward, String remark) {
        // 备注：真实商品中奖我们仅以u_user_award_detail中奖表为准
        // 备注：具体记录保存根据模块逻辑决定
        LOG.info("ActualGoodsGrantImpl-doGrant-info-{} award:{} awardDetail:{}", userId, JsonUtils.seriazileAsString(cfg), JsonUtils.seriazileAsString(d));
        TProdActualGoods prodActualGoods = socialProdManager.getProdActualGoods(Long.valueOf(d.getAwardTypeValue()));
        if(Objects.isNull(prodActualGoods)){
            throw new DataException("ActualGoodsGrantImpl-doGrant-error goodsId:{} 商品已不存在！", d.getAwardTypeValue());
        }
        // 发送系统通知
        Map<String, Object> paramMap = new HashMap<>();
        Map<String, Object> gmap = MapUtils.gmap("goodsName", prodActualGoods.getGoodsName(), "num", awardTimes);
        paramMap.put("content", MsgUtils.format$(NOTICE_CONTENT, gmap));
        paramMap.put("subTitle", MsgUtils.format$(NOTICE_SUB_TITLE, gmap));
        paramMap.put("overrideTouchType", ClientTouchType.Url);
        paramMap.put("overrideTouchValue", HtmlUrlUtils.getLuckyWheelUrl());
        noticeComponent.sendSystemNotice(userId, NoticeSysType.CommonNotice, paramMap);
        return new GrantDetailDTO(prodActualGoods.getGoodsName(), awardTimes.longValue());
    }

    @Override
    public AwardType getSupportType() {
        return AwardType.ActualGoods;
    }
}
