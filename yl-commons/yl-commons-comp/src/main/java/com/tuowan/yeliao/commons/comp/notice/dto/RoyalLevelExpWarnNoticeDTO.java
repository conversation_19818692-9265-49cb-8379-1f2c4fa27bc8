package com.tuowan.yeliao.commons.comp.notice.dto;

import com.easyooo.framework.validate.config.LMNotEmpty;
import com.easyooo.framework.validate.config.LMNotNull;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 贵族过期预警通知信息
 *
 * <AUTHOR>
 * @date 2020/7/11 19:05
 */
public class RoyalLevelExpWarnNoticeDTO extends SystemNoticeDTO {

    @LMNotNull
    private Long userId;
    @LMNotEmpty
    private String royalName;
    @LMNotNull
    private Integer remainDays;
    @LMNotNull
    private String date;


    public RoyalLevelExpWarnNoticeDTO(Long userId, String royalName, Integer remainDays, String date) {
        this.userId = userId;
        this.royalName = royalName;
        this.remainDays = remainDays;
        this.date = date;
    }

    @Override
    public Map<String, Object> toParamsMap() {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("userId", this.userId);
        map.put("royalName", this.royalName);
        map.put("remainDays", this.remainDays);
        map.put("date", this.date);
        return map;
    }


    @Override
    public Long getUserId() {
        return this.userId;
    }

    @Override
    public List<Long> getUserIds() {
        return null;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getRoyalName() {
        return royalName;
    }

    public void setRoyalName(String royalName) {
        this.royalName = royalName;
    }

    public Integer getRemainDays() {
        return remainDays;
    }

    public void setRemainDays(Integer remainDays) {
        this.remainDays = remainDays;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }
}
