package com.tuowan.yeliao.commons.comp.notice.dto;


import com.tuowan.yeliao.commons.data.enums.social.RelationChangeType;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 好友通知信息
 *
 * <AUTHOR>
 * @date 2020/7/11 21:31
 */
public class FriendNoticeDTO implements NoticeDTO {

    /** 用户ID */
    private Long userId;
    /** 好友ID */
    private Long friendId;
    /** 好友昵称 */
    private String friendNickname;
    /** 好友头像 */
    private String friendHeadPic;
    /** 好友关系变更类型 */
    private RelationChangeType relationChangeType;
    /** 亲密度等级 */
    private Integer imtimateLevel;
    /** 关系变更时间 */
    private Date changeTime;

    @Override
    public Map<String, Object> toParamsMap() {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("userId", this.userId);
        map.put("friendId", this.friendId);
        map.put("friendNickname", this.friendNickname);
        map.put("friendHeadPic", this.friendHeadPic);
        map.put("relationChangeType", this.relationChangeType);
        map.put("imtimateLevel", this.imtimateLevel);
        map.put("changeTime", this.changeTime);
        return map;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getFriendId() {
        return friendId;
    }

    public void setFriendId(Long friendId) {
        this.friendId = friendId;
    }

    public String getFriendNickname() {
        return friendNickname;
    }

    public void setFriendNickname(String friendNickname) {
        this.friendNickname = friendNickname;
    }

    public String getFriendHeadPic() {
        return friendHeadPic;
    }

    public void setFriendHeadPic(String friendHeadPic) {
        this.friendHeadPic = friendHeadPic;
    }

    public RelationChangeType getRelationChangeType() {
        return relationChangeType;
    }

    public void setRelationChangeType(RelationChangeType relationChangeType) {
        this.relationChangeType = relationChangeType;
    }

    public Integer getImtimateLevel() {
        return imtimateLevel;
    }

    public void setImtimateLevel(Integer imtimateLevel) {
        this.imtimateLevel = imtimateLevel;
    }

    public Date getChangeTime() {
        return changeTime;
    }

    public void setChangeTime(Date changeTime) {
        this.changeTime = changeTime;
    }

    @Override
    public Long getUserId() {
        return userId;
    }

    @Override
    public List<Long> getUserIds() {
        return null;
    }
}
