/*!
 * Copyright 2018, Julun, Inc.
 */

package com.tuowan.yeliao.commons.comp.rank;


import com.easyooo.framework.common.util.ListUtils;
import com.easyooo.framework.common.util.StringUtils;
import com.tuowan.yeliao.commons.comp.rank.dto.RawTupleDTO;
import com.tuowan.yeliao.commons.comp.rank.vo.CpRankInfoVO;
import com.tuowan.yeliao.commons.comp.rank.vo.RankUserInfoVO;
import com.tuowan.yeliao.commons.comp.support.RedisTupleUtils;
import com.tuowan.yeliao.commons.comp.support.TupleCallback;
import com.tuowan.yeliao.commons.config.redis.RedisKey;
import com.tuowan.yeliao.commons.config.redis.template.AbstractRedisTemplate;
import com.tuowan.yeliao.commons.config.redis.template.BusiRedisTemplate;
import com.tuowan.yeliao.commons.data.dto.user.UserBusiDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;
import redis.clients.jedis.exceptions.JedisNoScriptException;
import redis.clients.jedis.resps.Tuple;

import javax.annotation.concurrent.ThreadSafe;
import java.util.ArrayList;
import java.util.List;

import static com.tuowan.yeliao.commons.context.GlobalDataUtils.infoRankUser;


/**
 * 系统排行榜的通用实现部分组件，包括分值、查询、归档等功能实现
 *
 * <AUTHOR>
 * @date 2018/8/3 11:23
 */
@Component
@ThreadSafe
public class RankComponent implements ApplicationContextAware {
    /**
     * LOG instance
     */
    private final Logger LOG = LoggerFactory.getLogger(getClass());

    /**
     * <p> 榜单分值累加lua脚本Sha字符串，上线之前必须线初始化，
     * 一般情况，内容没有修改的情况SHA是不会变化的，但还请核对是否一致。</p>
     * <b> FBI WARING： 切忌上线之前初始化 </b>
     */
    private final String DEFAULT_RANK_SCORE_SHA1 = "ee1426d0c6d31720f6c81c924d8e8dfe66efa190";
    private final String DEFAULT_RANK_SCORE_SCRIPT = "local exist = redis.call('EXISTS', KEYS[1]) local ration = ARGV[3] local maxPoint = ration - 1 local minPoint = 1 local oldScore = redis.call('ZSCORE', KEYS[1],  ARGV[1]) if oldScore == false then oldScore = 0 end oldScore = math.floor(oldScore / ration) * ration local newScore = ARGV[2] * ration + oldScore local finalScore = newScore local minArr = redis.call('ZRANGEBYSCORE', KEYS[1], newScore, newScore + maxPoint, 'WITHSCORES', 'LIMIT', 0, 1) if next(minArr) == nil then finalScore = newScore + maxPoint else if minArr[2] - minPoint > newScore then finalScore = minArr[2] - minPoint end end redis.call('ZADD', KEYS[1], finalScore , ARGV[1]) return {math.floor(finalScore / ration), exist}";
    /**
     * 默认的分值放大倍数，意味着同比分最多支持该倍数-1的人数，
     * 超过该倍数的成员比分将会设置同比分最小值
     */
    private final Long DEFAULT_SCORE_RATION = 10000L;
    /**
     * 默认采用Busi缓存，可以通过子类重写注入的Redis源
     */
    protected AbstractRedisTemplate busiRedisTemplate;
    private String rankScoreSha1 = DEFAULT_RANK_SCORE_SHA1;

    /**
     * 获取指定榜单的用户分值
     */
    public Long getScore(RedisKey rankRedisKey, Long userId) {
        if (userId == null) {
            return -1L;
        }
        return getScore(rankRedisKey, userId.toString());
    }

    /**
     * 获取指定成员的比分值
     *
     * @param rankRedisKey
     * @param member
     * @return
     */
    public Long getScore(RedisKey rankRedisKey, String member) {
        return getScore(rankRedisKey, member, DEFAULT_SCORE_RATION);
    }


    /**
     * 获取指定名次的比分值
     *
     * @param rankRedisKey
     * @param top
     * @return
     */
    public Long getScoreByTop(RedisKey rankRedisKey, Long top) {
        List<Tuple> tuples = busiRedisTemplate.zrevrangeWithScores(rankRedisKey, top, top);
        if (ListUtils.isEmpty(tuples)) {
            return -1L;
        }
        Tuple tuple = tuples.iterator().next();
        return buildRawScore(tuple.getScore(), DEFAULT_SCORE_RATION);
    }

    /**
     * 获取指定榜单的用户排名
     * {@link #getTop(RedisKey, String)}
     */
    public Long getTop(RedisKey rankRedisKey, Long userId) {
        if (userId == null) {
            return -1L;
        }
        return getTop(rankRedisKey, userId.toString());
    }

    /**
     * 获取指定榜单的第一名分值
     */
    public Long getTop1Score(RedisKey rankRedisKey) {
        return getTop1Score(rankRedisKey, DEFAULT_SCORE_RATION);
    }

    /**
     * 获取指定榜单的第一名Tuple
     */
    public RawTupleDTO getTop1Tuple(RedisKey rankRedisKey) {
        return getTop1Tuple(rankRedisKey, DEFAULT_SCORE_RATION);
    }

    /**
     * 为指定的用户增加分值
     * {@link #incrScore(RedisKey, String, Long, Long)}
     */
    public Long incrScore(RedisKey rankRedisKey, Long userId, Long increment) {
        if (userId == null) {
            return -1L;
        }
        return incrScore(rankRedisKey, userId.toString(), increment, DEFAULT_SCORE_RATION);
    }


    /**
     * 为指定的成员增加分值，可以是任何对象ID
     * {@link #incrScore(RedisKey, String, Long, Long)}
     */
    public Long incrScore(RedisKey rankRedisKey, String memberId, Long increment) {
        return incrScore(rankRedisKey, memberId, increment, DEFAULT_SCORE_RATION);
    }


    /**
     * 为指定的用户重新设置分值
     * {@link #incrScore(RedisKey, String, Long, Long)}
     */
    public Long setScore(RedisKey rankRedisKey, Long userId, Long increment) {
        if (userId == null) {
            return -1L;
        }
        delMembers(rankRedisKey, userId.toString());
        return incrScore(rankRedisKey, userId.toString(), increment, DEFAULT_SCORE_RATION);
    }

    /**
     * 为指定的用户重新设置分值
     * {@link #incrScore(RedisKey, String, Long, Long)}
     */
    public Long setScore(RedisKey rankRedisKey, String memberId, Long increment) {
        if (memberId == null) {
            return -1L;
        }
        delMembers(rankRedisKey, memberId);
        return incrScore(rankRedisKey, memberId, increment, DEFAULT_SCORE_RATION);
    }

    /**
     * 分页查询排行榜用户列表
     */
    public List<RankUserInfoVO> getRankUserList(RedisKey rankRedisKey, Long offset, Long limit) {
        return getRankList(rankRedisKey, offset, limit, (element, score) -> {
            Long userId = Long.parseLong(element);
            return RankUserInfoVO.create(infoRankUser(userId), score);
        });
    }

    /**
     * 升序 分页查询排行榜用户列表
     */
    public List<RankUserInfoVO> getAscRankUserList(RedisKey rankRedisKey, Long offset, Long limit) {
        return getAscRankList(rankRedisKey, offset, limit, (element, score) -> {
            Long userId = Long.parseLong(element);
            return RankUserInfoVO.create(infoRankUser(userId), score);
        });
    }

    /**
     * 分页查询排行榜用户列表
     */
    public List<CpRankInfoVO> getRankCpList(RedisKey rankRedisKey, Long offset, Long limit) {
        return getRankList(rankRedisKey, offset, limit, (element, score) -> {
            String[] userIds = element.split("_");
            UserBusiDTO user1 = infoRankUser(Long.parseLong(userIds[0]));
            UserBusiDTO user2 = infoRankUser(Long.parseLong(userIds[1]));
            return CpRankInfoVO.create(element, user1, user2, score);
        });
    }

    /**
     * 分页查询排行榜用户列表
     */
    public List<RankUserInfoVO> getRankUserList(RedisKey rankRedisKey, Long min, Long max, Integer offset, Integer limit) {
        return getRankList(rankRedisKey, min.doubleValue(), max.doubleValue(), offset, limit, true, (element, score) -> {
            Long userId = Long.parseLong(element);
            return RankUserInfoVO.create(infoRankUser(userId), score);
        });
    }

    /**
     * 查询当前指定用户排位信息
     */
    public RankUserInfoVO getRankUser(RedisKey rankRedisKey, Long userId, Integer limit) {
        RankUserInfoVO rankInfo = getRankInfo(rankRedisKey, userId.toString(), (String element, Long score) -> RankUserInfoVO.create(infoRankUser(Long.parseLong(element)), score));
        rankInfo.setRanking(getRankingStr(getTop(rankRedisKey, userId.toString()), limit));
        return rankInfo;
    }

    /**
     * 查询当前指定用户排位信息
     */
    public CpRankInfoVO getRankCpUser(RedisKey rankRedisKey, String relationId, Integer limit) {
        CpRankInfoVO rankInfo = getRankInfo(rankRedisKey, relationId, (String element, Long score) -> {
            String[] userIds = element.split("_");
            UserBusiDTO user1 = infoRankUser(Long.parseLong(userIds[0]));
            UserBusiDTO user2 = infoRankUser(Long.parseLong(userIds[1]));
            return CpRankInfoVO.create(element, user1, user2, score);
        });
        rankInfo.setRanking(getRankingStr(getTop(rankRedisKey, relationId), limit));
        return rankInfo;
    }

    private String getRankingStr(Long top, Integer limit) {
        if (top < 0) {
            return "未上榜";
        }
        top = top + 1;
        if (top > limit) {
            return limit + "+";
        }
        return top.toString();
    }

    /**
     * 分页查询当前榜单结果集，外部根据比分和成员封装成要显示的数据
     *
     * @param rankRedisKey 榜单Key
     * @param offset       开始索引，从0开始
     * @param limit        取多少条结果集，如果-1返回所有
     * @return 返回结果集，永远不会返回NULL
     */
    public <T> List<T> getRankList(RedisKey rankRedisKey, Long offset, Long limit, TupleCallback<T> cb) {
        // 查询指定名次的排行榜
        List<Tuple> tuples = busiRedisTemplate.zrevrangeWithScores(rankRedisKey, offset, getEnd(offset, limit));
        return parseTuplesByCallback(tuples, cb);
    }

    /**
     * 分页查询当前榜单结果集，外部根据比分和成员封装成要显示的数据
     *
     * @param rankRedisKey 榜单Key
     * @param offset       开始索引，从0开始
     * @param limit        取多少条结果集，如果-1返回所有
     * @return 返回结果集，永远不会返回NULL
     */
    public <T> List<T> getAscRankList(RedisKey rankRedisKey, Long offset, Long limit, TupleCallback<T> cb) {
        // 查询指定名次的排行榜
        List<Tuple> tuples = busiRedisTemplate.zrangeWithScores(rankRedisKey, offset, getEnd(offset, limit));
        return parseTuplesByCallback(tuples, cb);
    }

    /**
     * 根据分值返回 成员+分值
     */
    public <T> List<T> getRankList(RedisKey rankRedisKey, Double min, Double max, Integer offset, Integer limit, boolean reverse, TupleCallback<T> cb) {
        max = (max + 1) * DEFAULT_SCORE_RATION - 1;
        min = min * DEFAULT_SCORE_RATION;
        List<Tuple> tuples;
        if (reverse) {
            tuples = busiRedisTemplate.zrevrangeByScoreWithScores(rankRedisKey, max, min, offset, limit);
        } else {
            tuples = busiRedisTemplate.zrangeByScoreWithScores(rankRedisKey, min, max, offset, limit);
        }
        return parseTuplesByCallback(tuples, cb);
    }

    private <T> List<T> parseTuplesByCallback(List<Tuple> tuples, TupleCallback<T> cb) {
        if (ListUtils.isNotEmpty(tuples)) {
            List<T> rankList = new ArrayList<>();
            for (Tuple tuple : tuples) {
                rankList.add(cb.doCallback(tuple.getElement(), buildRawScore(tuple.getScore(), DEFAULT_SCORE_RATION)));
            }
            return rankList;
        }
        return new ArrayList<>();
    }


    /**
     * 获取榜单指定成员的排位信息
     *
     * @param rankRedisKey
     * @param element
     * @param cb
     * @param <T>
     * @return
     */
    public <T> T getRankInfo(RedisKey rankRedisKey, String element, TupleCallback<T> cb) {
        Long score = getScore(rankRedisKey, element);
        score = Math.max(0, score);
        return cb.doCallback(element, score);
    }

    /**
     * 最原始的方法，蒋比分转换成真实比分
     *
     * @param rankRedisKey
     * @param offset
     * @param limit        取多少条结果集，如果-1返回所有
     */
    public List<RawTupleDTO> getRankList(RedisKey rankRedisKey, Integer offset, Integer limit) {
        return getRankList(rankRedisKey, offset.longValue(), limit.longValue());
    }

    /**
     * 最原始的方法，蒋比分转换成真实比分
     *
     * @param rankRedisKey
     * @param offset
     * @param limit        取多少条结果集，如果-1返回所有
     */
    public List<RawTupleDTO> getRankList(RedisKey rankRedisKey, Long offset, Long limit) {
        List<Tuple> tuples = busiRedisTemplate.zrevrangeWithScores(rankRedisKey, offset, getEnd(offset, limit));
        return RedisTupleUtils.parseTuples(tuples, (element, score) -> {
            RawTupleDTO dto = new RawTupleDTO();
            dto.setElement(element);
            dto.setScore(buildRawScore(score * 1D, DEFAULT_SCORE_RATION));
            return dto;
        });
    }

    /**
     * 原始榜单列表，数据和redis一致
     *
     * @param rankRedisKey
     * @param offset
     * @param limit        取多少条结果集，如果-1返回所有
     * @return
     */
    public List<RawTupleDTO> getOriginRankList(RedisKey rankRedisKey, Long offset, Long limit) {
        List<Tuple> tuples = busiRedisTemplate.zrevrangeWithScores(rankRedisKey, offset, getEnd(offset, limit));
        return RedisTupleUtils.parseTuples(tuples, (element, score) -> {
            RawTupleDTO dto = new RawTupleDTO();
            dto.setElement(element);
            dto.setScore(score);
            return dto;
        });
    }

    /**
     * 获取在某一分数区间的人数 min默认为0，max默认为榜单最高值
     *
     * @param rankRedisKey
     * @param min
     * @param max
     * @return
     */
    public Long getCountByScoreRange(RedisKey rankRedisKey, Long min, Long max) {
        if (max == null) {
            max = getTop1Score(rankRedisKey);
        }
        max = (max + 1) * DEFAULT_SCORE_RATION;
        if (min == null) {
            min = 0L;
        }
        min = min * DEFAULT_SCORE_RATION;
        return busiRedisTemplate.zcount(rankRedisKey, min, max);
    }

    /**
     * <p>递增榜单的比分值，核心实现通过redis lua脚本做<b>原子性</b>累加比分</p>
     * <p>
     * <b>同比分排序问题：按照谁先到原则</b>
     * 1、zscore查找对象的加之前的比分
     * 2、zrangebyscore查找与新比分同分支且0到倍率-1之间最小的分值
     * 3、zadd将上一步骤得到的最小分值减1得到当前对象的最终比分值
     * </p>
     * <p>
     * <b>关于有效期问题</b>
     * <p> 榜单一般都会设置有效期，通常只要设置一次有效期即可
     * 但通过增加比分相关操作无法知道当前榜单是否设置过有效期，
     * 解决办法可以每次都去ttl判断是否设置过，但效率不高！ </p>
     * <b>通过Lua脚本实现</b>
     * <p>当lua脚本执行会每次都执行exists Key，把结果与新比分一并返回。</p>
     * </p>
     *
     * @param rankRedisKey 榜单Key
     * @param member       排行榜对象ID，目前大部分都是userId
     * @param increment    需要递加的分值
     * @param scoreRation  分值放大倍数，当同比分的情况下按先到者排第一，
     *                     该参数意味着redis存储的比分值会放大该倍数
     * @return 返回新的比分值，如果返回-1说明参数或脚本有问题执行失败
     */
    protected Long incrScore(RedisKey rankRedisKey, String member, Long increment, Long scoreRation) {
        // check params
        if (rankRedisKey == null || StringUtils.isEmpty(member) || increment == null || scoreRation == null) {
            return -1L;
        }

        try {
            // 调用LUA执行加分操作
            return callRedisLUAGetScore(rankRedisKey, member, increment, scoreRation);
        } catch (JedisNoScriptException e) {
            LOG.warn("初始化排行榜加分的LUA脚本程序..", e);
            rankScoreSha1 = busiRedisTemplate.scriptLoad(DEFAULT_RANK_SCORE_SCRIPT);
            return callRedisLUAGetScore(rankRedisKey, member, increment, scoreRation);
        }
    }

    protected Long callRedisLUAGetScore(RedisKey rankRedisKey, String member, Long increment, Long scoreRation) {
        // 为榜单成员增加比分
        Object returnObj = busiRedisTemplate.evalsha(rankScoreSha1, 1, rankRedisKey.toKey(), member, Long.toString(increment), Long.toString(scoreRation));
        if (returnObj == null) {
            throw new JedisNoScriptException("NOSCRIPT No matching script. Please use EVAL.");
        }
        List<Object> values = (List<Object>) returnObj;
        Long newScore = (Long) values.get(0);
        Long existed = (Long) values.get(1);

        // 设置有效期(仅当第一次设置这个榜单Key才会被设置)
        if (0 == existed && rankRedisKey.toSeconds() != null) {
            busiRedisTemplate.expire(rankRedisKey);
        }

        // 返回新的比分值
        return newScore;
    }

    /**
     * 获取成员数
     *
     * @param rankRedisKey
     * @return
     */
    public Long count(RedisKey rankRedisKey) {
        return busiRedisTemplate.zcount(rankRedisKey, 0, Double.MAX_VALUE);
    }

    /**
     * 统计对应分值数量
     */
    public Long count(RedisKey rankRedisKey, Long min, Long max) {
        max = (max + 1) * DEFAULT_SCORE_RATION;
        min = min * DEFAULT_SCORE_RATION;
        return busiRedisTemplate.zcount(rankRedisKey, min, max);
    }

    /**
     * 删除指定榜单成员数量
     *
     * @param rankRedisKey
     * @return
     */
    public Long delMembers(RedisKey rankRedisKey, String... members) {
        return busiRedisTemplate.zrem(rankRedisKey, members);
    }

    /**
     * 获取指定成员的目前排位
     *
     * @return 从0第一名，1第二名，2第三名...依次类推，-1表示未上榜
     */
    public Long getTop(RedisKey rankRedisKey, String member) {
        Long top = busiRedisTemplate.zrevrank(rankRedisKey, member);
        // 未上榜
        if (top == null) {
            return -1L;
        }
        return top;
    }

    /**
     * 获取第一名用户信息
     *
     * @param rankKey
     * @return
     */
    public RankUserInfoVO getTopUser(RedisKey rankKey) {
        RawTupleDTO dto = getTop1Tuple(rankKey);
        if (dto == null) {
            return null;
        }
        Long userId = Long.parseLong(dto.getElement());
        return RankUserInfoVO.create(infoRankUser(userId), dto.getScore());
    }

    /**
     * 返回指定成员排行榜比分值
     *
     * @param member 成员
     * @return 当前成员的真实比分
     */
    protected Long getScore(RedisKey rankRedisKey, String member, Long scoreRation) {
        Double scaleScore = busiRedisTemplate.zscore(rankRedisKey, member);
        if (scaleScore == null) {
            return -1L;
        }
        return (long) Math.floor(scaleScore / scoreRation);
    }

    /**
     * 返回指榜单第一名的比分值
     */
    protected Long getTop1Score(RedisKey rankRedisKey, Long scoreRation) {
        RawTupleDTO tuple = getTop1Tuple(rankRedisKey, scoreRation);
        if (tuple == null) {
            return -1L;
        }
        return tuple.getScore();
    }


    public RawTupleDTO getTop1Tuple(RedisKey rankRedisKey, Long scoreRation) {
        List<Tuple> tuples = busiRedisTemplate.zrevrangeWithScores(rankRedisKey, 0, 0);
        if (ListUtils.isEmpty(tuples)) {
            return null;
        }
        return buildRawTuple(tuples.iterator().next(), scoreRation);
    }

    private RawTupleDTO buildRawTuple(Tuple tuple, Long scoreRation) {
        if (tuple != null) {
            RawTupleDTO dto = new RawTupleDTO();
            dto.setElement(tuple.getElement());
            dto.setScore(buildRawScore(tuple.getScore(), scoreRation));

            return dto;
        }
        return null;
    }

    private Long buildRawScore(Double scaleScore, Long scoreRation) {
        return (long) Math.floor(scaleScore / scoreRation);
    }

    private Long getEnd(Long offset, Long limit) {
        if (limit == -1L) {
            return -1L;
        }
        return offset + limit - 1L;
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.busiRedisTemplate = applicationContext.getBean(BusiRedisTemplate.class);
    }

}
