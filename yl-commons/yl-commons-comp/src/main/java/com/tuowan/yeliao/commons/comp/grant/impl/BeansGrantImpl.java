/*!
 * Copyright 2018, Julun, Inc.
 */

package com.tuowan.yeliao.commons.comp.grant.impl;


import com.tuowan.yeliao.commons.comp.grant.Grant;
import com.tuowan.yeliao.commons.comp.grant.dto.GrantDetailDTO;
import com.tuowan.yeliao.commons.comp.user.UserBusiComponent;
import com.tuowan.yeliao.commons.data.entity.config.TAward;
import com.tuowan.yeliao.commons.data.entity.config.TAwardDetail;
import com.tuowan.yeliao.commons.data.enums.config.AwardGrantType;
import com.tuowan.yeliao.commons.data.enums.config.AwardType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 充值金币奖励发放
 *
 * <AUTHOR>
 * @date 2018/7/17 16:35
 */
@Component
public class BeansGrantImpl implements Grant {

    @Autowired
    private UserBusiComponent userBusiComponent;

    @Override
    public GrantDetailDTO doGrant(AwardGrantType grantType, Long userId, TAward cfg, TAwardDetail d, Integer awardTimes, boolean unAward, String remark) {
        Long beans = d.getCount() * 1L * awardTimes;
        if (!unAward) {
            userBusiComponent.addRechargeBeans(userId, beans);
        }
        return new GrantDetailDTO("充值金币", beans);
    }

    @Override
    public AwardType getSupportType() {
        return AwardType.Beans;
    }
}
