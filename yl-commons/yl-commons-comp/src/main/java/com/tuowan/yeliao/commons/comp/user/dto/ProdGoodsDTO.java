package com.tuowan.yeliao.commons.comp.user.dto;

import com.tuowan.yeliao.commons.data.entity.config.TProdGoodsType;
import com.tuowan.yeliao.commons.data.enums.config.ProdGoodsType;

/**
 * 物品信息
 *
 * <AUTHOR>
 * @date 2020/8/29 17:30
 */
public class ProdGoodsDTO {

    /** 物品名称 */
    private String goodsName;
    /** 物品图片 */
    private String goodPic;
    /** 物品类型 */
    private ProdGoodsType goodsType;
    /** 物品类型配置 */
    private TProdGoodsType goodsTypeCfg;

    public static ProdGoodsDTO create(String goodsName,String goodPic, TProdGoodsType goodsTypeCfg) {
        ProdGoodsDTO dto = new ProdGoodsDTO();
        dto.setGoodsName(goodsName);
        dto.setGoodPic(goodPic);
        dto.setGoodsType(goodsTypeCfg.getGoodsType());
        dto.setGoodsTypeCfg(goodsTypeCfg);
        return dto;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public String getGoodPic() {
        return goodPic;
    }

    public void setGoodPic(String goodPic) {
        this.goodPic = goodPic;
    }

    public ProdGoodsType getGoodsType() {
        return goodsType;
    }

    public void setGoodsType(ProdGoodsType goodsType) {
        this.goodsType = goodsType;
    }

    public TProdGoodsType getGoodsTypeCfg() {
        return goodsTypeCfg;
    }

    public void setGoodsTypeCfg(TProdGoodsType goodsTypeCfg) {
        this.goodsTypeCfg = goodsTypeCfg;
    }
}
