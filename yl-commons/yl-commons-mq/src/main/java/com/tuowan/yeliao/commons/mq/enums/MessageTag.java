package com.tuowan.yeliao.commons.mq.enums;

import com.easyooo.framework.common.util.EnumUtils;
import com.tuowan.yeliao.commons.config.UnifiedConfig;

/**
 * 消息标签定义
 *
 * <AUTHOR>
 * @date 2022/4/11 19:54
 */
public enum MessageTag implements EnumUtils.IDEnum {

    Unknown(null, "UNKNOWN", "未定义"),

    GreenCheck(MessageTopic.Default, "GREEN_CHECK", "鉴黄业务消费者"),
    UserTask(MessageTopic.Default, "USER_TASK", "用户任务消费者"),
    UserAction(MessageTopic.Default, "USER_ACTION", "用户行为消费者"),
    Log(MessageTopic.Default, "LOG", "日志消费者"),
    AcctBusi(MessageTopic.Default, "ACCT_BUSI", "账户通用业务"),

    Message(MessageTopic.Social, "MESSAGE", "消息发送消费者"),
    AddExp(MessageTopic.Social, "ADD_EXP", "增加用户经验消费者"),
    FriendChat(MessageTopic.Social, "FRIEND_CHAT", "好友聊天消费者"),
    SocialBusi(MessageTopic.Social, "SOCIAL_BUSI", "社交通用业务"),
    ;

    private MessageTopic topic;
    private String id;
    private String desc;

    MessageTag(MessageTopic topic, String id, String desc) {
        this.topic = UnifiedConfig.isProdEnv() ? topic : MessageTopic.Default;
        this.id = id;
        this.desc = desc;
    }

    public MessageTopic getTopic() {
        return topic;
    }

    @Override
    public String getId() {
        return id;
    }

    @Override
    public String getDesc() {
        return desc;
    }
}
