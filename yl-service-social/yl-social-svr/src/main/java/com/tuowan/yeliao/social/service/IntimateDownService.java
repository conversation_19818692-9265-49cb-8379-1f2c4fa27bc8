/*!
 * Copyright 2018, Julun, Inc.
 */

package com.tuowan.yeliao.social.service;

import com.easyooo.framework.common.util.DateUtils;
import com.easyooo.framework.common.util.ListUtils;
import com.tuowan.yeliao.commons.comp.notice.NoticeComponent;
import com.tuowan.yeliao.commons.data.entity.user.UUserBasic;
import com.tuowan.yeliao.commons.data.entity.user.UUserExt;
import com.tuowan.yeliao.commons.data.enums.config.NoticeSysType;
import com.tuowan.yeliao.commons.data.manager.user.UserInfoManager;
import com.tuowan.yeliao.social.comp.friend.IntimateComponent;
import com.tuowan.yeliao.social.data.dto.friend.IntimateDownMsgDTO;
import com.tuowan.yeliao.social.data.entity.FRelationBasic;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 亲密度降级业务逻辑
 *
 * <AUTHOR>
 * @date 2020/7/23 15:13
 */
@Service
public class IntimateDownService {

    private final Logger LOG = LoggerFactory.getLogger(this.getClass());


    /**
     * 单次扣减的亲密度值
     */
    private static final Integer DOWN_NUM = -10;
    /**
     * 不活跃天数3天
     */
    private static final Integer NOT_ACTICE_DAYS = 3;
    @Autowired
    private IntimateComponent intimateComponent;
    @Autowired
    private NoticeComponent noticeComponent;
    @Autowired
    private UserInfoManager userInfoManager;

    public void saveIntimateDown(FRelationBasic basic, Map<Long, List<IntimateDownMsgDTO>> downMap) {
        // 判断 smallUserId 和 bigUserId 用户信息是否都存在 不存在则清除关系
        Long smallUserId = basic.getSmallUserId();
        Long bigUserId = basic.getBigUserId();
        UUserBasic smallUser = userInfoManager.getUserBasic(smallUserId);
        UUserBasic bigUser = userInfoManager.getUserBasic(bigUserId);
        if(Objects.isNull(smallUser) || Objects.isNull(bigUser)){
            intimateComponent.deleteUserRelation(basic);
        }
        // 检查要扣减的天数
        Date lastAddTime = DateUtils.trunc(basic.getLastAddtime());
        Date now = DateUtils.trunc(new Date());
        Date lastDownTime = basic.getLastDowntime() == null ? null : DateUtils.trunc(basic.getLastDowntime());

        // 检查要扣减的天数
        // 相差的总天数
        Long totalDays = DateUtils.getDiffDays(lastAddTime, now);
        // 首次扣减或者上次扣减之后，密友再次互动了，直接忽略上次扣减时间
        Long deductDays = totalDays - NOT_ACTICE_DAYS;

        // 上次扣过了
        if (lastDownTime != null) {
            // 判断上一次扣减是否至少相差4天以上，否则数据有问题
            Long lastDiffDays = DateUtils.getDiffDays(lastAddTime, lastDownTime);
            if (lastDiffDays >= NOT_ACTICE_DAYS + 1) {
                // 减去上一次扣减的时间
                deductDays = totalDays - lastDiffDays;
            }
        }
        // 需要扣减亲密度
        if (deductDays > 0) {
            Long remainIntimate = basic.getIntimateNum();
            for (int i = 0; i < deductDays; i++) {
                if (remainIntimate <= 20) {
                    remainIntimate = 0L;
                    break;
                }
                remainIntimate -= Math.max(20L, (long) Math.ceil(remainIntimate * 0.15));
            }
            Long deductIntimate = remainIntimate - basic.getIntimateNum();
            FRelationBasic relationBasic = intimateComponent.addIntimate(basic.getSmallUserId(), basic.getBigUserId(), deductIntimate);
            // 组装符合发出系统消息的数据
            addDownMsgDTO(relationBasic, basic.getLastAddtime(), deductIntimate, downMap);
        }
    }

    /**
     * 组装符合发出系统消息的数据
     *
     * @param basic
     * @param lastAddtime
     * @param deductIntimate
     * @param downMap
     */
    private void addDownMsgDTO(FRelationBasic basic, Date lastAddtime, Long deductIntimate, Map<Long, List<IntimateDownMsgDTO>> downMap) {
        if (basic.getIntimateLevel() <= 0) {
            return;
        }
        // 是否最近七天未登录，否则跳过
        Long userId = basic.getSmallUserId();
        Long friendId = basic.getBigUserId();
        Long now = System.currentTimeMillis();
        UUserExt ext = userInfoManager.getUserExt(userId);
        // 超过7天未登录
        if (DateUtils.plusDays(ext.getLastOpenTime(), 7).getTime() <= now) {
            return;
        }
        UUserExt friendExt = userInfoManager.getUserExt(friendId);
        // 超过7天未登录
        if (DateUtils.plusDays(friendExt.getLastOpenTime(), 7).getTime() <= now) {
            return;
        }
        IntimateDownMsgDTO msgDTO = new IntimateDownMsgDTO(basic, lastAddtime, deductIntimate);
        List<IntimateDownMsgDTO> userList = new ArrayList<>();
        if (ListUtils.isNotEmpty(downMap.get(userId))) {
            userList = downMap.get(userId);
        }
        userList.add(msgDTO);
        downMap.put(userId, userList);
        List<IntimateDownMsgDTO> friendList = new ArrayList<>();
        if (ListUtils.isNotEmpty(downMap.get(friendId))) {
            friendList = downMap.get(friendId);
        }
        friendList.add(msgDTO);
        downMap.put(friendId, friendList);
    }

    /**
     * 亲密度下降系统消息
     *
     * @param dto
     */
    public void sendDownMsg(IntimateDownMsgDTO dto) {
        FRelationBasic basic = dto.getBasic();
        Date lastAddtime = dto.getLastAddtime();
        Long deductIntimate = dto.getDeductIntimate();
        if (basic.getIntimateLevel() <= 0) {
            return;
        }
        Long userId = basic.getSmallUserId();
        Long friendId = basic.getBigUserId();
        // 系统消息
        Map<String, Object> paramMap = new HashMap<>();
        Long diffDay = DateUtils.getDiffDays(lastAddtime, new Date());
        paramMap.put("days", diffDay <= 1 ? 1 : diffDay);
        paramMap.put("downValue", -deductIntimate);
        // 今日重复用户则不会发送
        if (intimateComponent.saveIntimateDownMsgUserCache(userId)) {
            paramMap.put("nickname", userInfoManager.getUserBasic(friendId).getNickname());
            paramMap.put("overrideTouchValue", friendId);
            noticeComponent.sendSystemNotice(userId, NoticeSysType.IntimateDown, paramMap);
        }
        // 今日重复用户则不会发送
        if (intimateComponent.saveIntimateDownMsgUserCache(friendId)) {
            paramMap.put("nickname", userInfoManager.getUserBasic(userId).getNickname());
            paramMap.put("overrideTouchValue", userId);
            noticeComponent.sendSystemNotice(friendId, NoticeSysType.IntimateDown, paramMap);
        }
    }

}
