package com.tuowan.yeliao.social.service;

import com.easyooo.framework.common.util.ListUtils;
import com.tuowan.yeliao.commons.comp.message.MessageComponent;
import com.tuowan.yeliao.commons.context.GlobalUtils;
import com.tuowan.yeliao.commons.core.additions.UrlParamsMap;
import com.tuowan.yeliao.commons.core.enums.business.BusinessDataKey;
import com.tuowan.yeliao.commons.core.enums.general.BoolType;
import com.tuowan.yeliao.commons.core.enums.general.ReviewStatus;
import com.tuowan.yeliao.commons.data.entity.user.UUserBasic;
import com.tuowan.yeliao.commons.data.entity.user.UUserExt;
import com.tuowan.yeliao.commons.data.enums.config.SettingsType;
import com.tuowan.yeliao.commons.data.enums.user.SexType;
import com.tuowan.yeliao.commons.data.manager.commons.SeqManager;
import com.tuowan.yeliao.commons.data.manager.user.UserInfoManager;
import com.tuowan.yeliao.commons.data.manager.user.UserVisitManager;
import com.tuowan.yeliao.commons.data.support.config.impl.SettingsConfig;
import com.tuowan.yeliao.social.comp.friend.FateMatchComponent;
import com.tuowan.yeliao.social.comp.friend.UserInviteComponent;
import com.tuowan.yeliao.social.comp.friend.VisitComponent;
import com.tuowan.yeliao.social.comp.home.HomeRecomComponent;
import com.tuowan.yeliao.social.comp.log.LogComponent;
import com.tuowan.yeliao.social.data.entity.SCosmos;
import com.tuowan.yeliao.commons.data.enums.social.InviteBindType;
import com.tuowan.yeliao.social.data.entity.room.FChatRoom;
import com.tuowan.yeliao.social.data.manager.cosmos.CosmosManager;
import com.tuowan.yeliao.social.data.manager.friend.ChatManager;
import com.tuowan.yeliao.social.data.manager.room.RoomManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class ConmonService {
    /** 虚拟访客数量 */
    private static final Integer FICTITIOUS_VISITOR_COUNT = 3;
    private final Logger LOG = LoggerFactory.getLogger(this.getClass());
    @Autowired
    private CosmosManager cosmosManager;
    @Autowired
    private UserInfoManager userInfoManager;
    @Autowired
    private UserVisitManager userVisitManager;
    @Autowired
    private VisitComponent visitComponent;
    @Autowired
    private UserInviteComponent userInviteComponent;
    @Autowired
    private MessageComponent messageComponent;
    @Autowired
    private SeqManager seqManager;
    @Autowired
    private LogComponent logComponent;
    @Autowired
    private ChatManager chatManager;
    @Autowired
    private FateMatchComponent fateMatchComponent;
    @Autowired
    private HomeRecomComponent homeRecomComponent;
    @Autowired
    private RoomManager roomManager;

    /**
     * 社交模块处理用户注册
     */
    public void saveRegisterBindInvitor(){
        Long userId = GlobalUtils.extLong(BusinessDataKey.UserId);
        UUserExt userExt = userInfoManager.getUserExt(userId);
        if(Objects.isNull(userExt) || Objects.isNull(userExt.getInviteUserId())){
            return;
        }
        userInviteComponent.inviteOpt(userExt.getInviteUserId(), userId, InviteBindType.Register);
    }

    /**
     * 添加模拟访客
     */
    public void saveVirtualVisitor() {
        Long userId = GlobalUtils.uid();
        SexType sexType = GlobalUtils.sexType();
        // 查询访客数据
        List<Long> friendIds = visitComponent.getVirtualVisitor(userId, sexType, visitComponent.getExcludeUser(userId));
        if (ListUtils.isEmpty(friendIds)) {
            return;
        }
        long addSeconds = 1000L;
        long time = System.currentTimeMillis();
        for (Long friendId : friendIds) {
            if (userId.equals(friendId)) {
                return;
            }
            // 模拟好友访问我
            userVisitManager.putWaitAddVisitorQueue(friendId, userId, time + addSeconds);
            if (addSeconds <= 1000L) {
                addSeconds = 10 * 1000L;
            } else if (addSeconds <= 10 * 1000L) {
                addSeconds = 30 * 1000L;
            }
        }
        // 保存此次添加时间
        userVisitManager.cacheAddVisitorTime(userId);
        // 保存用户访问记录
        visitComponent.batchInsertVisitRank(userId, friendIds);
    }

    /**
     * 宇宙免审核
     */
    public void saveCosmosAuditFree() {
        Long cosmosId = GlobalUtils.extLong(BusinessDataKey.CosmosId);
        if (Objects.isNull(cosmosId)) {
            return;
        }
        SCosmos cosmos = cosmosManager.getCosmos(cosmosId);
        if (Objects.isNull(cosmos) || ReviewStatus.Wait != cosmos.getStatus()) {
            return;
        }
        BoolType boolType = SettingsConfig.getBoolType(SettingsType.CosmosAuditFreeSwitch);
        if (BoolType.True != boolType) {
            return;
        }
        // 修改数据库状态
        SCosmos update = new SCosmos(cosmosId);
        update.setStatus(ReviewStatus.Pass);
        cosmosManager.updateCosmos(update);
        // 放入宇宙集合（这里我们将分数设置为当前的时间戳）
        UUserBasic userBasic = userInfoManager.getUserBasic(cosmos.getUserId());
        cosmosManager.putCosmosCollect(cosmos.getCosmosId(), new Date().getTime(), userBasic.getSex());
    }
}
