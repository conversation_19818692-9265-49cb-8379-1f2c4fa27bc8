package com.tuowan.yeliao.social.listener;

import com.easyooo.framework.common.util.EnumUtils;
import com.tuowan.yeliao.commons.context.GlobalContext;
import com.tuowan.yeliao.commons.context.GlobalUtils;
import com.tuowan.yeliao.commons.core.enums.busicode.BusiCodeDefine;
import com.tuowan.yeliao.commons.core.enums.business.BusinessDataKey;
import com.tuowan.yeliao.commons.data.enums.social.FemaleMsg;
import com.tuowan.yeliao.commons.data.enums.user.SexType;
import com.tuowan.yeliao.commons.data.enums.user.UserBusiKeyMark;
import com.tuowan.yeliao.commons.mq.annotation.Consumer;
import com.tuowan.yeliao.commons.mq.enums.MessageTag;
import com.tuowan.yeliao.commons.mq.listener.ContextDispatchMessageListener;
import com.tuowan.yeliao.social.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 好友聊天消费者
 *
 * <AUTHOR>
 * @date 2022/4/18 11:44
 */
@Component
@Consumer(MessageTag.FriendChat)
public class ChatConsumeListener extends ContextDispatchMessageListener {

    @Autowired
    private ChatConsumeService chatConsumeService;
    @Autowired
    private UserExpService userExpService;
    @Autowired
    private NetCallService netCallService;
    @Autowired
    private PostService postService;
    @Autowired
    private MessageService messageService;

    @Override
    public void doProcessMessage(GlobalContext globalContext) {
        BusiCodeDefine busiCode = GlobalUtils.busiCodeName();
        Long userId = GlobalUtils.uid();
        SexType userSex = GlobalUtils.sexType();
        Long friendId = GlobalUtils.formLong("friendId");
        SexType friendSex = EnumUtils.byName(GlobalUtils.extValue(BusinessDataKey.FriendSex), SexType.class);
        // 扣费&分成以及邀友收益处理
        if (BusiCodeDefine.SendChatMsg == busiCode) {
            Long totalBeans = GlobalUtils.extLong(BusinessDataKey.TotalBeans, 0L);
            doProcess("私聊发消息", () -> {
                chatConsumeService.saveChatWaitConsumeAndIntimate(userId, friendId, userSex, friendSex, totalBeans);
            });
            doProcess("私聊亲密度等级处理", () -> {
                // 我消耗金币发送了私聊消息，增加我两的亲密度
                chatConsumeService.saveIntimateDeal(userId, friendId, totalBeans);
            });
            doProcess("私聊财富等级魅力等级处理", () -> {
                // 我消耗金币发送了私聊消息，增加自己的财富值
                userExpService.saveAddUserExp(userId, totalBeans);
                // 我消耗金币发送了私聊消息，增加对方的魅力值
                userExpService.saveAddCharmExp(friendId, totalBeans);
            });
            doProcess("私聊聊天内容保存", () -> {
                chatConsumeService.saveChatContent(userId, userSex, friendId, friendSex);
            });
            doProcess("男女用户每天首次私信交互处理", () -> {
                FemaleMsg femaleMsg = SexType.Female == userSex ? FemaleMsg.ActMsg : FemaleMsg.RpMsg;
                chatConsumeService.saveUserFirstMsgDayDeal(userSex, userId, friendId, femaleMsg);
            });
            doProcess("私聊发消息任务处理", () -> {
                chatConsumeService.saveMsgChatTaskProcess(userId, friendId);
            });
            doProcess("记录用户双方首次交互建立时间", () -> {
                chatConsumeService.saveFemaleFfActMsgTime(userId, friendId);
            });
            /*doProcess("女用户前几次私聊风控次数记录", () -> {
                chatConsumeService.saveFemaleMsgRiskCtrlRecord(userId, userSex, friendId);
            });*/
            /*doProcess("男用户回复女用户打招呼处理", () -> {
                chatConsumeService.dealMaleReplyFemaleChatUp(userId, friendId);
            });*/
            /*doProcess("私聊输入框引导开通VIP", () -> {
                messageService.sendChatInputBannerMsg(userId);
            });
            doProcess("男用户回复女用户打招呼处理", () -> {
                chatConsumeService.dealMaleReplyFemaleChatUp(userId, friendId);
            });*/
            /*doProcess("男用户第一次发起聊天提示", () -> {
                chatConsumeService.firstChatTipsInfo(userId, friendId);
            });*/
            /*doProcess("国庆幸运大转盘抽奖活动处理", () -> {
                chatConsumeService.saveLuckyWheelActChatDeal(userId, friendId);
            });
            doProcess("用户违规关键词判断", () -> {
                chatConsumeService.updateIllegallyKeyword(userId, content);
            });*/
            return;
        }
        if (BusiCodeDefine.SendChatRoomMsg == busiCode) {
            doProcess("聊天室聊天内容保存", () -> {
                chatConsumeService.saveChatRoomContent();
            });
            doProcess("聊天室发消息任务处理", () -> {
                Long chatRoomId = GlobalUtils.extLong(BusinessDataKey.ChatRoomId);
                chatConsumeService.saveMsgChatRoomTaskProcess(userId, chatRoomId);
            });
            doProcess("聊天室聊天聊天室热度处理", () -> {
                chatConsumeService.saveCrHotForSendMsg();
            });
            return;
        }
        if (BusiCodeDefine.SendChatGift == busiCode) {
            doProcess("幸运礼物抽奖", () -> {
                chatConsumeService.saveLuckyGiftDraw();
            });
            doProcess("私聊送礼亲密度处理", () -> {
                chatConsumeService.saveSendGiftIntimate(friendId);
            });
            doProcess("私聊送礼礼物亲密度处理", () -> {
                chatConsumeService.saveSendGiftOfGiftIntimate(friendId);
            });
            doProcess("用户收礼数据记录", () -> {
                chatConsumeService.saveStatUserReceiveGift(friendId);
            });
            doProcess("统计动态打赏人数", () -> {
                postService.saveProcessPostReward(userId);
            });
            /*doProcess("统计送礼金额", () -> {
                chatConsumeService.saveStatSendGiftMoneyLimit(userId, friendId);
            });*/
            doProcess("送礼任务处理", () -> {
                chatConsumeService.saveSendGiftTaskProcess(userId);
            });
            doProcess("收礼任务处理", () -> {
                chatConsumeService.saveReceiveGiftTaskProcess(friendId);
            });
            doProcess("送礼电视墙处理", () -> {
                chatConsumeService.saveSendGiftTvWallDeal(userId, friendId);
            });
            doProcess("聊天室送礼奖池处理", () -> {
                chatConsumeService.saveSendGiftInCrPondDeal(userId);
            });
            doProcess("女用户指标数据统计", () -> {
                chatConsumeService.saveFemaleReceiveGiftDataStat(userId);
            });
            doProcess("记录用户双方首次交互建立时间", () -> {
                chatConsumeService.saveFemaleFfActMsgTime(userId, friendId);
            });
            /*doProcess("送礼活动处理", () -> {
                chatConsumeService.saveSendEcuGiftActivityDeal(friendId);
            });*/
            /*doProcess("心意礼物统计", () -> {
                chatConsumeService.saveMindGiftStat();
            });*/
            return;
        }
        if (BusiCodeDefine.NetCallHangUp == busiCode || BusiCodeDefine.NetCallDisconnect == busiCode) {
            Long totalBeans = GlobalUtils.extLong(BusinessDataKey.TotalBeans);
            doProcess("处理音视频通话主播分成和亲密度", () -> {
                if (totalBeans == 0) {
                    return;
                }
                chatConsumeService.saveNetCallInvitePresentAndIntimate();
            });
            doProcess("同步用户通话空闲状态到ES", () -> {
                chatConsumeService.syncUserCallFreeStatusToEs();
            });
            doProcess("音视频通话结束，将女用户从正在通话队列中移除", () -> {
                chatConsumeService.deleteCallingQueueForFemale();
            });
            doProcess("音视频通话结束，女用户指标数据统计", () -> {
                chatConsumeService.saveFemaleCallInfo();
            });
            doProcess("视频通话结束，女用户通话时长任务处理", () -> {
                chatConsumeService.saveFemaleVideoTaskProcess();
            });
            doProcess("视频通话结束，男用户拨打任务处理", () -> {
                chatConsumeService.saveMaleVideoTaskProcess();
            });
            doProcess("视频通话结束，视频速配用户记录", () -> {
                chatConsumeService.saveVideoMatchDealForAccept();
            });
            doProcess("视频通话结束，记录最近社交交互时间", () -> {
                chatConsumeService.saveSocialLastlyInteractiveTime();
            });
            doProcess("视频通话结束，记录消息状态为已回复", () -> {
                chatConsumeService.saveRecordMsgReplyStatus();
            });
            doProcess("记录用户双方首次交互建立时间", () -> {
                chatConsumeService.saveFccTimeForCall();
            });
            return;
        }
        // 用户充值业务
        if (BusiCodeDefine.UpdateRechargeResult == busiCode) {
            doProcess("音视频充值通话余额提醒", () -> {
                Long targetUserId = GlobalUtils.extLong(BusinessDataKey.TargetUserId);
                netCallService.saveProcessNetCall(targetUserId);
            });
            doProcess("用户充值任务", () -> {
                chatConsumeService.saveRechargeTaskProcess();
            });
            doProcess("主播转化率处理", () -> {
                chatConsumeService.saveDealAnchorTransformRate();
            });
            return;
        }
        // 通话信息业务
        if (BusiCodeDefine.NetCallInfo == busiCode) {
            doProcess("女用户指标数据统计", () -> {
                chatConsumeService.saveFemaleCallInfoForInfo();
            });
            return;
        }
        // 取消通话业务
        if (BusiCodeDefine.NetCallCancel == busiCode) {
            doProcess("被叫用户错过通话发送系统消息提醒", () -> {
                chatConsumeService.saveSendDayMissCallMsg();
            });
            doProcess("女用户漏接视频通话过多，关闭视频接听功能", () -> {
                chatConsumeService.saveAutoOffVideoAnswerForCall(UserBusiKeyMark.FemaleMissCallTimes);
            });
            return;
        }
        // 送聊天礼物达到金额限制
        if (BusiCodeDefine.SendChatGiftArriveLimit == busiCode){
            doProcess("赠送私聊礼物达到金额限制", () -> {
                chatConsumeService.saveSendChatGiftArriveLimit(userId, friendId);
            });
            return;
        }
        // 拒接通话业务
        if (BusiCodeDefine.NetCallRefuse == busiCode) {
            /*doProcess("女用户指标数据统计", () -> {
                chatConsumeService.saveFemaleCallInfoForRefuse();
            });*/
            /*doProcess("女用户拒接视频通话过多，关闭视频接听功能", () -> {
                chatConsumeService.saveAutoOffVideoAnswerForCall(UserBusiKeyMark.FemaleRefuseCallTimes);
            });*/
            return;
        }
        // 接受通话业务处理
        if (BusiCodeDefine.NetCallAccept == busiCode) {
            /*doProcess("被叫用户接受通话业务处理", () -> {
                chatConsumeService.saveAcceptCallProcess();
            });*/
            /*doProcess("对《视频匹配》《视频邀请》通话做流量记录", () -> {
                chatConsumeService.saveFlowRecordForCallAccept();
            });*/
            doProcess("同步用户通话忙碌状态到ES", () -> {
                chatConsumeService.syncUserCallBusyStatusToEs();
            });
            doProcess("女用户通话中保存队列", () -> {
                chatConsumeService.saveCallingQueueForFemale();
            });
            doProcess("女用户指标数据统计", () -> {
                chatConsumeService.saveFemaleCallInfoForAccept();
            });
            /*doProcess("男用户首次使用视频免费券通话处理", () -> {
                chatConsumeService.saveMaleFirstUseVideoTicketDeal();
            });*/
            /*doProcess("男用户未首充拨打视频额外提醒", () -> {
                chatConsumeService.maleNoFirstRechargeVideoTips();
            });*/
            return;
        }
        // 通话评价业务处理
        if (busiCode == BusiCodeDefine.NetCallEvaluate) {
            /*doProcess("男用户首次通话评价【好评】女用户获得额外奖励", () -> {
                chatConsumeService.saveCallEvaluateVideoTicketExtAward();
            });*/
            return;
        }
        // 视频通话人脸统计处理
        if (busiCode == BusiCodeDefine.VideoCallFaceCount) {
            /*doProcess("女用户视频通话不露脸，关闭视频接听功能", () -> {
                chatConsumeService.saveAutoOffVideoAnswerForCall(UserBusiKeyMark.FemaleNoFaceCallTimes);
            });*/
            return;
        }
        if(BusiCodeDefine.FemaleChatUp == busiCode){
            doProcess("女用户搭讪-每天首次交互逻辑处理", () -> {
                chatConsumeService.saveUserFirstMsgDayDeal(userSex, userId, friendId, FemaleMsg.ActCu);
            });
            return;
        }
    }
}
