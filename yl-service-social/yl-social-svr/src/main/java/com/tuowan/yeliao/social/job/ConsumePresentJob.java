package com.tuowan.yeliao.social.job;

import com.easyooo.framework.common.util.ListUtils;
import com.tuowan.yeliao.commons.context.proxy.ProxyExecutors;
import com.tuowan.yeliao.commons.core.enums.busicode.BackCodeDefine;
import com.tuowan.yeliao.commons.data.entity.user.UConsumePresent;
import com.tuowan.yeliao.commons.job.DefaultJob;
import com.tuowan.yeliao.social.constant.JobKeyDefine;
import com.tuowan.yeliao.social.service.ConsumePresentService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 消费提成相关任务
 *
 * <AUTHOR>
 * @date 2022/4/19 13:47
 */
@Component
public class ConsumePresentJob extends DefaultJob {

    @Autowired
    private ConsumePresentService consumePresentService;

    /**
     * 消费提成处理任务
     * <p>
     * Cron: 0/3 * * * * ?
     *
     * @param param
     * @return
     */
    @XxlJob(JobKeyDefine.ConsumePresentHandler)
    public ReturnT<String> consumePresentHandler(String param) {
        List<UConsumePresent> values = consumePresentService.getWaitPresentList(getJobShardIndex(), getJobShardTotal());
        if (ListUtils.isEmpty(values)) {
            return ReturnT.SUCCESS;
        }
        long startTime = System.currentTimeMillis();
        for (UConsumePresent present : values) {
            try {
                ProxyExecutors.doProxy(BackCodeDefine.ConsumePresent, (context) -> {
                    consumePresentService.saveProcessPresent(present);
                    return null;
                });
            } catch (Exception e) {
                LOG.error("处理消费提成失败，原因：", e);
            }
        }
        long execTime = System.currentTimeMillis() - startTime;
        LOG.debug("处理消费提成完成，总计: {}条，耗时：{}ms", values.size(), execTime);
        return ReturnT.SUCCESS;
    }
}
