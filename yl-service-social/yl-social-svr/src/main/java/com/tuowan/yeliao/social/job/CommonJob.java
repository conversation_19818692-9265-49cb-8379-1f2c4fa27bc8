package com.tuowan.yeliao.social.job;

import com.easyooo.framework.common.util.JsonUtils;
import com.easyooo.framework.common.util.ListUtils;
import com.easyooo.framework.common.util.StringUtils;
import com.tuowan.yeliao.commons.context.proxy.ProxyExecutors;
import com.tuowan.yeliao.commons.core.enums.busicode.BackCodeDefine;
import com.tuowan.yeliao.commons.data.entity.config.TFemaleLevelNum;
import com.tuowan.yeliao.commons.data.entity.user.UChatMaster;
import com.tuowan.yeliao.commons.job.DefaultJob;
import com.tuowan.yeliao.commons.job.annotation.JobPool;
import com.tuowan.yeliao.social.constant.JobKeyDefine;
import com.tuowan.yeliao.social.service.CommonJobService;
import com.tuowan.yeliao.social.service.VisitorService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import redis.clients.jedis.resps.Tuple;

import java.util.Date;
import java.util.List;

/**
 * 社交模块通用job
 *
 * <AUTHOR>
 * @date 2022/2/16 15:41
 */
@Component
@JobPool(minPoolSize = 3, maxPoolSize = 6)
public class CommonJob extends DefaultJob {
    private static final Logger LOG = LoggerFactory.getLogger(CommonJob.class);

    @Autowired
    private CommonJobService commonJobService;

    /**
     * 社交模块通用任务
     * 备注：每天凌晨执行一次
     */
    @XxlJob(JobKeyDefine.SocialCommon)
    public ReturnT<String> socialCommon(String param) {
        // 1、清除休息期限制缓存队列
        runInThread(() -> {
            commonJobService.clearFemaleRestQueue();
        });
        LOG.info("CommonJob-socialCommon-firing.............");
        return ReturnT.SUCCESS;
    }

    /**
     * 给近期活跃女用户发送临时通知任务
     */
    @Deprecated
    // @XxlJob(JobKeyDefine.SendShortNotice)
    public ReturnT<String> sendShortNotice(String param) {
        long startTime = System.currentTimeMillis();
        ProxyExecutors.doProxy(BackCodeDefine.SendShortNotice, context -> {
            for(int page = 0;;page++){
                List<UChatMaster> dataList = null; //commonJobService.queryMasterInfo(page * 500, 500);
                if(ListUtils.isEmpty(dataList)){
                    break;
                }
                for(UChatMaster item : dataList){
                    try {
                        commonJobService.sendVideoPriceNotice(item.getUserId());
                    }catch (Exception e){
                        LOG.error("CommonJob-sendShortNotice-error userId:{}; e:", item.getUserId(), e);
                    }
                }
            }
            return null;
        });
        long execTime = System.currentTimeMillis() - startTime;
        LOG.info("发送临时通知任务，耗时：{}ms", execTime);
        return ReturnT.SUCCESS;
    }

    /**
     * 给指定用户发送临时通知任务
     */
    @Deprecated
    // @XxlJob(JobKeyDefine.SendShortNoticePoint)
    public ReturnT<String> sendShortNoticePoint(String param) {
        long startTime = System.currentTimeMillis();
        String dataStr = XxlJobHelper.getJobParam();
        if(StringUtils.isEmpty(dataStr)){
            LOG.warn("CommonJob-sendShortNoticePoint-warn param is empty....");
            return ReturnT.SUCCESS;
        }
        ProxyExecutors.doProxy(BackCodeDefine.SendShortNotice, context -> {
            for(String item : StringUtils.split(dataStr, ",")){
                try {
                    commonJobService.sendShortNoticePoint(Long.valueOf(item));
                }catch (Exception e){
                    LOG.error("CommonJob-sendShortNoticePoint-error userId:{}; e:", item, e);
                }
            }
            return null;
        });
        long execTime = System.currentTimeMillis() - startTime;
        LOG.info("发送临时通知任务，耗时：{}ms", execTime);
        return ReturnT.SUCCESS;
    }

    /**
     * 红包退回任务
     * 备注：10秒执行一次
     */
    @XxlJob(JobKeyDefine.RedPacketReturn)
    public ReturnT<String> redPacketReturn(String param) {
        long startTime = System.currentTimeMillis();
        // 获取待退回数据
        List<Tuple> data = commonJobService.getRedPacketWaitReturnData();
        if(ListUtils.isEmpty(data)){
            return ReturnT.SUCCESS;
        }
        Date now = new Date(startTime);
        ProxyExecutors.doProxy(BackCodeDefine.RedPacketReturn, context -> {
            data.forEach(item -> {
                Long id = Long.valueOf(item.getElement());
                Date expireTime = new Date((long) item.getScore());
                try {
                    if(expireTime.after(now)){
                        // 还没有过期
                        return;
                    }
                    commonJobService.saveRedPacketReturn(id, expireTime);
                }catch (Exception e){
                    LOG.error("CommonJob-redPacketReturn-error id:{}; e:", id, e);
                }
            });
            return null;
        });
        long execTime = System.currentTimeMillis() - startTime;
        LOG.info("处理红包退回任务，耗时：{}ms", execTime);
        return ReturnT.SUCCESS;
    }


    /**
     * 聊天室幸运礼物消息发送任务
     * 备注：1秒执行一次
     */
    @XxlJob(JobKeyDefine.GroupChatLuckyGiftMsgSend)
    public ReturnT<String> groupChatLuckyGiftMsgSend(String param) {
        long startTime = System.currentTimeMillis();
        // 获取待发送数据
        List<String> data = commonJobService.getGroupChatLgWaitSendMsgData();
        if(ListUtils.isEmpty(data)){
            return ReturnT.SUCCESS;
        }
        ProxyExecutors.doProxy(BackCodeDefine.SendGroupLuckyGiftMsg, context -> {
            data.forEach(item -> {
                try {
                    commonJobService.dealGroupChatLgMsgSend(item);
                }catch (Exception e){
                    LOG.error("CommonJob-groupChatLuckyGiftMsgSend-error item:{}; e:", item, e);
                }
            });
            return null;
        });
        long execTime = System.currentTimeMillis() - startTime;
        LOG.info("聊天室幸运礼物消息发送任务，耗时：{}ms", execTime);
        return ReturnT.SUCCESS;
    }
}
