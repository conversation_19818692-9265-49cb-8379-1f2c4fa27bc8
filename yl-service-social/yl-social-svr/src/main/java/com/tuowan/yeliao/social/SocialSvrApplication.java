package com.tuowan.yeliao.social;

import com.tuowan.yeliao.commons.config.Application;
import com.tuowan.yeliao.commons.config.enums.AppType;
import com.tuowan.yeliao.commons.web.SvrApp;
import com.tuowan.yeliao.commons.web.session.RedisSessionManager;
import org.springframework.boot.SpringApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Import;

/**
 * 社交服务程序启动入口
 *
 * <AUTHOR>
 * @date 2022/4/12 10:01
 */
@SvrApp
@Application(AppType.SocialSvr)
@EnableFeignClients(basePackages = {
        "com.tuowan.yeliao.acct.api.remote",
        "com.tuowan.yeliao.log.api.remote",
        "com.tuowan.yeliao.user.api.remote",
})
@Import({RedisSessionManager.class})
public class SocialSvrApplication {

    public static void main(String[] args) {
        SpringApplication.run(SocialSvrApplication.class, args);
    }

}
