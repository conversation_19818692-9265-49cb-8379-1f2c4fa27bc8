package com.tuowan.yeliao.social.service;

import com.easyooo.framework.common.util.DateUtils;
import com.easyooo.framework.common.util.EnumUtils;
import com.easyooo.framework.common.util.MsgUtils;
import com.tuowan.yeliao.commons.context.GlobalUtils;
import com.tuowan.yeliao.commons.core.enums.business.BusinessDataKey;
import com.tuowan.yeliao.commons.data.entity.user.UUserBasic;
import com.tuowan.yeliao.commons.data.enums.social.NetCallType;
import com.tuowan.yeliao.commons.data.enums.user.SexType;
import com.tuowan.yeliao.commons.data.manager.user.UserInfoManager;
import com.tuowan.yeliao.commons.web.proxy.config.MQInvoke;
import com.tuowan.yeliao.social.comp.chatmaster.ChatMasterComponent;
import com.tuowan.yeliao.social.comp.friend.NetCallComponent;
import com.tuowan.yeliao.social.data.entity.FChatNetCall;
import com.tuowan.yeliao.social.data.enums.call.CallEvaluateType;
import com.tuowan.yeliao.social.data.enums.friend.NetCallEndType;
import com.tuowan.yeliao.social.data.enums.friend.NetCallFinishType;
import com.tuowan.yeliao.social.data.enums.user.ScoreChangeType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Objects;

/**
 * 女用户服务分
 *
 */
@Service
public class FemaleScoreService {

    @Autowired
    private UserInfoManager userInfoManager;
    @Autowired
    private NetCallComponent netCallComponent;
    @Autowired
    private ChatMasterComponent chatMasterComponent;

    /**
     * 送礼完成 服务分处理
     */
    public void saveSendGiftScore(Long userId, Long friendId){
        // 判断收礼方是否是女用户
        UUserBasic friendBasic = userInfoManager.getUserBasic(friendId);
        if(SexType.Female != friendBasic.getSex()){
            return;
        }
        // 判断礼物价值 （金币消耗必须大于等于1000）
        long totalBeans = GlobalUtils.extLong(BusinessDataKey.ConsumerTotalBeans, 0L);
        if(totalBeans < 1000){
            return;
        }
        UUserBasic userBasic = userInfoManager.getUserBasic(userId);
        chatMasterComponent.femaleScoreChange(new Date(), friendId, ScoreChangeType.ReceiveGift, 1L, MsgUtils.format("{}【{}】赠送价值{}金币的礼物", userBasic.getNickname(), userId, totalBeans));
    }

    /**
     * 通话完成 服务分处理
     */
    public void saveCallScore(Long callId){
        // 判断通话时长是否是大于1分钟
        Integer duration = GlobalUtils.extInt(BusinessDataKey.Duration, 0);
        if(duration < 60){
            return;
        }
        FChatNetCall call = netCallComponent.getChatNetCall(callId);
        Long femaleUserId = call.getUserId(); // 拨打方
        Long maleUserId = call.getFriendId();
        UUserBasic userBasic = userInfoManager.getUserBasic(call.getUserId());
        if(SexType.Male == userBasic.getSex()){
            femaleUserId = call.getFriendId(); // 接收方
            maleUserId = call.getUserId();
        }
        UUserBasic maleBasic = userInfoManager.getUserBasic(maleUserId);
        chatMasterComponent.femaleScoreChange(new Date(), femaleUserId, ScoreChangeType.NetCallDuration, Math.min(30, duration / 60), MsgUtils.format("与{}【{}】通话{}秒", maleBasic.getNickname(), maleUserId, duration));
    }


    /**
     * 通话评价 服务分处理
     */
    @MQInvoke
    public void saveCallEvaluateScore(){
        // 判断通话时长是否是大于1分钟
        Integer duration = GlobalUtils.extInt(BusinessDataKey.Duration, 0);
        if(duration < 60){
            return;
        }
        // 判断评价 如果是一般则不做处理
        CallEvaluateType type = CallEvaluateType.valueOf(GlobalUtils.formValue("type"));
        if(CallEvaluateType.Common == type){
            return;
        }
        Long callId = GlobalUtils.formLong("callId");
        FChatNetCall call = netCallComponent.getChatNetCall(callId);
        Long femaleUserId = call.getUserId(); // 拨打方
        Long maleUserId = call.getFriendId();
        UUserBasic userBasic = userInfoManager.getUserBasic(call.getUserId());
        if(SexType.Male == userBasic.getSex()){
            femaleUserId = call.getFriendId(); // 接收方
            maleUserId = call.getUserId();
        }
        Date now = new Date();
        // 判断双方今日通话评价次数
        if(!netCallComponent.checkCallEvaluateAboutScore(now, maleUserId, femaleUserId)){
            return;
        }
        if(CallEvaluateType.Good == type){ // 满意加分
            UUserBasic maleBasic = userInfoManager.getUserBasic(maleUserId);
            chatMasterComponent.femaleScoreChange(now, femaleUserId, ScoreChangeType.CallSatisfied, 2, MsgUtils.format("{}【{}】通话评价满意", maleBasic.getNickname(), maleUserId));
            netCallComponent.recordCallEvaluateAboutScore(now, maleUserId, femaleUserId);
        }
        if(CallEvaluateType.poor == type){ // 不满意 扣分
            UUserBasic maleBasic = userInfoManager.getUserBasic(maleUserId);
            chatMasterComponent.femaleScoreChange(now, femaleUserId, ScoreChangeType.CallNotSatisfied, -2, MsgUtils.format("{}【{}】通话评价不满意", maleBasic.getNickname(), maleUserId));
            netCallComponent.recordCallEvaluateAboutScore(now, maleUserId, femaleUserId);
        }
    }

    /**
     * 拒绝通话 服务分处理
     */
    public void saveNetCallRefuseScore(){
        // 拒绝放不是女用户不做处理
        if(GlobalUtils.sexType() != SexType.Female){
            return;
        }
        Long callId = GlobalUtils.formLong("callId");
        FChatNetCall chatNetCall = netCallComponent.getChatNetCall(callId);
        if(Objects.isNull(chatNetCall) || Objects.isNull(chatNetCall.getRingTime())){
            return;
        }
        UUserBasic userBasic = userInfoManager.getUserBasic(chatNetCall.getUserId());
        chatMasterComponent.femaleScoreChange(new Date(), GlobalUtils.uid(), ScoreChangeType.RefuseCall, -2, MsgUtils.format("拒绝与{}【{}】的通话】", userBasic.getNickname(), userBasic.getUserId()));
    }

    /**
     * 挂断通话 服务分处理
     */
    public void saveNetCallHangUpScore(Long callId){
        // 挂断方不是女用户不做处理
        if(GlobalUtils.sexType() != SexType.Female){
            return;
        }
        // 如果不是手动挂断不做处理
        NetCallEndType endType = EnumUtils.byName(GlobalUtils.formValue("endType"), NetCallEndType.class);
        if(NetCallEndType.ManualHangUp != endType){
            return;
        }
        FChatNetCall chatNetCall = netCallComponent.getChatNetCall(callId);
        if(Objects.isNull(chatNetCall) || Objects.isNull(chatNetCall.getConnectTime())){
            return;
        }
        Long maleUserId = GlobalUtils.uid().equals(chatNetCall.getUserId()) ? chatNetCall.getFriendId() : chatNetCall.getUserId();
        UUserBasic maleBasic = userInfoManager.getUserBasic(maleUserId);
        chatMasterComponent.femaleScoreChange(new Date(), GlobalUtils.uid(), ScoreChangeType.ActHangUpCall, -2, MsgUtils.format("主动挂断与{}【{}】的通话", maleBasic.getNickname(), maleBasic.getUserId()));
    }

    /**
     * 《无人脸》挂断通话 服务分处理
     */
    public void saveNetCallHangUpForNoFaceScore(Long callId){
        // 挂断方不是男用户不做处理
        if(GlobalUtils.sexType() != SexType.Male){
            return;
        }
        // 不是因为无人脸不做处理
        NetCallFinishType finishType = GlobalUtils.extEnum(BusinessDataKey.FinishType, NetCallFinishType.class);
        if(NetCallFinishType.NoFace != finishType){
            return;
        }
        FChatNetCall call = netCallComponent.getChatNetCall(callId);
        if(Objects.isNull(call) || NetCallType.Video != call.getCallType() || Objects.isNull(call.getConnectTime())){
            return;
        }
        // 查询对方ID
        UUserBasic maleUserBasic = userInfoManager.getUserBasic(GlobalUtils.uid());
        Long otherUserId = GlobalUtils.uid().equals(call.getUserId()) ? call.getFriendId() : call.getUserId();
        chatMasterComponent.femaleScoreChange(new Date(), otherUserId, ScoreChangeType.VideoCallNoFace, -3, MsgUtils.format("与{}【{}】视频未识别到人脸", maleUserBasic.getNickname(), maleUserBasic.getUserId()));
    }

    /**
     * 取消通话 服务分处理
     */
    public void saveNetCallCancelScore(){
        // 如果是女用户取消的通话 说明是女用户自己主动拨打的
        if(GlobalUtils.sexType() == SexType.Female){
            return;
        }
        Long callId = GlobalUtils.formLong("callId");
        FChatNetCall chatNetCall = netCallComponent.getChatNetCall(callId);
        if(Objects.isNull(chatNetCall) || Objects.isNull(chatNetCall.getRingTime())){
            return;
        }
        // 如果当前时间跟拨打时间小于20秒 我们也不做处理
        if(DateUtils.getDiffSeconds(chatNetCall.getCallTime()) < 20){
            return;
        }
        // 如果女用户此时下线了 我们也不做处理
        if(!userInfoManager.isOnline(chatNetCall.getFriendId())){
            return;
        }
        UUserBasic maleBasic = userInfoManager.getUserBasic(GlobalUtils.uid());
        chatMasterComponent.femaleScoreChange(new Date(), chatNetCall.getFriendId(), ScoreChangeType.NoReceiveCall, -1, MsgUtils.format("漏接{}【{}】的通话", maleBasic.getNickname(), maleBasic.getUserId()));
    }

}
