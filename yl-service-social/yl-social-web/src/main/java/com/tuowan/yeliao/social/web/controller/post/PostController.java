package com.tuowan.yeliao.social.web.controller.post;

import com.tuowan.yeliao.commons.context.config.HeaderType;
import com.tuowan.yeliao.commons.context.config.Request;
import com.tuowan.yeliao.commons.context.config.SessionType;
import com.tuowan.yeliao.commons.core.enums.general.ClientType;
import com.tuowan.yeliao.commons.core.http.ReturnUtils;
import com.tuowan.yeliao.commons.core.http.Root;
import com.tuowan.yeliao.commons.web.common.form.PageForm;
import com.tuowan.yeliao.commons.web.common.form.TotpCodeForm;
import com.tuowan.yeliao.commons.web.common.vo.PageVO;
import com.tuowan.yeliao.social.service.post.PostCommentService;
import com.tuowan.yeliao.social.service.post.PostService;
import com.tuowan.yeliao.social.web.form.post.*;
import com.tuowan.yeliao.social.web.vo.post.*;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @title 动态管理
 * @date 2020/11/19 16:11
 */
@RestController
@RequestMapping("/soc/post")
public class PostController {

    @Autowired
    private PostService postService;
    @Autowired
    private PostCommentService postCommentService;

    /**
     * @title 发布动态
     */
    @Request(containExternalInvoke = true)
    @RequestMapping("/pubPost")
    public Root<Void> pubPost(@RequestBody SavePostForm form) {
        postService.savePubPost(form);
        return ReturnUtils.empty();
    }

    /**
     * @title 查询动态列表
     */
    @Request
    @RequestMapping("/list")
    public Root<PageVO> list(@RequestBody PostListQueryForm form) {
        return ReturnUtils.root(postService.saveListPost(form));
    }

    /**
     * @title 查询话题动态列表
     */
    @Request
    @RequestMapping("/groupList")
    public Root<PageVO> groupList(@RequestBody GroupPostQueryForm form) {
        return ReturnUtils.root(postService.getGroupPostList(form));
    }

    /**
     * @title 用户动态
     */
    @Request
    @RequestMapping("/userList")
    public Root<PageVO> userList(@RequestBody UserPostForm form) {
        return ReturnUtils.root(postService.getUserPostList(form));
    }

    /**
     * @title 参与的动态
     */
    @Request
    @RequestMapping("/involvedList")
    public Root<PageVO> involvedList(@RequestBody PageForm form) {
        return ReturnUtils.root(postService.getInvolvedList(form));
    }

    /**
     * @title 收藏的动态
     */
    @Request
    @RequestMapping("/collectList")
    public Root<PageVO> collectList(@RequestBody PageForm form) {
        return ReturnUtils.root(postService.getCollectList(form));
    }

    /**
     * @title 动态详情
     */
    @Request
    @RequestMapping("/postDetail")
    public Root<PostDetailVO> postDetail(@RequestBody PostIdForm form) {
        return ReturnUtils.root(postService.savePostDetail(form));
    }

    /**
     * @title 删除动态
     */
    @Request
    @RequestMapping("/deletePost")
    public Root<Void> deletePost(@RequestBody PostIdForm form) {
        postService.deletePost(form);
        return ReturnUtils.empty();
    }

    /**
     * @title 动态点赞
     */
    @Request
    @RequestMapping("/postPraise")
    public Root<Void> postPraise(@RequestBody PostIdForm form) {
        postService.savePostPraise(form);
        return ReturnUtils.empty();
    }

    /**
     * @title 取消动态点赞
     */
    @Request
    @RequestMapping("/cancelPraisePost")
    public Root<Void> cancelPraisePost(@RequestBody PostIdForm form) {
        postService.saveCancelPostPraise(form);
        return ReturnUtils.empty();
    }

    /**
     * @title 点赞列表
     */
    @Deprecated
    @Request
    @RequestMapping("/praiseList")
    public Root<PraiseListVO> praiseList(@RequestBody PraiseListForm form) {
        return ReturnUtils.root(postService.saveListPraise(form));
    }

    /**
     * @title 打赏列表
     */
    @Request
    @RequestMapping("/rewardList")
    public Root<PageVO> rewardList(@RequestBody RewardListForm form) {
        return ReturnUtils.root(postService.listReward(form));
    }

    /**
     * @title 评论动态
     */
    @Request
    @RequestMapping("/comment")
    public Root<CommentVO> comment(@RequestBody CommentForm form) {
        return ReturnUtils.root(postCommentService.saveComment(form));
    }

    /**
     * 评论点赞
     */
    @Request
    @RequestMapping("/praiseComment")
    public Root<Void> praiseComment(@RequestBody CommentIdForm form) {
        postCommentService.savePraiseComment(form);
        return ReturnUtils.empty();
    }

    /**
     * 取消评论点赞
     */
    @Request
    @RequestMapping("/cancelPraiseComment")
    public Root<Void> cancelPraiseComment(@RequestBody CommentIdForm form) {
        postCommentService.saveCancelPraiseComment(form);
        return ReturnUtils.empty();
    }

    /**
     * @title 评论列表
     */
    @Request
    @RequestMapping("/commentList")
    public Root<PageVO> commentList(@RequestBody CommentQueryForm form) {
        return ReturnUtils.root(postCommentService.getCommentList(form));
    }

    /**
     * 动态置顶
     */
    @Request
    @RequestMapping("/topUp")
    public Root<Void> topUp(@RequestBody PostTopUpForm form) {
        postService.saveTopUp(form);
        return ReturnUtils.empty();
    }

    /**
     * 动态收藏
     */
    @Request
    @RequestMapping("/collect")
    public Root<Void> collect(@RequestBody PostCollectForm form) {
        postService.saveCollect(form);
        return ReturnUtils.empty();
    }

    /**
     * 动态投票
     */
    @Request
    @RequestMapping("/vote")
    public Root<Void> vote(@RequestBody PostVoteForm form) {
        postService.saveVote(form);
        return ReturnUtils.empty();
    }

    /**
     * 动态上热门
     */
    @Request
    @RequestMapping("/upHot")
    public Root<Void> upHot(@RequestBody PostIdForm form) {
        postService.saveUpHot(form);
        return ReturnUtils.empty();
    }

    /**
     * 动态分享
     */
    @Request
    @RequestMapping("/share")
    public Root<ShareVO> share(@RequestBody PostShareForm form) {
        return ReturnUtils.root(postService.saveShare(form));
    }

    /**
     * 创建动态ES索引
     */
    @Request(session = SessionType.None, header = HeaderType.None)
    @RequestMapping("/refreshList")
    public Root<Void> refreshList(@Param("type") String type, @Param("code") String code) {
        postService.saveRefreshList(TotpCodeForm.build(type, code));
        return ReturnUtils.empty();
    }


    /******************** CMS操作 ********************/

    /**
     * 审核动态（CMS）
     */
    @Request(clients = ClientType.Cms)
    @RequestMapping("/audit")
    public Root<Void> audit(@RequestBody PostAuditForm form) {
        form.setAuditSource(1);
        postService.saveAuditPost(form);
        return ReturnUtils.empty();
    }

    /**
     * 手动调整动态热度值（CMS）
     */
    @Request(clients = ClientType.Cms)
    @RequestMapping("/modifyBaseHeat")
    public Root<Void> modifyHeat(@RequestBody PostHeatEditForm form) {
        postService.updateBaseHeatValue(form);
        return ReturnUtils.empty();
    }

    /**
     * 动态CMS置顶
     */
    @Request(clients = ClientType.Cms)
    @RequestMapping("/modifyTopUp")
    public Root<Void> modifyTopUp(@RequestBody PostTopUpStatusForm form) {
        postService.updatePostTopUpStatus(form);
        return ReturnUtils.empty();
    }

    /**
     * 标记普通帖子（CMS）
     */
    @Request(clients = ClientType.Cms)
    @RequestMapping("/markNormal")
    public Root<Void> markNormal(@RequestBody MarkNormalForm form) {
        postService.saveMarkNormal(form);
        return ReturnUtils.empty();
    }

    /**
     * 调整动态话题话题（CMS）
     */
    @Request(clients = ClientType.Cms)
    @RequestMapping("/group")
    public Root<Void> group(@RequestBody PostGroupForm form) {
        postService.updatePostGroup(form);
        return ReturnUtils.empty();
    }

    /**
     * 删除动态（CMS）
     */
    @Request(clients = ClientType.Cms)
    @RequestMapping("/cmsDeletePost")
    public Root<Void> cmsDelete(@RequestBody PostIdForm form) {
        postService.deletePost(form);
        return ReturnUtils.empty();
    }

    /**
     * 为动态指定推广广告
     */
    @Request(clients = ClientType.Cms)
    @RequestMapping("/pointAd")
    public Root<Void> pointAd(@RequestBody PointAdForm form) {
        postService.savePointAd(form);
        return ReturnUtils.empty();
    }

}
