package com.tuowan.yeliao.social.web.form.friend.redpt;

import com.easyooo.framework.validate.config.LMLength;
import com.easyooo.framework.validate.config.LMNotEmpty;
import com.tuowan.yeliao.commons.web.common.form.Form;

public class ReceiveForm implements Form {
    @LMNotEmpty
    @LMLength(min = 32, max = 32)
    private String sign;

    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }
}
