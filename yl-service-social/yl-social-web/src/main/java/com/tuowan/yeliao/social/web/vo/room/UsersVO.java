package com.tuowan.yeliao.social.web.vo.room;

import com.tuowan.yeliao.commons.data.dto.user.OnlineDTO;
import com.tuowan.yeliao.commons.data.enums.general.AuditStatus;
import com.tuowan.yeliao.commons.data.enums.user.SexType;
import com.tuowan.yeliao.commons.data.enums.user.VipType;
import com.tuowan.yeliao.social.data.enums.room.RoomUserType;

public class UsersVO {
    // 成员 userId
    private Long userId;
    // 成员类型
    private RoomUserType type;
    // 成员昵称
    private String nickname;
    // 成员头像
    private String headPic;
    // 成员性别
    private SexType sex;
    // 成员年龄
    private Integer age;
    // 成员是否VIP
    private VipType vipType;
    // 成员在线信息
    private OnlineDTO onlineDTO;
    // 成员顶部icon
    private String topIcon;

    /////   聊天室申请用户列表使用    //////////
    // 申请时间描述
    private String applyTimeDesc;
    // 审核状态
    private AuditStatus status;

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public RoomUserType getType() {
        return type;
    }

    public void setType(RoomUserType type) {
        this.type = type;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getHeadPic() {
        return headPic;
    }

    public void setHeadPic(String headPic) {
        this.headPic = headPic;
    }

    public SexType getSex() {
        return sex;
    }

    public void setSex(SexType sex) {
        this.sex = sex;
    }

    public Integer getAge() {
        return age;
    }

    public void setAge(Integer age) {
        this.age = age;
    }

    public VipType getVipType() {
        return vipType;
    }

    public void setVipType(VipType vipType) {
        this.vipType = vipType;
    }

    public OnlineDTO getOnlineDTO() {
        return onlineDTO;
    }

    public void setOnlineDTO(OnlineDTO onlineDTO) {
        this.onlineDTO = onlineDTO;
    }

    public String getTopIcon() {
        return topIcon;
    }

    public void setTopIcon(String topIcon) {
        this.topIcon = topIcon;
    }

    public String getApplyTimeDesc() {
        return applyTimeDesc;
    }

    public void setApplyTimeDesc(String applyTimeDesc) {
        this.applyTimeDesc = applyTimeDesc;
    }

    public AuditStatus getStatus() {
        return status;
    }

    public void setStatus(AuditStatus status) {
        this.status = status;
    }
}
