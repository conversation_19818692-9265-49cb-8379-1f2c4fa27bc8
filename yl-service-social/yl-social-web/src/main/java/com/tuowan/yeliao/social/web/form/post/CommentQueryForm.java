package com.tuowan.yeliao.social.web.form.post;

import com.easyooo.framework.validate.config.LMNotNull;
import com.tuowan.yeliao.commons.core.constant.GlobalConstant;
import com.tuowan.yeliao.commons.web.common.form.PageForm;

/**
 * 评论查询表单
 *
 * <AUTHOR>
 * @date 2022/7/11 19:39
 */
public class CommentQueryForm extends PageForm {
    /**
     * 动态ID
     */
    @LMNotNull
    private Long postId;
    /**
     * 父级评论Id
     */
    private Long parentCommentId = GlobalConstant.ROOT_ID;

    public Long getPostId() {
        return postId;
    }

    public void setPostId(Long postId) {
        this.postId = postId;
    }

    public Long getParentCommentId() {
        return parentCommentId;
    }

    public void setParentCommentId(Long parentCommentId) {
        this.parentCommentId = parentCommentId;
    }
}
