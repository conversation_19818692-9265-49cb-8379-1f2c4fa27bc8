package com.tuowan.yeliao.social.web.vo.post;

import com.tuowan.yeliao.commons.core.enums.general.BoolType;
import com.tuowan.yeliao.social.data.dto.post.UserPraiseDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/10/20 21:02
 */
public class PraiseListVO {

    /**
     * 点赞列表
     */
    private List<UserPraiseDTO> list;
    /**
     * 点赞列表是否有更多
     */
    private BoolType hasMore;
    /**
     * 是否已点赞
     */
    private BoolType hasPraised;

    /** 点赞数量 */
    private Integer praiseNum;

    /**
     * 首赞用户信息
     */
    private UserPraiseDTO firstPraiseUser;

    public List<UserPraiseDTO> getList() {
        return list;
    }

    public void setList(List<UserPraiseDTO> list) {
        this.list = list;
    }

    public BoolType getHasPraised() {
        return hasPraised;
    }

    public void setHasPraised(BoolType hasPraised) {
        this.hasPraised = hasPraised;
    }

    public UserPraiseDTO getFirstPraiseUser() {
        return firstPraiseUser;
    }

    public void setFirstPraiseUser(UserPraiseDTO firstPraiseUser) {
        this.firstPraiseUser = firstPraiseUser;
    }

    public BoolType getHasMore() {
        return hasMore;
    }

    public void setHasMore(BoolType hasMore) {
        this.hasMore = hasMore;
    }

    public Integer getPraiseNum() {
        return praiseNum;
    }

    public void setPraiseNum(Integer praiseNum) {
        this.praiseNum = praiseNum;
    }
}
