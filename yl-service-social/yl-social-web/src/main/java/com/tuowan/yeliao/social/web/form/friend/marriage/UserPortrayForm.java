package com.tuowan.yeliao.social.web.form.friend.marriage;

import com.easyooo.framework.validate.config.LMNotNull;
import com.tuowan.yeliao.commons.data.enums.user.*;
import com.tuowan.yeliao.commons.web.common.form.Form;
import com.tuowan.yeliao.social.data.enums.friend.MarriageChooseType;

/**
 * 用户画像表单
 *
 * <AUTHOR>
 * @date 2021/12/29 13:49
 */
public class UserPortrayForm implements Form {

    /** 你的年龄 */
    private PortrayAgeType age;
    /** 你的月工资 */
    private PortraySalaryType salary;
    /** 你的感情状况 */
    private PortrayEmotionType emotion;
    /** 你的交友目的 */
    private PortrayGoalType goal;
    /** 你的择偶意向 */
    private PortrayIntentionType intention;
    /** 择偶对象年龄要求 */
    private PortrayTargetAgeType targetAge;
    /** 择偶对象地域要求 */
    private PortrayAreaType area;

    /** 更新类型 */
    @LMNotNull
    private MarriageChooseType chooseType;

    public PortrayAgeType getAge() {
        return age;
    }

    public void setAge(PortrayAgeType age) {
        this.age = age;
    }

    public PortraySalaryType getSalary() {
        return salary;
    }

    public void setSalary(PortraySalaryType salary) {
        this.salary = salary;
    }

    public PortrayEmotionType getEmotion() {
        return emotion;
    }

    public void setEmotion(PortrayEmotionType emotion) {
        this.emotion = emotion;
    }

    public PortrayGoalType getGoal() {
        return goal;
    }

    public void setGoal(PortrayGoalType goal) {
        this.goal = goal;
    }

    public PortrayIntentionType getIntention() {
        return intention;
    }

    public void setIntention(PortrayIntentionType intention) {
        this.intention = intention;
    }

    public PortrayTargetAgeType getTargetAge() {
        return targetAge;
    }

    public void setTargetAge(PortrayTargetAgeType targetAge) {
        this.targetAge = targetAge;
    }

    public PortrayAreaType getArea() {
        return area;
    }

    public void setArea(PortrayAreaType area) {
        this.area = area;
    }

    public MarriageChooseType getChooseType() {
        return chooseType;
    }

    public void setChooseType(MarriageChooseType chooseType) {
        this.chooseType = chooseType;
    }
}
