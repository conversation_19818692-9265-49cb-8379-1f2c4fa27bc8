package com.tuowan.yeliao.social.web.vo.activity;

import com.tuowan.yeliao.commons.core.enums.general.BoolType;

import java.util.List;

public class LevelExcHomeVO {
    // 匹配状态
    private BoolType matchStatus;
    // 用户头像
    private String headPic;

    // 已达挑战等级
    private Integer level;
    // 今天系统匹配已达多少次
    private Integer todayMatchTimes;
    // 预计获得社交积分
    private String expectedCash;

    // 系统搭讪缘分匹配
    private Integer cuMatchTimes;
    private Integer totalCuMatchTimes;

    // 主动搭讪缘分匹配
    private Integer acuMatchTimes;
    private Integer totalAcuMatchTimes;

    // 主动电话匹配
    private Integer callMatchTimes;
    private Integer totalCallMatchTimes;

    // 今日社交积分（备注：该字段现在用来表示 今日获得总积分）
    private String todaySocialCash;
    // 今日总积分（备注：该字段现在用来表示 下一等级还缺多少积分）
    private String todayTotalCash;

    // 提示
    private String tips;

    // 展示的新人任务
    private List<LevelExcTaskItemVO> taskList;

    public BoolType getMatchStatus() {
        return matchStatus;
    }

    public void setMatchStatus(BoolType matchStatus) {
        this.matchStatus = matchStatus;
    }

    public String getHeadPic() {
        return headPic;
    }

    public void setHeadPic(String headPic) {
        this.headPic = headPic;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public Integer getTodayMatchTimes() {
        return todayMatchTimes;
    }

    public void setTodayMatchTimes(Integer todayMatchTimes) {
        this.todayMatchTimes = todayMatchTimes;
    }

    public String getExpectedCash() {
        return expectedCash;
    }

    public void setExpectedCash(String expectedCash) {
        this.expectedCash = expectedCash;
    }

    public Integer getCuMatchTimes() {
        return cuMatchTimes;
    }

    public void setCuMatchTimes(Integer cuMatchTimes) {
        this.cuMatchTimes = cuMatchTimes;
    }

    public Integer getTotalCuMatchTimes() {
        return totalCuMatchTimes;
    }

    public void setTotalCuMatchTimes(Integer totalCuMatchTimes) {
        this.totalCuMatchTimes = totalCuMatchTimes;
    }

    public Integer getAcuMatchTimes() {
        return acuMatchTimes;
    }

    public void setAcuMatchTimes(Integer acuMatchTimes) {
        this.acuMatchTimes = acuMatchTimes;
    }

    public Integer getTotalAcuMatchTimes() {
        return totalAcuMatchTimes;
    }

    public void setTotalAcuMatchTimes(Integer totalAcuMatchTimes) {
        this.totalAcuMatchTimes = totalAcuMatchTimes;
    }

    public Integer getCallMatchTimes() {
        return callMatchTimes;
    }

    public void setCallMatchTimes(Integer callMatchTimes) {
        this.callMatchTimes = callMatchTimes;
    }

    public Integer getTotalCallMatchTimes() {
        return totalCallMatchTimes;
    }

    public void setTotalCallMatchTimes(Integer totalCallMatchTimes) {
        this.totalCallMatchTimes = totalCallMatchTimes;
    }

    public String getTodaySocialCash() {
        return todaySocialCash;
    }

    public void setTodaySocialCash(String todaySocialCash) {
        this.todaySocialCash = todaySocialCash;
    }

    public String getTodayTotalCash() {
        return todayTotalCash;
    }

    public void setTodayTotalCash(String todayTotalCash) {
        this.todayTotalCash = todayTotalCash;
    }

    public String getTips() {
        return tips;
    }

    public void setTips(String tips) {
        this.tips = tips;
    }

    public List<LevelExcTaskItemVO> getTaskList() {
        return taskList;
    }

    public void setTaskList(List<LevelExcTaskItemVO> taskList) {
        this.taskList = taskList;
    }
}
