package com.tuowan.yeliao.social.web.controller.user;

import com.tuowan.yeliao.commons.context.config.Request;
import com.tuowan.yeliao.commons.context.config.SessionType;
import com.tuowan.yeliao.social.data.entity.User;
import com.tuowan.yeliao.social.service.user.UserService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-07-04
 */
@RestController
@RequestMapping("/soc/user/v2")
public class UserController {

    @Resource
    private UserService userService;

    @Request(SessionType.None)
    @GetMapping("/list")
    public List<User> listUser() {
        return userService.listUsers();
    }
}
