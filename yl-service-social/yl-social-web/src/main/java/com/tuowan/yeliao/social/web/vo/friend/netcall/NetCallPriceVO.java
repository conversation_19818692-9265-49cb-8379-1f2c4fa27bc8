package com.tuowan.yeliao.social.web.vo.friend.netcall;


import com.tuowan.yeliao.commons.data.enums.social.NetCallType;
import com.tuowan.yeliao.social.data.dto.friend.AudioPriceDTO;

import java.util.LinkedList;

/**
 * 通话单价VO
 *
 * <AUTHOR>
 * @date 2021/4/29 09:24
 */
public class NetCallPriceVO {

    /** 当前用户金币数量 */
    private Long beans;
    /** 音频类型 */
    private NetCallType callType;
    /**
     * 价格列表
     */
    private LinkedList<AudioPriceDTO> priceList;
    /**
     * 平台规范地址
     */
    private String platformRuleUrl;
    /**
     * 防诈骗指南地址
     */
    private String preventFraudUrl;

    public Long getBeans() {
        return beans;
    }

    public void setBeans(Long beans) {
        this.beans = beans;
    }

    public NetCallType getCallType() {
        return callType;
    }

    public void setCallType(NetCallType callType) {
        this.callType = callType;
    }

    public LinkedList<AudioPriceDTO> getPriceList() {
        return priceList;
    }

    public void setPriceList(LinkedList<AudioPriceDTO> priceList) {
        this.priceList = priceList;
    }

    public String getPlatformRuleUrl() {
        return platformRuleUrl;
    }

    public void setPlatformRuleUrl(String platformRuleUrl) {
        this.platformRuleUrl = platformRuleUrl;
    }

    public String getPreventFraudUrl() {
        return preventFraudUrl;
    }

    public void setPreventFraudUrl(String preventFraudUrl) {
        this.preventFraudUrl = preventFraudUrl;
    }
}
