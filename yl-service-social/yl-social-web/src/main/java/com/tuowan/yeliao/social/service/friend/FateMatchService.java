package com.tuowan.yeliao.social.service.friend;

import com.tuowan.yeliao.commons.config.UnifiedConfig;
import com.tuowan.yeliao.commons.config.lock.UserLockTemplate;
import com.tuowan.yeliao.commons.context.GlobalUtils;
import com.tuowan.yeliao.commons.core.exception.BusiException;
import com.tuowan.yeliao.commons.data.entity.user.UUserBasic;
import com.tuowan.yeliao.commons.data.enums.user.SexType;
import com.tuowan.yeliao.commons.data.manager.user.UserInfoManager;
import com.tuowan.yeliao.commons.data.utils.BusiUtils;
import com.tuowan.yeliao.commons.web.proxy.config.BusiCode;
import com.tuowan.yeliao.social.comp.friend.FateMatchUserComponent;
import com.tuowan.yeliao.social.web.vo.friend.fate.FateMatchVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Objects;

/**
 * 缘分速配服务
 */
@Service
public class FateMatchService {
    /**
     * 用户每日最大匹配数量
     */
    private static final Integer FATE_MATCH_TIMES_LIMIT = 50;

    @Autowired
    private UserInfoManager userInfoManager;
    @Autowired
    private UserLockTemplate userLockTemplate;
    @Autowired
    private FateMatchUserComponent fateMatchUserComponent;

    /**
     * 匹配异性用户
     */
    @BusiCode
    public FateMatchVO match() {
        Long userId = GlobalUtils.uid();
        SexType sexType = GlobalUtils.sexType();
        userLockTemplate.acquireTransactionLock(userId);
        Date now = new Date();
        // 判断今日匹配次数是否达到限制
        Long fateMatchNum = fateMatchUserComponent.getFateMatchNum(userId, now);
        if (FATE_MATCH_TIMES_LIMIT <= fateMatchNum) {
            throw new BusiException("今日缘分匹配次数用完啦，明日再来吧~");
        }
        // 匹配用户
        Long friendId = fateMatchUserComponent.matchOppositeSexFateFriend(userId, sexType, now);
        if (Objects.isNull(friendId)) {
            throw new BusiException("匹配失败，请稍后重试！");
        }
        // 记录用户匹配数据
        if (!UnifiedConfig.isDevEnv()) {
            fateMatchUserComponent.recordFateMatchData(userId, friendId, now);
        }
        // 查询用户信息
        UUserBasic friendBasic = userInfoManager.getUserBasic(friendId);
        FateMatchVO resultVO = new FateMatchVO();
        resultVO.setUserId(friendBasic.getUserId());
        resultVO.setAge(BusiUtils.getAgeByDate(friendBasic.getBirthDate()) + "");
        resultVO.setSexType(friendBasic.getSex());
        resultVO.setNickname(friendBasic.getNickname());
        resultVO.setHeadPic(friendBasic.getHeadPic());
        resultVO.setRealPerson(friendBasic.getRealPerson());
        return resultVO;
    }

}
