package com.tuowan.yeliao.social.web.form.room;

import com.easyooo.framework.validate.config.LMLength;
import com.easyooo.framework.validate.config.LMNotEmpty;
import com.tuowan.yeliao.commons.core.enums.general.BoolType;
import com.tuowan.yeliao.commons.web.common.form.Form;

import java.util.List;

public class CreateForm implements Form {
    // 聊天室名称
    @LMNotEmpty
    @LMLength(max = 10)
    private String roomName;
    // 聊天室描述
    @LMNotEmpty
    @LMLength(max = 100)
    private String roomDesc;
    // 聊天室背景
    // private String roomBgUrl;
    // 聊天室公告
    @LMLength(max = 200)
    private String roomNotice;
    // 加入聊天室是否需要审核
    @LMNotEmpty
    private BoolType needAudit;
    // 聊天室标签合集
    private List<Integer> tags;


    public String getRoomName() {
        return roomName;
    }

    public void setRoomName(String roomName) {
        this.roomName = roomName;
    }

    public String getRoomDesc() {
        return roomDesc;
    }

    public void setRoomDesc(String roomDesc) {
        this.roomDesc = roomDesc;
    }

    /*public String getRoomBgUrl() {
        return roomBgUrl;
    }

    public void setRoomBgUrl(String roomBgUrl) {
        this.roomBgUrl = roomBgUrl;
    }*/

    public String getRoomNotice() {
        return roomNotice;
    }

    public void setRoomNotice(String roomNotice) {
        this.roomNotice = roomNotice;
    }

    public BoolType getNeedAudit() {
        return needAudit;
    }

    public void setNeedAudit(BoolType needAudit) {
        this.needAudit = needAudit;
    }

    public List<Integer> getTags() {
        return tags;
    }

    public void setTags(List<Integer> tags) {
        this.tags = tags;
    }
}
