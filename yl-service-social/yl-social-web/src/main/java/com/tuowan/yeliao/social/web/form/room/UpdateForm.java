package com.tuowan.yeliao.social.web.form.room;

import com.easyooo.framework.validate.config.LMLength;
import com.easyooo.framework.validate.config.LMNotNull;
import com.sun.org.apache.xpath.internal.operations.Bool;
import com.tuowan.yeliao.commons.core.enums.general.BoolType;
import com.tuowan.yeliao.commons.web.common.form.Form;

public class UpdateForm implements Form {
    // 聊天室标识ID
    @LMNotNull
    private Long roomId;
    // 修改聊天室名称 传该字段
    @LMLength(max = 10)
    private String roomName;
    // 修改聊天室描述 传该字段
    @LMLength(max = 100)
    private String roomDesc;
    // 修改聊天室背景 传该字段
    // private String roomBgUrl;
    // 修改聊天室是否需要审核加入 传该字段
    private BoolType needAudit;
    // 修改聊天室消息免打扰 传该字段
    private BoolType notDisturb;
    // 聊天室公告
    @LMLength(max = 200)
    private String roomNotice;

    public Long getRoomId() {
        return roomId;
    }

    public void setRoomId(Long roomId) {
        this.roomId = roomId;
    }

    public String getRoomName() {
        return roomName;
    }

    public void setRoomName(String roomName) {
        this.roomName = roomName;
    }

    public String getRoomDesc() {
        return roomDesc;
    }

    public void setRoomDesc(String roomDesc) {
        this.roomDesc = roomDesc;
    }

    /*public String getRoomBgUrl() {
        return roomBgUrl;
    }

    public void setRoomBgUrl(String roomBgUrl) {
        this.roomBgUrl = roomBgUrl;
    }*/

    public BoolType getNeedAudit() {
        return needAudit;
    }

    public void setNeedAudit(BoolType needAudit) {
        this.needAudit = needAudit;
    }

    public BoolType getNotDisturb() {
        return notDisturb;
    }

    public void setNotDisturb(BoolType notDisturb) {
        this.notDisturb = notDisturb;
    }

    public String getRoomNotice() {
        return roomNotice;
    }

    public void setRoomNotice(String roomNotice) {
        this.roomNotice = roomNotice;
    }
}
