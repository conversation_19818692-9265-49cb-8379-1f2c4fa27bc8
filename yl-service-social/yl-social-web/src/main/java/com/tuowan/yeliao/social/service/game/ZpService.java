package com.tuowan.yeliao.social.service.game;

import com.easyooo.framework.common.util.MsgUtils;
import com.easyooo.framework.common.util.StringUtils;
import com.tuowan.yeliao.commons.comp.grant.GrantComponent;
import com.tuowan.yeliao.commons.comp.grant.dto.AwardDTO;
import com.tuowan.yeliao.commons.comp.user.UserBusiComponent;
import com.tuowan.yeliao.commons.config.lock.UserLockTemplate;
import com.tuowan.yeliao.commons.context.GlobalUtils;
import com.tuowan.yeliao.commons.core.additions.UrlParamsMap;
import com.tuowan.yeliao.commons.core.constant.GlobalConstant;
import com.tuowan.yeliao.commons.core.enums.busicode.BusiCodeDefine;
import com.tuowan.yeliao.commons.core.enums.errcode.ErrCodeType;
import com.tuowan.yeliao.commons.core.exception.BusiException;
import com.tuowan.yeliao.commons.data.entity.user.UUserBasic;
import com.tuowan.yeliao.commons.data.manager.user.UserInfoManager;
import com.tuowan.yeliao.commons.web.common.form.PageForm;
import com.tuowan.yeliao.commons.web.common.vo.PageVO;
import com.tuowan.yeliao.commons.web.proxy.config.BusiCode;
import com.tuowan.yeliao.social.comp.activity.LuckyWheelComponent;
import com.tuowan.yeliao.social.data.dto.activity.LwaAwardDTO;
import com.tuowan.yeliao.social.web.vo.game.dzp.DrawVO;
import com.tuowan.yeliao.social.web.vo.game.dzp.DzpAwardVO;
import com.tuowan.yeliao.social.web.vo.game.dzp.HomeVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

@Service
public class ZpService {

    @Autowired
    private LuckyWheelComponent luckyWheelComponent;
    @Autowired
    private UserBusiComponent userBusiComponent;
    @Autowired
    private UserLockTemplate userLockTemplate;
    @Autowired
    private GrantComponent grantComponent;
    @Autowired
    private UserInfoManager userInfoManager;

    /**
     * 首页
     */
    @BusiCode
    public HomeVO queryHome(){
        // 获取奖励列表
        List<DzpAwardVO> awardVOList = new ArrayList<>();
        List<LwaAwardDTO> lwaAwardDTOS = luckyWheelComponent.queryDisplayAwardCfgs();
        lwaAwardDTOS.forEach(item -> {
            UrlParamsMap map = UrlParamsMap.build(item.getAwardDetail().getExtJsonCfg());
            awardVOList.add(DzpAwardVO.build(item.getAward().getCode(), map.getString("pic"), item.getAward().getName(), map.getString("icon")));
        });
        // 返回值构建
        HomeVO vo = new HomeVO();
        vo.setPrice(GlobalConstant.GAME_DZP_PRICE);
        vo.setTotalSilver(GlobalUtils.silver());
        vo.setAwardVOList(awardVOList);
        vo.setBanner(luckyWheelComponent.queryDzpAwardBanner());
        vo.setRule( "【玩法规则】\n" +
                    "1、淘宝大作战为趣味性小游戏，所有的奖励都是不可兑现、出售、交易的，一旦发现有不法用户利用本游戏非法牟利将会在收集信息后进行报警。\n" +
                    "2、淘宝大作战有单抽和五连抽两种玩法，单抽一次消耗100银币，五连抽消耗500银币，如果中奖的为银币礼物或亲密度卡的话只能给异性赠与，如果中奖的为特权类【头像框、座驾等】只能自己使用。\n" +
                    "3、银币不足时可以消耗金币兑换，兑换比例为1金币兑换1银币，也可以通过充值购买银币。\n" +
                    "友情提示：请理性消费，切勿失去理智。");
        return vo;
    }

    /**
     * 抽奖
     */
    @BusiCode(BusiCodeDefine.GameDzpDraw)
    public DrawVO saveDraw(){
        Long userId = GlobalUtils.uid();
        userLockTemplate.acquireTransactionLock(userId);
        UUserBasic userBasic = userInfoManager.getUserBasic(userId);
        if(GlobalUtils.silver() < GlobalConstant.GAME_DZP_PRICE){
            throw new BusiException(ErrCodeType.SilverNotEnough);
        }
        // 扣除银币
        Long surplusSilver = userBusiComponent.addSilver(userId, -GlobalConstant.GAME_DZP_PRICE);
        // 抽奖
        LwaAwardDTO awardDTO = luckyWheelComponent.luckyWheelDraw(userId);
        UrlParamsMap map = UrlParamsMap.build(awardDTO.getAwardDetail().getExtJsonCfg());
        // 中奖banner数据保存
        luckyWheelComponent.recordDzpAwardBanner(MsgUtils.format("恭喜 {} 抽奖获得{}", userBasic.getNickname(), awardDTO.getAward().getName()));
        // 返回值构建
        DrawVO vo = new DrawVO();
        vo.setTotalSilver(surplusSilver);
        vo.setAwardCode(awardDTO.getAward().getCode());
        vo.setAwardDTOList(Collections.singletonList(AwardDTO.create1(map.getString("pic"), awardDTO.getAward().getName())));
        return vo;
    }

    /**
     * 中奖记录
     */
    @BusiCode
    public PageVO queryAwardLog(PageForm form){
        List<AwardDTO> awardDTOS = luckyWheelComponent.pageDzpAwardLog(GlobalUtils.uid(), form.getOffset(), form.getLimit());
        return new PageVO(awardDTOS, form.getOffset(), form.getLimit());
    }

}
