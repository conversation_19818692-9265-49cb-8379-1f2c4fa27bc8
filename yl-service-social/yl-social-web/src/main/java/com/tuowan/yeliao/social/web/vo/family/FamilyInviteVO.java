package com.tuowan.yeliao.social.web.vo.family;


import com.tuowan.yeliao.commons.data.enums.general.ClientTouchType;

/**
 * 家族邀请信息封装
 *
 * <AUTHOR>
 * @date 2021/7/27 16:32
 */
public class FamilyInviteVO {
    /**
     * 邀请消息标题
     */
    private String title;
    /**
     * 邀请消息内容
     */
    private String content;

    /**
     * 邀请图片
     */
    private String pic;
    /**
     * 消息点击事件类型
     */
    private ClientTouchType touchType;
    /**
     * 点击跳转值
     */
    private String touchValue;
    /**
     * 点击按钮文案
     */
    private String touchText;

    /**
     * 用户ID
     */
    private Long friendId;

    /**
     * 昵称
     */
    private String nickname;
    /**
     * 头像
     */
    private String headPic;

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getPic() {
        return pic;
    }

    public void setPic(String pic) {
        this.pic = pic;
    }

    public ClientTouchType getTouchType() {
        return touchType;
    }

    public void setTouchType(ClientTouchType touchType) {
        this.touchType = touchType;
    }

    public String getTouchValue() {
        return touchValue;
    }

    public void setTouchValue(String touchValue) {
        this.touchValue = touchValue;
    }

    public String getTouchText() {
        return touchText;
    }

    public void setTouchText(String touchText) {
        this.touchText = touchText;
    }

    public Long getFriendId() {
        return friendId;
    }

    public void setFriendId(Long friendId) {
        this.friendId = friendId;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getHeadPic() {
        return headPic;
    }

    public void setHeadPic(String headPic) {
        this.headPic = headPic;
    }
}
