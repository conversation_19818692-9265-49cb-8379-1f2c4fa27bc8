package com.tuowan.yeliao.social.web.vo.family;


import com.tuowan.yeliao.social.data.enums.family.RedPacketTimeConditionType;

/**
 * 红包创建返回值
 *
 * <AUTHOR>
 * @date 2021/7/23 14:16
 */
public class FamilyRedPacketCreateVO {

    /**
     * 红包id
     */
    private Integer redId;
    /**
     * 红包名称
     */
    private String title;
    /**
     * 红包领取时间类型
     */
    private RedPacketTimeConditionType timeCondition;
    /**
     * 开抢剩余时间 单位秒
     */
    private Long surplusSeconds;
    /**
     * 金币余额
     */
    private Long beans;

    /**
     * 发送者用户Id
     */
    private Long userId;
    /**
     * 发送者用户昵称
     */
    private String nickname;
    /**
     * 发送者用户头像
     */
    private String headPic;


    public FamilyRedPacketCreateVO(Integer redId, String title, RedPacketTimeConditionType timeConditionType, Long surplusSeconds) {
        this.redId = redId;
        this.title = title;
        this.timeCondition = timeConditionType;
        this.surplusSeconds = surplusSeconds;
        if (null == surplusSeconds) {
            this.surplusSeconds = timeCondition.getSeconds();
        }
    }

    public Integer getRedId() {
        return redId;
    }

    public void setRedId(Integer redId) {
        this.redId = redId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public RedPacketTimeConditionType getTimeCondition() {
        return timeCondition;
    }

    public void setTimeCondition(RedPacketTimeConditionType timeCondition) {
        this.timeCondition = timeCondition;
    }

    public Long getSurplusSeconds() {
        return surplusSeconds;
    }

    public void setSurplusSeconds(Long surplusSeconds) {
        this.surplusSeconds = surplusSeconds;
    }

    public Long getBeans() {
        return beans;
    }

    public void setBeans(Long beans) {
        this.beans = beans;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getHeadPic() {
        return headPic;
    }

    public void setHeadPic(String headPic) {
        this.headPic = headPic;
    }
}
