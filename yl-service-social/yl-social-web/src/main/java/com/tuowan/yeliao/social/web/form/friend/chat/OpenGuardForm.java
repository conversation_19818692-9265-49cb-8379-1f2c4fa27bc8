package com.tuowan.yeliao.social.web.form.friend.chat;

import com.easyooo.framework.validate.config.LMNotNull;
import com.tuowan.yeliao.commons.core.enums.general.BoolType;
import com.tuowan.yeliao.commons.web.common.form.Form;

/**
 * 开通守护表单
 *
 * <AUTHOR>
 * @date 2021/4/28 20:58
 */
public class OpenGuardForm implements Form {

    /** 好友ID(需要开通守护的好友ID) */
    @LMNotNull
    private Long friendId;
    /** 所需数量 */
    @LMNotNull
    private Integer count;
    /** 是否为取代 */
    @LMNotNull
    private BoolType replace;

    public Long getFriendId() {
        return friendId;
    }

    public void setFriendId(Long friendId) {
        this.friendId = friendId;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public BoolType getReplace() {
        return replace;
    }

    public void setReplace(BoolType replace) {
        this.replace = replace;
    }
}
