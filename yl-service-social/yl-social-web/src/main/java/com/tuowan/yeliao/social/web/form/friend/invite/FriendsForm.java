package com.tuowan.yeliao.social.web.form.friend.invite;

import com.easyooo.framework.validate.config.LMNotNull;
import com.tuowan.yeliao.commons.data.enums.user.SexType;
import com.tuowan.yeliao.commons.web.common.form.PageForm;

public class FriendsForm extends PageForm {
    /**
     * 性别
     */
    @LMNotNull
    private SexType type;

    /**
     * 搜索ID
     */
    private Long friendId;

    public SexType getType() {
        return type;
    }

    public void setType(SexType type) {
        this.type = type;
    }

    public Long getFriendId() {
        return friendId;
    }

    public void setFriendId(Long friendId) {
        this.friendId = friendId;
    }
}
