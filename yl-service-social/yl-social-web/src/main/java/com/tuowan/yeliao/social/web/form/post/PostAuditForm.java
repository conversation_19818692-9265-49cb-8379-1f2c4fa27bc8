package com.tuowan.yeliao.social.web.form.post;

import com.easyooo.framework.validate.config.LMNotNull;
import com.tuowan.yeliao.commons.core.enums.general.BoolType;
import com.tuowan.yeliao.commons.web.common.form.Form;

/**
 * 动态审核表单
 *
 * <AUTHOR>
 * @date 2020/11/25 10:08
 */
public class PostAuditForm implements Form {

    /**
     * 动态ID
     */
    @LMNotNull
    private Long postId;
    /**
     * 是否通过
     */
    @LMNotNull
    private BoolType pass;
    /**
     * 易盾审核结果
     */
    private Integer suggestion;
    /**
     * 审核原因
     */
    private String auditReason;
    /**
     * 备注
     */
    private String remark;
    /**
     * 审核方
     * 备注：1=CMS；2=易盾
     */
    private Integer auditSource;

    public Long getPostId() {
        return postId;
    }

    public void setPostId(Long postId) {
        this.postId = postId;
    }

    public BoolType getPass() {
        return pass;
    }

    public void setPass(BoolType pass) {
        this.pass = pass;
    }

    public Integer getSuggestion() {
        return suggestion;
    }

    public void setSuggestion(Integer suggestion) {
        this.suggestion = suggestion;
    }

    public String getAuditReason() {
        return auditReason;
    }

    public void setAuditReason(String auditReason) {
        this.auditReason = auditReason;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getAuditSource() {
        return auditSource;
    }

    public void setAuditSource(Integer auditSource) {
        this.auditSource = auditSource;
    }
}
