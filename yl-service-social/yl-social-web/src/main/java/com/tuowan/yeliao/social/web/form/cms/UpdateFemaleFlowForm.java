package com.tuowan.yeliao.social.web.form.cms;

import com.easyooo.framework.validate.config.LMNotNull;
import com.tuowan.yeliao.commons.web.common.form.Form;
import com.tuowan.yeliao.social.data.enums.cms.FemaleFlowType;

public class UpdateFemaleFlowForm implements Form {
    @LMNotNull
    private FemaleFlowType type;
    @LMNotNull
    private Long num;
    @LMNotNull
    private Long userId;

    public FemaleFlowType getType() {
        return type;
    }

    public void setType(FemaleFlowType type) {
        this.type = type;
    }

    public Long getNum() {
        return num;
    }

    public void setNum(Long num) {
        this.num = num;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }
}
