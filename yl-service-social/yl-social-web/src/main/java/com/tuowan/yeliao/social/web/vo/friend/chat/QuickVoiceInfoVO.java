package com.tuowan.yeliao.social.web.vo.friend.chat;


import com.tuowan.yeliao.commons.data.enums.social.QuickVoicePeriodType;

import java.util.List;

/**
 * 快捷语音信息封装
 *
 * <AUTHOR>
 * @date 2021/5/18 21:20
 */
public class QuickVoiceInfoVO {

    /**
     * 说明
     */
    private String remark;
    /**
     * 时段列表
     */
    private List<QuickVoicePeriodVO> periodList;

    private QuickVoicePeriodType currPeriod;

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public List<QuickVoicePeriodVO> getPeriodList() {
        return periodList;
    }

    public void setPeriodList(List<QuickVoicePeriodVO> periodList) {
        this.periodList = periodList;
    }

    public QuickVoicePeriodType getCurrPeriod() {
        return currPeriod;
    }

    public void setCurrPeriod(QuickVoicePeriodType currPeriod) {
        this.currPeriod = currPeriod;
    }
}
