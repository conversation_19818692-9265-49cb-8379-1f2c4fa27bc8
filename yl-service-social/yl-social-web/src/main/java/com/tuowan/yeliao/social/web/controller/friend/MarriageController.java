package com.tuowan.yeliao.social.web.controller.friend;


import com.tuowan.yeliao.commons.context.config.Request;
import com.tuowan.yeliao.commons.core.http.ReturnUtils;
import com.tuowan.yeliao.commons.core.http.Root;
import com.tuowan.yeliao.commons.web.common.vo.PageVO;
import com.tuowan.yeliao.social.service.friend.MarriageService;
import com.tuowan.yeliao.social.web.form.common.QueryForm;
import com.tuowan.yeliao.social.web.form.friend.marriage.ChooseForm;
import com.tuowan.yeliao.social.web.form.friend.marriage.UserPortrayForm;
import com.tuowan.yeliao.social.web.vo.marriage.MarriageChooseVO;
import com.tuowan.yeliao.social.web.vo.marriage.MarriageHomeVO;
import com.tuowan.yeliao.social.web.vo.marriage.MarriageResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * <AUTHOR>
 * @title 姻缘测算
 * @date 2021/12/28 19:31
 */
@RestController
@RequestMapping("/soc/friend/marriage")
public class MarriageController {

    @Autowired
    private MarriageService marriageService;

    /**
     * @title 首页信息
     */
    @Request
    @RequestMapping("/home")
    public Root<MarriageHomeVO> home() {
        return ReturnUtils.root(marriageService.home());
    }

    /**
     * @title 择偶信息初始化
     */
    @Request
    @RequestMapping("/choose")
    public Root<MarriageChooseVO> choose(@RequestBody ChooseForm form) {
        return ReturnUtils.root(marriageService.choose(form));
    }


    /**
     * @title 择偶信息修改
     */
    @Request
    @RequestMapping("/portray")
    public Root<Void> portray(@RequestBody UserPortrayForm form) {
        marriageService.savePortray(form);
        return ReturnUtils.empty();
    }

    /**
     * @title 测算姻缘
     */
    @Request
    @RequestMapping("/calculate")
    public Root<MarriageResultVO> calculate() {
        return ReturnUtils.root(marriageService.saveCalculate());
    }

    /**
     * @title 测算记录
     */
    @Request
    @RequestMapping("/log")
    public Root<PageVO> log(@RequestBody QueryForm form) {
        return ReturnUtils.root(marriageService.log(form));
    }
}
