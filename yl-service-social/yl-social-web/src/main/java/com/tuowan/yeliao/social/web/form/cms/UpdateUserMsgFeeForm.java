package com.tuowan.yeliao.social.web.form.cms;

import com.easyooo.framework.validate.config.LMNotNull;
import com.tuowan.yeliao.commons.web.common.form.Form;

public class UpdateUserMsgFeeForm implements Form {
    /**
     * 用户标识
     */
    @LMNotNull
    private Long userId;
    /**
     * 私信价格
     */
    @LMNotNull
    private Integer msgFee;

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Integer getMsgFee() {
        return msgFee;
    }

    public void setMsgFee(Integer msgFee) {
        this.msgFee = msgFee;
    }
}
