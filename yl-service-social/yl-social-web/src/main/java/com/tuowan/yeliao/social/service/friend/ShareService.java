package com.tuowan.yeliao.social.service.friend;

import com.alibaba.nacos.common.utils.Pair;
import com.alibaba.nacos.common.utils.StringUtils;
import com.tuowan.yeliao.commons.context.GlobalUtils;
import com.tuowan.yeliao.commons.core.additions.UrlParamsMap;
import com.tuowan.yeliao.commons.core.constant.GlobalConstant;
import com.tuowan.yeliao.commons.core.enums.general.PackageType;
import com.tuowan.yeliao.commons.core.exception.BusiException;
import com.tuowan.yeliao.commons.data.entity.user.UUserBasic;
import com.tuowan.yeliao.commons.data.enums.config.SettingsType;
import com.tuowan.yeliao.commons.data.enums.user.SexType;
import com.tuowan.yeliao.commons.data.manager.user.UserInfoManager;
import com.tuowan.yeliao.commons.data.support.config.impl.SettingsConfig;
import com.tuowan.yeliao.commons.data.support.qr.QrCodeGenWrapper;
import com.tuowan.yeliao.commons.data.utils.HtmlUrlUtils;
import com.tuowan.yeliao.commons.web.proxy.config.BusiCode;
import com.tuowan.yeliao.social.web.form.friend.ShareQrForm;
import com.tuowan.yeliao.social.web.vo.friend.share.ShareQrItemVO;
import com.tuowan.yeliao.social.web.vo.friend.share.ShareQrVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.LinkedList;
import java.util.List;

/**
 * 分享相关服务实现
 */
@Service
public class ShareService {
    private final Logger LOG = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private UserInfoManager userInfoManager;

    /**
     * 获取专属二维码
     */
    @BusiCode
    public ShareQrVO queryShareQr(ShareQrForm form) {
        try {
            UUserBasic userBasic = userInfoManager.getUserBasic(GlobalUtils.uid());
            // 返回值封装
            ShareQrVO vo = new ShareQrVO();
            String inviteCode = userBasic.getInviteCode();
            UrlParamsMap paramsMap = UrlParamsMap.build(SettingsConfig.getString(SettingsType.InviteCodeUsers));
            if(StringUtils.isNotEmpty(paramsMap.getString(String.valueOf(GlobalUtils.uid())))){
                inviteCode = paramsMap.getString(String.valueOf(GlobalUtils.uid()));
            }
            // 邀请码
            vo.setInviteInfo(inviteCode);
            // 合成图片合集
            String inviteUrl = HtmlUrlUtils.getInviteDownloadUrl(inviteCode); //带邀请人邀请码的落地页链接
            String inviteQrPic = QrCodeGenWrapper.of(inviteUrl).setWidth(120).setHeight(120).asBase64(); // 落地页链接转换成图片的 base64
            List<ShareQrItemVO> posterList = new LinkedList<>();
            Pair<String, String> posters = getPosters();
            String bgPics = userBasic.getSex() == SexType.Female ? posters.getFirst() : posters.getSecond();
            for (String pic : bgPics.split(",")){
                posterList.add(ShareQrItemVO.build1(inviteQrPic, pic));
            }
            vo.setPosterList(posterList);
            return vo;
        } catch (Exception e) {
            LOG.error("ShareService-queryShareQr-error reason:", e);
            throw new BusiException("分享二维码生成错误！！！");
        }
    }

    /** -------------------- 辅助方法 -------------------------- */
    /**
     * 根据包名获取海报
     * @return first 男
     * @return second 女
     */
    private Pair<String, String> getPosters(){
        return Pair.with(GlobalConstant.INVITE_POSTER_BG_MALE, GlobalConstant.INVITE_POSTER_BG_FEMALE);
    }

}
