package com.tuowan.yeliao.social.web.vo.friend.chat;


import com.tuowan.yeliao.commons.data.dto.user.GuardInfoDTO;

/**
 * 开通守护结果
 *
 * <AUTHOR>
 * @date 2021/4/28 20:14
 */
public class OpenGuardResultVO {

    /**
     * 守护榜首信息
     */
    private GuardInfoDTO topGuardInfo;
    /**
     * 好友关系ID（初次互动，包括聊天和送礼，会建立关系）
     */
    private String relationId;
    /**
     * 送出的礼物信息
     */
    private ChatGiftSimpleVO giftInfo;
    /**
     * 礼物提成金额
     */
    private String presentCash;

    public static OpenGuardResultVO create(GuardInfoDTO topGuardInfo, String relationId, ChatGiftSimpleVO giftInfo, String presentCash) {
        OpenGuardResultVO vo = new OpenGuardResultVO();
        vo.setTopGuardInfo(topGuardInfo);
        vo.setRelationId(relationId);
        vo.setGiftInfo(giftInfo);
        vo.setPresentCash(presentCash);
        return vo;
    }

    public GuardInfoDTO getTopGuardInfo() {
        return topGuardInfo;
    }

    public void setTopGuardInfo(GuardInfoDTO topGuardInfo) {
        this.topGuardInfo = topGuardInfo;
    }

    public String getRelationId() {
        return relationId;
    }

    public void setRelationId(String relationId) {
        this.relationId = relationId;
    }

    public ChatGiftSimpleVO getGiftInfo() {
        return giftInfo;
    }

    public void setGiftInfo(ChatGiftSimpleVO giftInfo) {
        this.giftInfo = giftInfo;
    }

    public String getPresentCash() {
        return presentCash;
    }

    public void setPresentCash(String presentCash) {
        this.presentCash = presentCash;
    }
}
