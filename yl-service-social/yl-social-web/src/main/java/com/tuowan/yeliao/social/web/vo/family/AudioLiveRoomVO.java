package com.tuowan.yeliao.social.web.vo.family;


import com.tuowan.yeliao.social.data.dto.family.AudioLiveMicMemberDTO;
import com.tuowan.yeliao.social.data.enums.family.FamilyActionType;

import java.util.List;

/**
 * 当前语音房的信息
 *
 * <AUTHOR>
 * @date 2021/7/12 9:49
 */
public class AudioLiveRoomVO {

    private Integer familyId;
    /**
     * 上麦用户列表
     */
    private List<AudioLiveMicMemberDTO> micUserList;
    /**
     * 直播标题
     */
    private String showTitle;
    /**
     * 总麦数量
     */
    private Integer micCount;
    /**
     * 语聊房行为类型
     */
    private FamilyActionType actionType;

    public Integer getFamilyId() {
        return familyId;
    }

    public void setFamilyId(Integer familyId) {
        this.familyId = familyId;
    }

    public List<AudioLiveMicMemberDTO> getMicUserList() {
        return micUserList;
    }

    public void setMicUserList(List<AudioLiveMicMemberDTO> micUserList) {
        this.micUserList = micUserList;
    }

    public String getShowTitle() {
        return showTitle;
    }

    public void setShowTitle(String showTitle) {
        this.showTitle = showTitle;
    }

    public FamilyActionType getActionType() {
        return actionType;
    }

    public void setActionType(FamilyActionType actionType) {
        this.actionType = actionType;
    }

    public Integer getMicCount() {
        return micCount;
    }

    public void setMicCount(Integer micCount) {
        this.micCount = micCount;
    }
}
