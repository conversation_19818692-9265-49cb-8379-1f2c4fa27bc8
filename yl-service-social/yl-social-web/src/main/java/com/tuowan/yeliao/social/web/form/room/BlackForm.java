package com.tuowan.yeliao.social.web.form.room;

import com.easyooo.framework.validate.config.LMNotNull;
import com.tuowan.yeliao.commons.core.enums.general.BoolType;
import com.tuowan.yeliao.commons.web.common.form.Form;

public class BlackForm implements Form {
    @LMNotNull
    private Long roomId;
    @LMNotNull
    private Long userId;
    @LMNotNull
    private BoolType type;
    @LMNotNull
    private BoolType isCms;

    public Long getRoomId() {
        return roomId;
    }

    public void setRoomId(Long roomId) {
        this.roomId = roomId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public BoolType getType() {
        return type;
    }

    public void setType(BoolType type) {
        this.type = type;
    }

    public BoolType getIsCms() {
        return isCms;
    }

    public void setIsCms(BoolType isCms) {
        this.isCms = isCms;
    }
}
