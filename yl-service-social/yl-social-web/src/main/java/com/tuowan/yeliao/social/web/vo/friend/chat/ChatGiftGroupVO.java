package com.tuowan.yeliao.social.web.vo.friend.chat;

import java.util.List;

/**
 * 礼物分组信息封装
 *
 * <AUTHOR>
 * @date 2020/9/4 10:13
 */
public class ChatGiftGroupVO {

    /**
     * 类型code
     */
    private String typeCode;
    /**
     * 类型名称
     */
    private String typeName;
    /**
     * 礼物tab版本号，如果版本号高于客户端版本号，显示新礼物红点提示
     */
    private Integer version;
    /**
     * 包含礼物
     */
    private List<ChatGiftVO> giftList;

    public ChatGiftGroupVO(String typeCode, String typeName, List<ChatGiftVO> giftList) {
        this.typeCode = typeCode;
        this.typeName = typeName;
        this.giftList = giftList;
    }

    public String getTypeCode() {
        return typeCode;
    }

    public void setTypeCode(String typeCode) {
        this.typeCode = typeCode;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public List<ChatGiftVO> getGiftList() {
        return giftList;
    }

    public void setGiftList(List<ChatGiftVO> giftList) {
        this.giftList = giftList;
    }
}
