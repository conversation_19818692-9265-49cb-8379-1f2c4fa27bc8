package com.tuowan.yeliao.social.service.friend;

import com.easyooo.framework.common.enums.DatePattern;
import com.easyooo.framework.common.util.DateUtils;
import com.easyooo.framework.common.util.JsonUtils;
import com.easyooo.framework.common.util.StringUtils;
import com.tuowan.yeliao.commons.comp.message.MessageComponent;
import com.tuowan.yeliao.commons.comp.task.dto.CpTaskProgressInfoDTO;
import com.tuowan.yeliao.commons.config.enums.ObjectLockType;
import com.tuowan.yeliao.commons.config.lock.ObjectLockTemplate;
import com.tuowan.yeliao.commons.context.GlobalUtils;
import com.tuowan.yeliao.commons.core.enums.busicode.BusiCodeDefine;
import com.tuowan.yeliao.commons.core.enums.business.SocialGiftDefine;
import com.tuowan.yeliao.commons.core.enums.general.BoolType;
import com.tuowan.yeliao.commons.core.exception.BusiException;
import com.tuowan.yeliao.commons.data.dto.social.ChatTipsInfoDTO;
import com.tuowan.yeliao.commons.data.dto.social.CpMomentDTO;
import com.tuowan.yeliao.commons.data.entity.config.TAnimation;
import com.tuowan.yeliao.commons.data.entity.config.TLevel;
import com.tuowan.yeliao.commons.data.entity.config.TProdSocialGift;
import com.tuowan.yeliao.commons.data.entity.user.UUserBasic;
import com.tuowan.yeliao.commons.data.enums.general.ClientTouchType;
import com.tuowan.yeliao.commons.data.enums.task.CpTaskStatus;
import com.tuowan.yeliao.commons.data.manager.user.UserInfoManager;
import com.tuowan.yeliao.commons.data.persistence.config.TAnimationMapper;
import com.tuowan.yeliao.commons.data.persistence.config.TProdSocialGiftMapper;
import com.tuowan.yeliao.commons.data.utils.BusiUtils;
import com.tuowan.yeliao.commons.data.utils.HtmlUrlUtils;
import com.tuowan.yeliao.commons.web.common.form.FriendIdForm;
import com.tuowan.yeliao.commons.web.proxy.config.BusiCode;
import com.tuowan.yeliao.commons.web.proxy.config.NotifyMode;
import com.tuowan.yeliao.social.comp.friend.CpComponent;
import com.tuowan.yeliao.social.data.dto.friend.CpAnniversaryDTO;
import com.tuowan.yeliao.social.data.entity.FRelationBasic;
import com.tuowan.yeliao.social.data.entity.FRelationCp;
import com.tuowan.yeliao.social.data.enums.friend.LoveSpaceStatus;
import com.tuowan.yeliao.social.data.manager.friend.FriendRelationManager;
import com.tuowan.yeliao.social.data.persistence.FRelationBasicMapper;
import com.tuowan.yeliao.social.data.persistence.FRelationCpMapper;
import com.tuowan.yeliao.social.web.form.friend.cp.CpClockQueryForm;
import com.tuowan.yeliao.social.web.form.friend.cp.CpMomentsQueryForm;
import com.tuowan.yeliao.social.web.vo.friend.chat.ChatGiftVO;
import com.tuowan.yeliao.social.web.vo.friend.chat.ChatGuideInfoVO;
import com.tuowan.yeliao.social.web.vo.friend.cp.*;
import com.tuowan.yeliao.social.web.vo.user.UserBaseInfoVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 情侣空间相关业务
 *
 * <AUTHOR>
 * @date 2021/6/3 16:55
 */
@Service
public class FriendCpService {

    @Autowired
    private UserInfoManager userInfoManager;
    @Autowired
    private FRelationBasicMapper fRelationBasicMapper;
    @Autowired
    private FriendRelationManager relationManager;
    @Autowired
    private FRelationCpMapper fRelationCpMapper;
    @Autowired
    private CpComponent cpComponent;
    @Autowired
    private TProdSocialGiftMapper tProdSocialGiftMapper;
    @Autowired
    private TAnimationMapper tAnimationMapper;
    @Autowired
    private MessageComponent messageComponent;
    @Autowired
    private ObjectLockTemplate objectLockTemplate;

    /**
     * 查询情侣空间基本信息
     */
    @BusiCode
    public CpInfoVO getInfo(FriendIdForm form) {
        // 情侣空间升级检查
        checkCpUpdateVersion(true);
        Long userId = GlobalUtils.uid();
        Long friendId = form.getFriendId();
        UUserBasic basic = userInfoManager.getUserBasic(userId);
        UUserBasic friendBasic = userInfoManager.getUserBasic(form.getFriendId());
        if (basic.getSex() == friendBasic.getSex()) {
            return null;
        }
        String relationId = BusiUtils.generateRelationId(userId, friendId);
        FRelationBasic relation = fRelationBasicMapper.selectByPrimaryKey(new FRelationBasic(relationId));
        CpInfoVO vo = new CpInfoVO();
        vo.setMyInfo(UserBaseInfoVO.create(basic));
        vo.setFriendInfo(UserBaseInfoVO.create(friendBasic));
        TLevel nextIntimateLevel = relationManager.getNextIntimateLevel(relation == null ? 0 : relation.getIntimateLevel());
        vo.setIntimateNum(0L);
        vo.setIntimateLevel(relation == null ? 0 : relation.getIntimateLevel());
        vo.setMeetDays(0L);
        vo.setStatus(LoveSpaceStatus.WaitOpen);
        vo.setNextIntimateNum(nextIntimateLevel.getExpLowerLimit());
        vo.setRuleUrl(HtmlUrlUtils.getCpSpaceUrl());
        TProdSocialGift gift = tProdSocialGiftMapper.selectByPrimaryKey(new TProdSocialGift(SocialGiftDefine.Rose.getGiftId()));
        // 礼物信息
        vo.setGiftInfo(ChatGiftVO.create(gift, getGiftSpecialParams(gift.getSpecialParams()), null, null, GlobalUtils.userLevel()));
        if (relation == null || relation.getIntimateLevel() <= 0) {
            return vo;
        }
        FRelationCp cpInfo = fRelationCpMapper.selectByPrimaryKey(new FRelationCp(relationId));
        vo.setMeetDays(DateUtils.getDiffDays(relation.getIntimateTime(), DateUtils.nowTime()) + 1);
        if (cpInfo == null) {
            vo.setIntimateNum(relation.getIntimateNum());
            vo.setStatus(LoveSpaceStatus.WaitInvite);
            return vo;
        }
        if (cpInfo.getStatus() == LoveSpaceStatus.WaitAccept) {
            if (cpInfo.getInviteUserId().equals(userId)) {
                // 如果我是邀请人，我方显示已经请对方，等待对方接受
                vo.setStatus(LoveSpaceStatus.Invited);
            } else {
                // 如果我是被邀请人，显示等待我方接受
                vo.setStatus(LoveSpaceStatus.WaitAccept);
            }
        } else {
            vo.setStatus(LoveSpaceStatus.Open);
            // 已开通，设置情侣昵称
            String cpIcon = cpComponent.getCpIcon(relation.getIntimateLevel().longValue());
            vo.getMyInfo().setCpIcon(cpIcon);
            vo.getFriendInfo().setCpIcon(cpIcon);
            vo.setMeetDays(DateUtils.getDiffDays(cpInfo.getAcceptTime(), DateUtils.nowTime()) + 1);
        }
        vo.setIntimateNum(relation.getIntimateNum());
        vo.setNextIntimateNum(nextIntimateLevel.getExpLowerLimit());
        return vo;
    }

    /**
     * 获取礼物动画参数配置
     *
     * @param specialParams
     * @return
     */
    private Map<String, Object> getGiftSpecialParams(String specialParams) {
        if (StringUtils.isEmpty(specialParams)) {
            return null;
        }
        Map<String, Object> paramMap = JsonUtils.toJsonMap(specialParams);
        String animateCode = paramMap.get("animCode") == null ? null : paramMap.get("animCode").toString();
        if (StringUtils.isNotEmpty(animateCode)) {
            TAnimation animation = tAnimationMapper.selectByPrimaryKey(new TAnimation(animateCode));
            if (animation != null) {
                paramMap.put("svgaUrl", animation.getSvgaUrl());
                paramMap.put("webpUrl", animation.getWebpUrl());
            }
        }
        return paramMap;
    }

    /**
     * 邀请开通情侣空间
     *
     * @param form
     */
    @BusiCode
    public void saveInvite(FriendIdForm form) {
        // 情侣空间升级检查
        checkCpUpdateVersion(true);
        Long userId = GlobalUtils.uid();
        Long friendId = form.getFriendId();
        UUserBasic basic = userInfoManager.getUserBasic(userId);

        UUserBasic friendBasic = userInfoManager.getUserBasic(form.getFriendId());
        if (basic.getSex() == friendBasic.getSex()) {
            throw new BusiException("只能邀请异性开通情侣空间哦");
        }
        String relationId = BusiUtils.generateRelationId(userId, friendId);
        objectLockTemplate.acquireTransactionLock(ObjectLockType.OpenCpSpace, relationId);
        FRelationCp cp = fRelationCpMapper.selectByPrimaryKey(new FRelationCp(relationId));
        if (cp == null) {
            fRelationCpMapper.insert(FRelationCp.create(relationId, userId, friendId));
            messageComponent.sendChatTipsMsg(friendBasic, userId, ChatTipsInfoDTO.create("已邀请" + friendBasic.getNickname() + "开通情侣空间"));
            String textTpl = basic.getNickname() + "邀请你开通情侣空间，${touchText}";
            messageComponent.sendChatTipsMsg(basic, friendId, ChatTipsInfoDTO.createWithTouchText(textTpl, "点击查看", null, ClientTouchType.LoveSpace));
            this.sendInviteNotice(userId, friendBasic);
        } else {
            // 如果同时点了邀请，则直接接受
            if (!cp.getInviteUserId().equals(userId) && cp.getStatus() == LoveSpaceStatus.WaitAccept) {
                cp.setStatus(LoveSpaceStatus.Open);
                cp.setAcceptTime(DateUtils.nowTime());
                fRelationCpMapper.updateByPrimaryKeySelective(cp);

                // 同步情侣空间状态到基础关系表，方便业务判断
                FRelationBasic relationBasic = new FRelationBasic(relationId);
                relationBasic.setCpStatus(BoolType.True);
                fRelationBasicMapper.updateByPrimaryKeySelective(relationBasic);

                // 生成情侣空间动态
                // cpComponent.addMoment(userId, friendId, CpMomentType.OpenLoveSpace);
            }
        }

    }

    /**
     * 发送邀请系统通知
     *
     * @param userId
     * @param friendBasic
     */
    private void sendInviteNotice(Long userId, UUserBasic friendBasic) {
        return;
        /*Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("nickname", GlobalUtils.nickname());
        paramMap.put("overrideTouchValue", userId);
        paramMap.put("sex", GlobalUtils.sexType() == SexType.Female ? "她" : "他");
        noticeComponent.sendSystemNotice(friendBasic.getUserId(), NoticeSysType.InviteOpenLoveSpace, paramMap);*/
    }

    /**
     * 接受情侣空间开通邀请
     *
     * @param form
     */
    @BusiCode
    public void saveAccept(FriendIdForm form) {
        // 情侣空间升级检查
        checkCpUpdateVersion(true);
        Long userId = GlobalUtils.uid();
        Long friendId = form.getFriendId();
        String relationId = BusiUtils.generateRelationId(userId, friendId);
        FRelationCp cp = fRelationCpMapper.selectByPrimaryKey(new FRelationCp(relationId));
        if (cp == null) {
            throw new BusiException("对方还没有邀请你哦");
        }
        if (cp.getStatus() == LoveSpaceStatus.Open) {
            throw new BusiException("情侣空间已开通，请重新打开");
        }
        cp.setStatus(LoveSpaceStatus.Open);
        cp.setAcceptTime(DateUtils.nowTime());
        fRelationCpMapper.updateByPrimaryKeySelective(cp);

        // 同步情侣空间状态到基础关系表，方便业务判断
        FRelationBasic basic = new FRelationBasic(relationId);
        basic.setCpStatus(BoolType.True);
        fRelationBasicMapper.updateByPrimaryKeySelective(basic);

        // 生成情侣空间动态
        // cpComponent.addMoment(userId, friendId, CpMomentType.OpenLoveSpace);
    }

    /**
     * 查询情侣任务进度信息
     */
    @BusiCode
    public CpTaskInfoVO queryTaskInfo(FriendIdForm form) {
        CpTaskInfoVO vo = new CpTaskInfoVO();
        Long userId = GlobalUtils.uid();
        Long friendId = form.getFriendId();
        //vo.setAwardList(cpTaskComponent.listSweetAward(userId, friendId));
        Date taskDate = DateUtils.nowTime();
        if (DateUtils.getHour(taskDate) < 4) {
            // 凌晨四点之前，还是显示昨天的任务进度
            taskDate = DateUtils.plusDays(taskDate, -1);
        }
        //vo.setTaskList(cpTaskComponent.listTaskProgressInfo(userId, GlobalUtils.sexType(), friendId, taskDate));
        return vo;
    }

    /**
     * 查询情侣空间动态列表
     */
    @BusiCode
    public CpMomentsVO queryMoments(CpMomentsQueryForm form) {
        Long userId = GlobalUtils.uid();
        Long friendId = form.getFriendId();
        Long limit = 40L;
        UUserBasic user = userInfoManager.getUserBasic(userId);
        List<CpMomentDTO> moments = cpComponent.listMoments(user, friendId, form.getLastMomentId(), limit);
        return new CpMomentsVO(moments, limit);
    }


    @BusiCode
    public CpAnniversaryVO queryAnniversary(FriendIdForm form) {
        Date nowDate = DateUtils.nowDate();
        return new CpAnniversaryVO(cpComponent.listAnniversary(GlobalUtils.uid(), form.getFriendId(), nowDate));
    }

    /**
     * 获取情侣打卡信息
     *
     * @param form
     * @return
     */
    @BusiCode(value = BusiCodeDefine.CpClock, forward = NotifyMode.ALWAYS)
    public CpClockVO saveClockInfo(CpClockQueryForm form) {
        checkCpUpdateVersion(true);
        CpClockVO vo = new CpClockVO();
        Long userId = GlobalUtils.uid();
        Long friendId = form.getFriendId();
        Date nowDate = DateUtils.nowTime();
        // 本月第一天
        Date clockMonth = DateUtils.getFirstOfMonth(nowDate);
        List<String> monthList = new ArrayList<>();
        for (int i = 0; i < 4; i++) {
            monthList.add(DateUtils.toString(DateUtils.plusMonths(clockMonth, -i), DatePattern.YMD));
        }
        vo.setMonthList(monthList);
        if (StringUtils.isNotEmpty(form.getClockMonth())) {
            clockMonth = DateUtils.parse(form.getClockMonth(), DatePattern.YMD);
        }
        vo.setClockMonth(DateUtils.toString(clockMonth, DatePattern.YMD));
        //vo.setClockList(cpTaskComponent.getClockByDate(userId, GlobalUtils.sexType(), friendId, clockMonth, nowDate));
        // 连续打卡天数
        Integer clockDays = cpComponent.getClockDays(userId, friendId);
        vo.setClockDays(clockDays);
        //vo.setAwardList(cpTaskComponent.listClockAward(userId, friendId, clockDays));
        return vo;
    }

    /**
     * 查询私信界面引导信息
     */
    @BusiCode
    public ChatGuideInfoVO queryGuideInfo(FriendIdForm form) {
        if (checkCpUpdateVersion(false)) {
            return new ChatGuideInfoVO(null, new ArrayList<>());
        }
        ChatGuideInfoVO vo = new ChatGuideInfoVO();
        Long userId = GlobalUtils.uid();
        Long friendId = form.getFriendId();
        CpTaskProgressInfoDTO taskInfo = null; //cpTaskComponent.getNowCpTask(userId, GlobalUtils.sexType(), friendId, DateUtils.nowTime());
        if (taskInfo != null && taskInfo.getTaskStatus() != CpTaskStatus.Finish && taskInfo.getTtl() > 0) {
            vo.setTaskInfo(taskInfo);
        }
        // 如果今日打开过引导界面，则不再返回纪念日引导信息
        Date nowDate = DateUtils.nowDate();
        if (!cpComponent.hasRemindAnniversary(userId, friendId)) {
            List<CpAnniversaryDTO> listAnniversary = cpComponent.listAnniversary(userId, friendId, nowDate);
            vo.setAnniversaryList(listAnniversary.stream().filter(t -> t.getDiffDays() == 0).collect(Collectors.toList()));
        }
        // 标记今日已经开启过纪念日引导
        cpComponent.markAnniversaryRemind(userId, friendId);
        return vo;
    }

    /**
     * 情侣空间升级检查
     */
    private boolean checkCpUpdateVersion(boolean errMsg) {
        return true;
//        boolean blowV220 = AppVersionUtils.isBelowTargetVersion(GlobalUtils.clientType(), GlobalUtils.clientVersion(), VersionConstant.V2_2_0);
//        if (blowV220 && errMsg) {
//            if (GlobalUtils.clientType() == ClientType.Android) {
//                throw new BusiException("升级新版本查看情侣空间");
//            }
//            throw new BusiException("情侣空间升级中，暂不支持查看");
//        }
//        return blowV220;
    }
}
