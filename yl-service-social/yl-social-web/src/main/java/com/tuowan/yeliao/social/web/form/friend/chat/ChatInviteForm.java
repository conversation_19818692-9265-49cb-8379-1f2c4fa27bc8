package com.tuowan.yeliao.social.web.form.friend.chat;

import com.easyooo.framework.validate.config.LMNotNull;
import com.tuowan.yeliao.commons.web.common.form.Form;
import com.tuowan.yeliao.social.data.enums.friend.ChatInviteType;

/**
 * 聊天邀请表单
 *
 * <AUTHOR>
 * @date 2021/3/18 17:42
 */
public class ChatInviteForm implements Form {

    /**
     * 邀请人ID
     */
    @LMNotNull
    private Long friendId;
    /**
     * 邀请类型
     */
    @LMNotNull
    private ChatInviteType inviteType;


    public Long getFriendId() {
        return friendId;
    }

    public void setFriendId(Long friendId) {
        this.friendId = friendId;
    }

    public ChatInviteType getInviteType() {
        return inviteType;
    }

    public void setInviteType(ChatInviteType inviteType) {
        this.inviteType = inviteType;
    }
}
