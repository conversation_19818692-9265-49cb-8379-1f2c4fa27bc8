package com.tuowan.yeliao.social.web.vo.friend.netcall;


import com.tuowan.yeliao.commons.core.enums.general.BoolType;
import com.tuowan.yeliao.commons.data.dto.social.GiftDTO;
import com.tuowan.yeliao.commons.data.entity.user.UUserBasic;
import com.tuowan.yeliao.commons.data.enums.social.NetCallType;
import com.tuowan.yeliao.commons.data.utils.dto.AdDefineDTO;
import com.tuowan.yeliao.social.data.dto.friend.RelationBasicDTO;
import com.tuowan.yeliao.social.data.dto.friend.netcall.VideoCallCameraCtrDTO;
import com.tuowan.yeliao.social.data.utils.agora.AgoraSdkUtils;

import java.util.List;

/**
 * 通话被叫详情信息
 *
 * <AUTHOR>
 * @date 2020/7/7 19:27
 */
public class NetCallReceiverVO {

    /** 通话ID */
    private Long callId;
    /** 通话类型 */
    private NetCallType callType;
    /** 频道ID */
    private String channelId;
    /** 鉴权Token */
    private String token;
    /** 被叫是否实名 */
    private BoolType realName;
    /** 鉴黄频率 -1 表示不鉴黄 */
    private Integer interval;
    /** 主叫人信息 */
    private NetCallUserVO callerInfo;
    /** 用户关系 */
    private NetCallRelationVO relationInfo;
    /** 是否已取消 */
    private BoolType canceled;
    /** 通话取消事件 */
    private Long cancelTime;
    /** 折扣券名称 */
    private String discountName;
    /** 广告列表 */
    private List<AdDefineDTO> adList;
    /** 免费时长 */
    private Long freeTimeLength;
    /** 通话单价 */
    private Integer beans;
    /** 当免费时显示的文本 */
    private String freeText;
    /** IOS用户接收方是否处于审核模式 */
    private BoolType iosUserInReviewVersion;
    /** 视频通话摄像头控制 */
    private VideoCallCameraCtrDTO videoCallCameraCtrDTO;

    public static NetCallReceiverVO create(Long callId, NetCallType callType, Long userId, UUserBasic caller, String callerBackPic, RelationBasicDTO relationInfo, boolean isCanceled) {
        NetCallReceiverVO vo = new NetCallReceiverVO();
        vo.setCallId(callId);
        vo.setCallType(callType);
        vo.setChannelId(AgoraSdkUtils.buildNetCallChannelId(callId));
        vo.setToken(AgoraSdkUtils.buildAuthToken(userId, vo.getChannelId()));
        vo.setCallerInfo(NetCallUserVO.create(caller, callerBackPic));
        vo.setRelationInfo(NetCallRelationVO.create(relationInfo));
        vo.setCanceled(isCanceled ? BoolType.True : null);
        return vo;
    }

    public Long getCallId() {
        return callId;
    }

    public void setCallId(Long callId) {
        this.callId = callId;
    }

    public NetCallType getCallType() {
        return callType;
    }

    public void setCallType(NetCallType callType) {
        this.callType = callType;
    }

    public String getChannelId() {
        return channelId;
    }

    public void setChannelId(String channelId) {
        this.channelId = channelId;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public BoolType getRealName() {
        return realName;
    }

    public void setRealName(BoolType realName) {
        this.realName = realName;
    }

    public Integer getInterval() {
        return interval;
    }

    public void setInterval(Integer interval) {
        this.interval = interval;
    }

    public NetCallUserVO getCallerInfo() {
        return callerInfo;
    }

    public void setCallerInfo(NetCallUserVO callerInfo) {
        this.callerInfo = callerInfo;
    }

    public NetCallRelationVO getRelationInfo() {
        return relationInfo;
    }

    public void setRelationInfo(NetCallRelationVO relationInfo) {
        this.relationInfo = relationInfo;
    }

    public BoolType getCanceled() {
        return canceled;
    }

    public void setCanceled(BoolType canceled) {
        this.canceled = canceled;
    }

    public Long getCancelTime() {
        return cancelTime;
    }

    public void setCancelTime(Long cancelTime) {
        this.cancelTime = cancelTime;
    }

    public String getDiscountName() {
        return discountName;
    }

    public void setDiscountName(String discountName) {
        this.discountName = discountName;
    }

    public List<AdDefineDTO> getAdList() {
        return adList;
    }

    public void setAdList(List<AdDefineDTO> adList) {
        this.adList = adList;
    }

    public Long getFreeTimeLength() {
        return freeTimeLength;
    }

    public void setFreeTimeLength(Long freeTimeLength) {
        this.freeTimeLength = freeTimeLength;
    }

    public Integer getBeans() {
        return beans;
    }

    public void setBeans(Integer beans) {
        this.beans = beans;
    }

    public String getFreeText() {
        return freeText;
    }

    public void setFreeText(String freeText) {
        this.freeText = freeText;
    }

    public BoolType getIosUserInReviewVersion() {
        return iosUserInReviewVersion;
    }

    public void setIosUserInReviewVersion(BoolType iosUserInReviewVersion) {
        this.iosUserInReviewVersion = iosUserInReviewVersion;
    }

    public VideoCallCameraCtrDTO getVideoCallCameraCtrDTO() {
        return videoCallCameraCtrDTO;
    }

    public void setVideoCallCameraCtrDTO(VideoCallCameraCtrDTO videoCallCameraCtrDTO) {
        this.videoCallCameraCtrDTO = videoCallCameraCtrDTO;
    }
}
