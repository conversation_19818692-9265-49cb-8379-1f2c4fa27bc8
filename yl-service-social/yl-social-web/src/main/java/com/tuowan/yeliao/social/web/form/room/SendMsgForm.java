package com.tuowan.yeliao.social.web.form.room;

import com.easyooo.framework.validate.config.LMNotEmpty;
import com.easyooo.framework.validate.config.LMNotNull;
import com.tuowan.yeliao.commons.data.enums.social.ChatContentType;
import com.tuowan.yeliao.commons.web.common.form.Form;

public class SendMsgForm implements Form {
    @LMNotNull
    private Long roomId;
    @LMNotNull
    private ChatContentType contentType;
    @LMNotEmpty
    private String content;

    public Long getRoomId() {
        return roomId;
    }

    public void setRoomId(Long roomId) {
        this.roomId = roomId;
    }

    public ChatContentType getContentType() {
        return contentType;
    }

    public void setContentType(ChatContentType contentType) {
        this.contentType = contentType;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
