package com.tuowan.yeliao.social.web.vo.family;


import com.tuowan.yeliao.commons.data.enums.user.OnlineStatus;
import com.tuowan.yeliao.commons.data.enums.user.SexType;

/**
 * 家族禁言列表VO
 *
 * <AUTHOR>
 */
public class FamilyShutupVO {

    /** 用户id */
    private Long userId;

    /** 昵称 */
    private String nickname;

    /** 头像 */
    private String headPic;

    /** 性别 */
    private SexType sex;

    /** 年龄 */
    private Integer age;

    /** 在线状态 */
    private OnlineStatus onlineStatus;

    /** 在线状态描述 */
    private String onlineStatusText;

    /** 剩余时间（单位秒） */
    private Long seconds;

    public FamilyShutupVO(Long userId, String nickname, String headPic, SexType sex, Integer age, OnlineStatus onlineStatus, String onlineStatusText, Long seconds) {
        this.userId = userId;
        this.nickname = nickname;
        this.headPic = headPic;
        this.sex = sex;
        this.age = age;
        this.onlineStatus = onlineStatus;
        this.onlineStatusText = onlineStatusText;
        this.seconds = seconds;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getHeadPic() {
        return headPic;
    }

    public void setHeadPic(String headPic) {
        this.headPic = headPic;
    }

    public SexType getSex() {
        return sex;
    }

    public void setSex(SexType sex) {
        this.sex = sex;
    }

    public Integer getAge() {
        return age;
    }

    public void setAge(Integer age) {
        this.age = age;
    }

    public OnlineStatus getOnlineStatus() {
        return onlineStatus;
    }

    public void setOnlineStatus(OnlineStatus onlineStatus) {
        this.onlineStatus = onlineStatus;
    }

    public String getOnlineStatusText() {
        return onlineStatusText;
    }

    public void setOnlineStatusText(String onlineStatusText) {
        this.onlineStatusText = onlineStatusText;
    }

    public Long getSeconds() {
        return seconds;
    }

    public void setSeconds(Long seconds) {
        this.seconds = seconds;
    }
}
