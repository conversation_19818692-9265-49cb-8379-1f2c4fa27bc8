package com.tuowan.yeliao.social.web.vo.friend.mgw;

public class MgwInfoItemVO {
    // 送礼方ID
    private Long senderUserId;
    // 送礼方头像
    private String senderHeadPic;
    // 送礼方昵称
    private String senderNickname;

    // 收礼方ID
    private Long receiverUserId;
    // 收礼方头像
    private String receiverHeadPic;
    // 收礼方昵称
    private String receiverNickname;

    // 礼物图片
    private String giftPic;
    // 礼物名称
    private String giftName;
    // 礼物金币
    private Integer giftBeans;
    // 礼物寄语
    private String mgMessage;
    // 送礼时间格式化
    private String timeText;

    public Long getSenderUserId() {
        return senderUserId;
    }

    public void setSenderUserId(Long senderUserId) {
        this.senderUserId = senderUserId;
    }

    public String getSenderHeadPic() {
        return senderHeadPic;
    }

    public void setSenderHeadPic(String senderHeadPic) {
        this.senderHeadPic = senderHeadPic;
    }

    public String getSenderNickname() {
        return senderNickname;
    }

    public void setSenderNickname(String senderNickname) {
        this.senderNickname = senderNickname;
    }

    public Long getReceiverUserId() {
        return receiverUserId;
    }

    public void setReceiverUserId(Long receiverUserId) {
        this.receiverUserId = receiverUserId;
    }

    public String getReceiverHeadPic() {
        return receiverHeadPic;
    }

    public void setReceiverHeadPic(String receiverHeadPic) {
        this.receiverHeadPic = receiverHeadPic;
    }

    public String getReceiverNickname() {
        return receiverNickname;
    }

    public void setReceiverNickname(String receiverNickname) {
        this.receiverNickname = receiverNickname;
    }

    public String getGiftPic() {
        return giftPic;
    }

    public void setGiftPic(String giftPic) {
        this.giftPic = giftPic;
    }

    public String getGiftName() {
        return giftName;
    }

    public void setGiftName(String giftName) {
        this.giftName = giftName;
    }

    public Integer getGiftBeans() {
        return giftBeans;
    }

    public void setGiftBeans(Integer giftBeans) {
        this.giftBeans = giftBeans;
    }

    public String getMgMessage() {
        return mgMessage;
    }

    public void setMgMessage(String mgMessage) {
        this.mgMessage = mgMessage;
    }

    public String getTimeText() {
        return timeText;
    }

    public void setTimeText(String timeText) {
        this.timeText = timeText;
    }
}
