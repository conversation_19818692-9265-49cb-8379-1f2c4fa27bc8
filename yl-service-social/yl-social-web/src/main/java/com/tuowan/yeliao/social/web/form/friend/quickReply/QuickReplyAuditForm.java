package com.tuowan.yeliao.social.web.form.friend.quickReply;

import com.tuowan.yeliao.commons.data.enums.general.ReviewResultType;
import com.tuowan.yeliao.commons.web.common.form.Form;
import com.easyooo.framework.validate.config.LMNotNull;

/**
 * 快捷回复审核表单
 *
 * <AUTHOR>
 * @date 2021/12/13 11:38
 */
public class QuickReplyAuditForm implements Form {

    /** 快捷回复ID */
    @LMNotNull
    private Long replyId;
    /** 是否通过 */
    private ReviewResultType auditStatus;
    /** 审核原因 */
    private String auditReason;

    public Long getReplyId() {
        return replyId;
    }

    public void setReplyId(Long replyId) {
        this.replyId = replyId;
    }

    public ReviewResultType getAuditStatus() {
        return auditStatus;
    }

    public void setAuditStatus(ReviewResultType auditStatus) {
        this.auditStatus = auditStatus;
    }

    public String getAuditReason() {
        return auditReason;
    }

    public void setAuditReason(String auditReason) {
        this.auditReason = auditReason;
    }
}
