package com.tuowan.yeliao.social.api.request;

import com.tuowan.yeliao.commons.context.GlobalContext;
import com.tuowan.yeliao.commons.context.request.ContextRequest;

/**
 * 封禁用户请求
 *
 * <AUTHOR>
 * @date 2022/8/26 11:46
 */
public class BanUserRequest extends ContextRequest {

    /** 用户ID */
    private Long userId;

    public static BanUserRequest build(GlobalContext context, Long userId) {
        BanUserRequest request = new BanUserRequest();
        request.setContext(context);
        request.setUserId(userId);
        return request;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }
}
