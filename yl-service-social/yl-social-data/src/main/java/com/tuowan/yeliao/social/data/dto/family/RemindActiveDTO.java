package com.tuowan.yeliao.social.data.dto.family;

import com.easyooo.framework.common.util.MsgUtils;

/**
 * 提醒活跃DTO
 *
 * <AUTHOR>
 * @date 2021/8/27 13:39
 */
public class RemindActiveDTO {

    /**
     * 需要提醒的人数
     */
    private Integer remindNum;

    /**
     * 提醒活跃描述
     */
    private String desc;

    public RemindActiveDTO(Integer remindNum, Integer zeroNum) {
        this.remindNum = remindNum;
        this.desc = MsgUtils.format("{}人近三天家族贡献为0", zeroNum);
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public Integer getRemindNum() {
        return remindNum;
    }

    public void setRemindNum(Integer remindNum) {
        this.remindNum = remindNum;
    }
}
