package com.tuowan.yeliao.social.data.search.document;

import com.easyooo.framework.common.util.RandomUtils;
import com.easyooo.framework.support.elasticsearch.Document;
import com.easyooo.framework.support.elasticsearch.ESMapping;
import com.easyooo.framework.support.elasticsearch.MappingType;
import com.tuowan.yeliao.commons.core.enums.general.BoolType;
import com.tuowan.yeliao.commons.data.entity.user.UMessageSetting;
import com.tuowan.yeliao.commons.data.entity.user.UUserBasic;
import com.tuowan.yeliao.commons.data.entity.user.UUserExt;
import com.tuowan.yeliao.commons.data.entity.user.UUserLocation;
import com.tuowan.yeliao.commons.data.enums.user.OnlineStatus;
import com.tuowan.yeliao.commons.data.enums.user.SexType;
import com.tuowan.yeliao.commons.data.utils.BusiUtils;

import java.util.Date;

/**
 * 用户特征信息，包括地理位置，标签等
 * 用于附近用户筛选
 *
 * <AUTHOR>
 * @date 2020/12/14 19:31
 */
@Document(index = "usr-feature", keyProperty = "id")
public class UserFeature {
    /**
     * 用户ID
     */
    @ESMapping(type = MappingType.Long)
    private Long id;

    /**
     * 用户性别
     */
    @ESMapping(type = MappingType.Keyword)
    private SexType sex;

    /**
     * 在线状态
     */
    @ESMapping(type = MappingType.Keyword)
    private String onlineStatus;

    /**
     * 出生日期
     */
    @ESMapping(type = MappingType.Long)
    private Long birth;

    /**
     * 用户地理位置信息， 纬度,经度
     */
    @ESMapping(type = MappingType.GeoPoint)
    private String loc;

    /**
     * 上次在线时间
     */
    @ESMapping(type = MappingType.Long)
    private Long lastTime;

    /**
     * 用户注册时间
     */
    @ESMapping(type = MappingType.Long)
    private Long createTime;

    /**
     * 用户最近充值时间
     */
    @ESMapping(type = MappingType.Long)
    private Long lastRechargeTime;

    /**
     * 位置是否保密
     * 1 => 位置保密
     * 0 => 位置不保密
     */
    @ESMapping(type = MappingType.Long)
    private Long hideLoc;

    /**
     * 聊主等级
     */
    @ESMapping(type = MappingType.Integer)
    private Integer chatMasterLevel;

    /**
     * 随机数
     * 备注：该值在 1000 ~ 2000 之间，用户每次上线都会重新随机一个值
     */
    @ESMapping(type = MappingType.Integer)
    private Integer randomNum;

    /**
     * 是否通话中
     * 备注：0 没有通话；1 正在通话；2 关闭了视频通话服务
     */
    @ESMapping(type = MappingType.Integer)
    private Integer inCall;

    /**
     * 附近列表排序分
     */
    @ESMapping(type = MappingType.Long)
    private Long nscore;

    /**
     * 推荐列表排序分
     */
    @ESMapping(type = MappingType.Long)
    private Long rscore;

    /**
     * 预留字段1
     * 备注：该字段存储女用户的额外分数 男用户该字段始终为0
     */
    @ESMapping(type = MappingType.Long)
    private Long data1;

    /**
     * 预留字段2
     */
    @ESMapping(type = MappingType.Long)
    private Long data2;

    /**
     * 预留字段3
     */
    @ESMapping(type = MappingType.Keyword)
    private String data3;

    /**
     * 预留字段4
     * 备注：该字段目前用于存储用户昵称
     */
    @ESMapping(type = MappingType.Text)
    private String data4;

    public UserFeature() {
    }

    public UserFeature(Long id){
        this.id = id;
    }

    public UserFeature(UUserBasic user, UUserExt ext, UMessageSetting setting, UUserLocation loc, Long rscore, OnlineStatus onlineStatus, BoolType inCall, Long extScore) {
        this.id = user.getUserId();
        this.sex = user.getSex();
        this.onlineStatus = onlineStatus.getId();
        this.birth = user.getBirthDate().getTime();
        this.loc = loc == null ? null : BusiUtils.buildUserLocForEs(loc.getLng(), loc.getLat());

        this.lastTime = ext.getLastOpenTime() == null ? null : ext.getLastOpenTime().getTime();
        this.lastRechargeTime = ext.getLastRechargeTime() == null ? null : ext.getLastRechargeTime().getTime();
        this.createTime = user.getCreateTime().getTime();

        this.hideLoc = BoolType.True == setting.getHideLocation() ? 1L : 0L;
        this.chatMasterLevel = user.getChatMasterLevel().getLevel();
        this.randomNum = RandomUtils.getInt(1000, 2000);
        this.inCall = BoolType.True == inCall ? 1 : 0;

        this.nscore = 0L;  // 暂时没有用 默认先给0
        this.rscore = rscore;

        this.data1 = extScore;
        // 以下扩展字段 暂时都没有用
        this.data2 = 0L;
        this.data3 = null;
        this.data4 = user.getNickname();
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public SexType getSex() {
        return sex;
    }

    public void setSex(SexType sex) {
        this.sex = sex;
    }

    public String getOnlineStatus() {
        return onlineStatus;
    }

    public void setOnlineStatus(String onlineStatus) {
        this.onlineStatus = onlineStatus;
    }

    public Long getBirth() {
        return birth;
    }

    public void setBirth(Long birth) {
        this.birth = birth;
    }

    public String getLoc() {
        return loc;
    }

    public void setLoc(String loc) {
        this.loc = loc;
    }

    public Long getLastTime() {
        return lastTime;
    }

    public void setLastTime(Long lastTime) {
        this.lastTime = lastTime;
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long getLastRechargeTime() {
        return lastRechargeTime;
    }

    public void setLastRechargeTime(Long lastRechargeTime) {
        this.lastRechargeTime = lastRechargeTime;
    }

    public Long getHideLoc() {
        return hideLoc;
    }

    public void setHideLoc(Long hideLoc) {
        this.hideLoc = hideLoc;
    }

    public Integer getChatMasterLevel() {
        return chatMasterLevel;
    }

    public void setChatMasterLevel(Integer chatMasterLevel) {
        this.chatMasterLevel = chatMasterLevel;
    }

    public Integer getRandomNum() {
        return randomNum;
    }

    public void setRandomNum(Integer randomNum) {
        this.randomNum = randomNum;
    }

    public Integer getInCall() {
        return inCall;
    }

    public void setInCall(Integer inCall) {
        this.inCall = inCall;
    }

    public Long getNscore() {
        return nscore;
    }

    public void setNscore(Long nscore) {
        this.nscore = nscore;
    }

    public Long getRscore() {
        return rscore;
    }

    public void setRscore(Long rscore) {
        this.rscore = rscore;
    }

    public Long getData1() {
        return data1;
    }

    public void setData1(Long data1) {
        this.data1 = data1;
    }

    public Long getData2() {
        return data2;
    }

    public void setData2(Long data2) {
        this.data2 = data2;
    }

    public String getData3() {
        return data3;
    }

    public void setData3(String data3) {
        this.data3 = data3;
    }

    public String getData4() {
        return data4;
    }

    public void setData4(String data4) {
        this.data4 = data4;
    }
}
