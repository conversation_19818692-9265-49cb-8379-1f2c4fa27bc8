<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tuowan.yeliao.social.data.persistence.UUserGiftGroupStatMapper">
  <sql id="Base_Column_List">
    user_id, gift_id, group_id, gift_count
  </sql>
  <select id="selectByPrimaryKey" parameterType="UUserGiftGroupStat" resultType="UUserGiftGroupStat">
    select 
    <include refid="Base_Column_List" />
    from u_user_gift_group_stat
    where user_id = #{userId}
      and gift_id = #{giftId}
      and group_id = #{groupId}
  </select>
    <delete id="deleteByPrimaryKey" parameterType="UUserGiftGroupStat">
    delete from u_user_gift_group_stat
    where user_id = #{userId}
      and gift_id = #{giftId}
      and group_id = #{groupId}
  </delete>
  <insert id="insert" parameterType="UUserGiftGroupStat">
    insert into u_user_gift_group_stat (user_id, gift_id, group_id, gift_count)
    values (#{userId}, #{giftId}, #{groupId}, #{giftCount})
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="UUserGiftGroupStat">
    update u_user_gift_group_stat
    <set>
      <if test="giftCount != null">
        gift_count = #{giftCount},
      </if>
    </set>
    where user_id = #{userId}
      and gift_id = #{giftId}
      and group_id = #{groupId}
  </update>
  <update id="updateByPrimaryKey" parameterType="UUserGiftGroupStat">
    update u_user_gift_group_stat
    set gift_count = gift_count + #{giftCount}
    where user_id = #{userId}
      and gift_id = #{giftId}
      and group_id = #{groupId}
  </update>
  <select id="selectByUserId" resultType="UUserGiftGroupStat">
    select
    <include refid="Base_Column_List" />
    from u_user_gift_group_stat
    where user_id = #{userId}
  </select>

</mapper>