<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tuowan.yeliao.social.data.persistence.FRelationCpMapper">
    <sql id="Base_Column_List">
        relation_id, invite_user_id, accept_user_id, status, invite_time, accept_time, sweet_value
    </sql>
    <select id="selectByPrimaryKey" parameterType="FRelationCp" resultType="FRelationCp">
        select
        <include refid="Base_Column_List"/>
        from f_relation_cp
        where relation_id = #{relationId}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="FRelationCp">
        delete from f_relation_cp
        where relation_id = #{relationId}
    </delete>
    <insert id="insert" parameterType="FRelationCp">
        insert into f_relation_cp (relation_id, invite_user_id, accept_user_id, status, invite_time,
        accept_time, sweet_value)
        values (#{relationId}, #{inviteUserId}, #{acceptUserId}, #{status}, #{inviteTime},
        #{acceptTime}, #{sweetValue})
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="FRelationCp">
        update f_relation_cp
        <set>
            <if test="inviteUserId != null">
                invite_user_id = #{inviteUserId},
            </if>
            <if test="acceptUserId != null">
                accept_user_id = #{acceptUserId},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="inviteTime != null">
                invite_time = #{inviteTime},
            </if>
            <if test="acceptTime != null">
                accept_time = #{acceptTime},
            </if>
            <if test="sweetValue != null">
                sweet_value = sweet_value + #{sweetValue},
            </if>
        </set>
        where relation_id = #{relationId}
    </update>
    <update id="updateByPrimaryKey" parameterType="FRelationCp">
        update f_relation_cp
        set invite_user_id = #{inviteUserId},
        accept_user_id = #{acceptUserId},
        status = #{status},
        invite_time = #{inviteTime},
        accept_time = #{acceptTime},
        sweet_value = #{sweetValue}
        where relation_id = #{relationId}
    </update>
</mapper>