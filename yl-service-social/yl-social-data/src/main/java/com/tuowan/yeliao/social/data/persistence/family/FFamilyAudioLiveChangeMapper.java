/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.social.data.persistence.family;

import com.easyooo.framework.sharding.annotation.Table;
import com.tuowan.yeliao.social.data.entity.family.FFamilyAudioLiveChange;
import org.springframework.stereotype.Repository;

@Repository
@Table(value = "F_FAMILY_AUDIO_LIVE_CHANGE", schema = "YL_SOCIAL")
public interface FFamilyAudioLiveChangeMapper {
    int deleteByPrimaryKey(FFamilyAudioLiveChange record);

    int insert(FFamilyAudioLiveChange record);

    FFamilyAudioLiveChange selectByPrimaryKey(FFamilyAudioLiveChange record);

    int updateByPrimaryKeySelective(FFamilyAudioLiveChange record);

    int updateByPrimaryKey(FFamilyAudioLiveChange record);
}