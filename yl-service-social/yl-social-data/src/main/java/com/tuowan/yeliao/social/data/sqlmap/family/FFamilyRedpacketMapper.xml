<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tuowan.yeliao.social.data.persistence.family.FFamilyRedpacketMapper">
  <sql id="Base_Column_List">
    red_id, title, red_cnt, total_beans, red_status, red_type, status_time, family_id, family_condition, sex_condition,time_condition,
    create_time, creator
  </sql>
  <select id="selectByPrimaryKey" parameterType="FFamilyRedpacket" resultType="FFamilyRedpacket">
    select 
    <include refid="Base_Column_List" />
    from f_family_redpacket
    where red_id = #{redId}
  </select>
    <delete id="deleteByPrimaryKey" parameterType="FFamilyRedpacket">
    delete from f_family_redpacket
    where red_id = #{redId}
  </delete>
  <insert id="insert" parameterType="FFamilyRedpacket" useGeneratedKeys="true" keyProperty="redId">
    insert into f_family_redpacket (red_id, title, red_cnt, total_beans, red_status, red_type, status_time, family_id,
      family_condition, sex_condition, time_condition,create_time, creator)
    values (#{redId}, #{title}, #{redCnt}, #{totalBeans}, #{redStatus}, #{redType}, #{statusTime}, #{familyId},
      #{familyCondition}, #{sexCondition},#{timeCondition}, #{createTime}, #{creator})
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="FFamilyRedpacket">
    update f_family_redpacket
    <set>
      <if test="title != null">
        title = #{title},
      </if>
      <if test="redCnt != null">
        red_cnt = #{redCnt},
      </if>
      <if test="totalBeans != null">
        total_beans = #{totalBeans},
      </if>
      <if test="redStatus != null">
        red_status = #{redStatus},
      </if>
      <if test="redType != null">
        red_type = #{redType},
      </if>
      <if test="statusTime != null">
        status_time = #{statusTime},
      </if>
      <if test="familyId != null">
        family_id = #{familyId},
      </if>
      <if test="familyCondition != null">
        family_condition = #{familyCondition},
      </if>
      <if test="sexCondition != null">
        sex_condition = #{sexCondition},
      </if>
      <if test="timeCondition != null">
        time_condition = #{timeCondition},
      </if>
      <if test="createTime != null">
        create_time = #{createTime},
      </if>
      <if test="creator != null">
        creator = #{creator},
      </if>
    </set>
    where red_id = #{redId}
  </update>
  <update id="updateByPrimaryKey" parameterType="FFamilyRedpacket">
    update f_family_redpacket
    set title = #{title},
      red_cnt = #{redCnt},
      total_beans = #{totalBeans},
      red_status = #{redStatus},
    red_type = #{redType},
    status_time = #{statusTime},
      family_id = #{familyId},
      family_condition = #{familyCondition},
      sex_condition = #{sexCondition},
      time_condition = #{timeCondition},
      create_time = #{createTime},
      creator = #{creator}
    where red_id = #{redId}
  </update>
  <select id="selectByType" resultType="FFamilyRedpacket">
    select
    <include refid="Base_Column_List" />
    from f_family_redpacket
    where creator = #{userId}
    and red_type = #{redType}
    limit 1
  </select>

</mapper>