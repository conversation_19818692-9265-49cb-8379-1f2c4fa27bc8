<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tuowan.yeliao.social.data.persistence.SCosmosStatMapper">
    <sql id="Base_Column_List">
        stat_id
        , cosmos_id, user_id, create_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="SCosmosStat" resultType="SCosmosStat">
        select
        <include refid="Base_Column_List"/>
        from s_cosmos_stat
        where stat_id = #{statId}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="SCosmosStat">
        delete
        from s_cosmos_stat
        where stat_id = #{statId}
    </delete>
    <insert id="insert" parameterType="SCosmosStat" keyProperty="statId" useGeneratedKeys="true">
        insert into s_cosmos_stat (stat_id, cosmos_id, user_id, create_time)
        values (#{statId}, #{cosmosId}, #{userId}, #{createTime})
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="SCosmosStat">
        update s_cosmos_stat
        <set>
            <if test="cosmosId != null">
                cosmos_id = #{cosmosId},
            </if>
            <if test="userId != null">
                user_id = #{userId},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
        </set>
        where stat_id = #{statId}
    </update>
    <update id="updateByPrimaryKey" parameterType="SCosmosStat">
        update s_cosmos_stat
        set cosmos_id   = #{cosmosId},
            user_id     = #{userId},
            create_time = #{createTime}
        where stat_id = #{statId}
    </update>

    <select id="countCosmosWatch" resultType="java.lang.Integer">
        select count(1)
        from s_cosmos_stat
        where cosmos_id = #{cosmosId}
    </select>

</mapper>