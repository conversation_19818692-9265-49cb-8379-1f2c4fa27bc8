package com.tuowan.yeliao.social.data.dto.family;


import com.tuowan.yeliao.commons.core.enums.general.BoolType;
import com.tuowan.yeliao.commons.data.enums.user.SexType;
import com.tuowan.yeliao.social.data.enums.family.FamilyRoleType;

/**
 * 语聊直播上麦成员信息
 *
 * <AUTHOR>
 * @date 2021/7/12 9:50
 */
public class AudioLiveMicMemberDTO {

    /**
     * 麦序
     */
    private Integer micNo;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 昵称
     */
    private String nickname;
    /**
     * 头像
     */
    private String headPic;

    /**
     * 头像框
     */
    private String headFrame;
    /**
     * 性别
     */
    private SexType sex;

    /**
     * 家族身份类型
     */
    private FamilyRoleType familyRoleType;
    private String familyRole;
    private String familyRolePic;
    /**
     * 是否闭麦， True|False
     */
    private BoolType mute;
    /**
     * 是否锁麦
     */
    private BoolType lock;

    public static AudioLiveMicMemberDTO createLockMic(Integer micNo) {
        AudioLiveMicMemberDTO dto = new AudioLiveMicMemberDTO();
        dto.setMicNo(micNo);
        dto.setLock(BoolType.True);
        return dto;

    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getHeadPic() {
        return headPic;
    }

    public void setHeadPic(String headPic) {
        this.headPic = headPic;
    }

    public String getHeadFrame() {
        return headFrame;
    }

    public void setHeadFrame(String headFrame) {
        this.headFrame = headFrame;
    }

    public SexType getSex() {
        return sex;
    }

    public void setSex(SexType sex) {
        this.sex = sex;
    }

    public Integer getMicNo() {
        return micNo;
    }

    public void setMicNo(Integer micNo) {
        this.micNo = micNo;
    }

    public String getFamilyRole() {
        if (null != familyRoleType) {
            return familyRoleType.getDesc();
        }
        return familyRole;
    }

    public void setFamilyRole(String familyRole) {
        this.familyRole = familyRole;
    }

    public FamilyRoleType getFamilyRoleType() {
        return familyRoleType;
    }

    public void setFamilyRoleType(FamilyRoleType familyRoleType) {
        this.familyRoleType = familyRoleType;
    }

    public String getFamilyRolePic() {
        if (null != familyRoleType) {
            return familyRoleType.getRolePic();
        }
        return familyRolePic;
    }

    public void setFamilyRolePic(String familyRolePic) {
        this.familyRolePic = familyRolePic;
    }

    public BoolType getMute() {
        return mute;
    }

    public void setMute(BoolType mute) {
        this.mute = mute;
    }

    public BoolType getLock() {
        return lock;
    }

    public void setLock(BoolType lock) {
        this.lock = lock;
    }
}
