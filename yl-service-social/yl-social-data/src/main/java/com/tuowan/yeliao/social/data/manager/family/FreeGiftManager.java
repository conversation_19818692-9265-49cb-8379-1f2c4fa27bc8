package com.tuowan.yeliao.social.data.manager.family;


import com.tuowan.yeliao.commons.config.redis.RedisKey;
import com.tuowan.yeliao.commons.config.redis.template.SocialRedisTemplate;
import com.tuowan.yeliao.commons.core.enums.business.SocialGiftDefine;
import com.tuowan.yeliao.commons.core.enums.redis.SocialKeyDefine;
import com.tuowan.yeliao.commons.data.entity.config.TProdSocialGift;
import com.tuowan.yeliao.commons.data.entity.user.UUserBasic;
import com.tuowan.yeliao.commons.data.persistence.config.TProdSocialGiftMapper;
import com.tuowan.yeliao.social.data.dto.friend.FreeGiftInfoDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 免费礼物业务封装
 *
 * <AUTHOR>
 * @date 2021/9/13 17:36
 */
@Component
public class FreeGiftManager {

    @Autowired
    private TProdSocialGiftMapper tProdSocialGiftMapper;
    @Autowired
    private SocialRedisTemplate socialRedisTemplate;


    /**
     * 领取免费礼物
     */
    public FreeGiftInfoDTO receiveGift(Long userId) {
        RedisKey receiveGiftCntKey = buildReceivedGiftCntKey();
        Long totalCount = Math.min(socialRedisTemplate.hincrBy(receiveGiftCntKey, userId.toString(), 1L), 3);
        socialRedisTemplate.expire(receiveGiftCntKey);
        Integer sendCount = socialRedisTemplate.hgetInt(buildSendGiftCntKey(), userId.toString());
        FreeGiftInfoDTO vo = new FreeGiftInfoDTO();
        TProdSocialGift gift = tProdSocialGiftMapper.selectByPrimaryKey(new TProdSocialGift(SocialGiftDefine.Chocolate.getGiftId()));
        vo.setGiftId(gift.getGiftId());
        vo.setGiftName(gift.getGiftName());
        vo.setPic(gift.getPic());
//        if (totalCount >= 3) {
//            vo.setGiftCnt(totalCount.intValue() - sendCount);
//            vo.setNextGiftTtl(-1L);
//        } else {
//            vo.setGiftCnt(totalCount.intValue() - sendCount);
//            vo.setNextGiftTtl(getNextGiftTtl(totalCount.intValue()));
//        }
        // 免费礼物功能下线
        vo.setGiftCnt(0);
        vo.setNextGiftTtl(-1L);
        return vo;
    }

    /**
     * 进入家族获取免费礼物信息
     */
    public FreeGiftInfoDTO getFreeGiftInfo(UUserBasic basic) {
//        if (DateUtils.nowTime().before(DateUtils.plusDays(DateUtils.trunc(basic.getCreateTime()), 3))) {
//            Long userId = basic.getUserId();
//            Integer sendCount = busiRedisTemplate.hgetInt(buildSendGiftCntKey(), userId.toString());
//            if (sendCount >= 3) {
//                return null;
//            }
//            FreeGiftInfoDTO vo = new FreeGiftInfoDTO();
//            TProdSocialGift gift = tProdSocialGiftMapper.selectByPrimaryKey(new TProdSocialGift(SocialGiftDefine.Chocolate.getGiftId()));
//            vo.setGiftId(gift.getGiftId());
//            vo.setGiftName(gift.getGiftName());
//            vo.setPic(gift.getPic());
//            Integer receivedCnt = Math.min(3, busiRedisTemplate.hgetInt(buildReceivedGiftCntKey(), userId.toString()));
//            vo.setNextGiftTtl(getNextGiftTtl(receivedCnt));
//            vo.setGiftCnt(receivedCnt - sendCount);
//            return vo;
//        }
        // 注册超过三天之后不可见
        return null;
    }

    /**
     * 标记送出免费礼物
     *
     * @param userId
     * @param sendCount
     */
    public void sendFreeGift(Long userId, Integer sendCount) {
        // 免费礼物送出
        RedisKey redisKey = RedisKey.create(SocialKeyDefine.SendFreeGiftCount);
        Long freeCount = socialRedisTemplate.hincrBy(redisKey, userId.toString(), sendCount.longValue());
        if (freeCount.equals(sendCount.longValue())) {
            socialRedisTemplate.expire(redisKey);
        }
    }

    /**
     * 获取下一个免费礼物的倒计时
     *
     * @param receivedCnt
     * @return
     */
    private Long getNextGiftTtl(Integer receivedCnt) {
        if (receivedCnt >= 3) {
            return -1L;
        }
        if (receivedCnt == 0) {
            return 30L;
        } else if (receivedCnt == 1) {
            return 60L;
        }
        return 90L;
    }


    private RedisKey buildReceivedGiftCntKey() {
        return RedisKey.create(SocialKeyDefine.ReceivedFreeGiftCount);
    }

    private RedisKey buildSendGiftCntKey() {
        return RedisKey.create(SocialKeyDefine.SendFreeGiftCount);
    }


}
