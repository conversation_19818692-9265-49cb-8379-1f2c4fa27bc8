<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tuowan.yeliao.social.data.persistence.family.FFamilyAudioLiveMicHisMapper">
  <sql id="Base_Column_List">
    user_id, start_time, family_id, show_tid, mic_no, end_time, end_type, optr_user_id,on_type
  </sql>
  <select id="selectByPrimaryKey" parameterType="FFamilyAudioLiveMicHis" resultType="FFamilyAudioLiveMicHis">
    select 
    <include refid="Base_Column_List" />
    from f_family_audio_live_mic_his
    where user_id = #{userId}
      and start_time = #{startTime}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="FFamilyAudioLiveMicHis">
    delete from f_family_audio_live_mic_his
    where user_id = #{userId}
      and start_time = #{startTime}
  </delete>
  <insert id="insert" parameterType="FFamilyAudioLiveMicHis">
    insert into f_family_audio_live_mic_his (user_id, start_time, family_id, show_tid, mic_no, end_time, end_type, 
      optr_user_id,on_type)
    values (#{userId}, #{startTime}, #{familyId}, #{showTid}, #{micNo}, #{endTime}, #{endType}, 
      #{optrUserId},#{onType})
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="FFamilyAudioLiveMicHis">
    update f_family_audio_live_mic_his
    <set>
      <if test="familyId != null">
        family_id = #{familyId},
      </if>
      <if test="showTid != null">
        show_tid = #{showTid},
      </if>
      <if test="micNo != null">
        mic_no = #{micNo},
      </if>
      <if test="endTime != null">
        end_time = #{endTime},
      </if>
      <if test="endType != null">
        end_type = #{endType},
      </if>
      <if test="optrUserId != null">
        optr_user_id = #{optrUserId},
      </if>
      <if test="onType != null">
        on_type = #{onType},
      </if>
    </set>
    where user_id = #{userId}
      and start_time = #{startTime}
  </update>
  <update id="updateByPrimaryKey" parameterType="FFamilyAudioLiveMicHis">
    update f_family_audio_live_mic_his
    set family_id = #{familyId},
      show_tid = #{showTid},
      mic_no = #{micNo},
      end_time = #{endTime},
      end_type = #{endType},
      optr_user_id = #{optrUserId},
      on_type = #{onType}
    where user_id = #{userId}
      and start_time = #{startTime}
  </update>
</mapper>