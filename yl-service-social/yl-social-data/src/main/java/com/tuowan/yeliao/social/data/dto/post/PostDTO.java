package com.tuowan.yeliao.social.data.dto.post;

import com.easyooo.framework.common.util.StringUtils;
import com.tuowan.yeliao.commons.core.enums.general.BoolType;
import com.tuowan.yeliao.commons.core.enums.general.user.UserType;
import com.tuowan.yeliao.commons.data.dto.user.OnlineDTO;
import com.tuowan.yeliao.commons.data.dto.user.UserBusiDTO;
import com.tuowan.yeliao.commons.data.entity.user.UUserBasic;
import com.tuowan.yeliao.commons.data.enums.general.ReviewResultType;
import com.tuowan.yeliao.commons.data.enums.user.OnlineStatus;
import com.tuowan.yeliao.commons.data.enums.user.SexType;
import com.tuowan.yeliao.commons.data.enums.user.VipType;
import com.tuowan.yeliao.commons.data.utils.BusiUtils;
import com.tuowan.yeliao.social.data.entity.post.SPost;

import java.util.ArrayList;
import java.util.List;

/**
 * 动态内容DTO
 *
 * <AUTHOR>
 * @date 2020/11/20 09:44
 */
public class PostDTO {

    /**
     * 动态ID
     */
    private Long postId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户类型UserType：观众(V)、主播(A)、运管(M)
     */
    private UserType userType;

    /**
     * 用户昵称
     */
    private String nickname;

    /**
     * 用户头像
     */
    private String headPic;
    /** 头像框 */
    private String headFrame;

    /**
     * 头像真人认证
     */
    private BoolType realPerson;

    /**
     * 真人认证标识
     */
    private String authMark;

    /**
     * 性别：男(M)、女(F)
     */
    private SexType sex;

    /**
     * 年龄
     */
    private Integer age;

    /**
     * 文本内容
     */
    private String content;

    /**
     * 视频连接
     */
    private String videoUrl;

    /**
     * 视频封封面
     */
    private String videoCover;

    /**
     * 图片地址，多个以英文逗号分隔
     */
    private List<PostPicDTO> picList;

    /**
     * 分享次数
     */
    private Integer greetNum;

    /**
     * 点赞次数
     */
    private Integer praiseNum;

    /**
     * 评论数
     */
    private Integer commentNum;

    /**
     * 评论数
     */
    private Integer rewardNum;

    /**
     * 已读人数
     */
    private String readNum;

    /**
     * 热度值
     */
    private Long heatValue;

    /**
     * 审核状态
     */
    private ReviewResultType auditStatus;

    /**
     * 定位城市
     */
    private String city;

    /**
     * 定位的具体位置
     */
    private String location;

    /**
     * 发布时间
     */
    private Long postTime;

    /**
     * 动态话题信息
     */
    private GroupDTO group;

    /**
     * 是否点赞
     */
    private BoolType hasPraise;

    /**
     * 是否已打赏
     */
    private BoolType hasReward;

    /**
     * 删除权限
     */
    private BoolType deleteAuth;

    /**
     * 在线状态
     */
    private OnlineStatus onlineStatus;

    /**
     * 状态文案
     */
    private String onlineStatusText;

    /**
     * 首赞或榜一用户
     */
    private PostTopUserDTO topUser;

    /**
     * VIP类型
     */
    private VipType vipType;

    /**
     * 发布时间格式信息
     */
    private PostTimeDTO postTimeDTO;

    /**
     * ip对应城市
     */
    private String ipCity;

    /**
     * 我是否关注发布人
     */
    private BoolType follow;

    /**
     * 正常动态
     *
     * @param basic
     * @param post
     */
    public PostDTO(UUserBasic basic, UserBusiDTO busiDTO, SPost post, Long currUserId, OnlineDTO onlineInfo) {
        this.postId = post.getPostId();
        this.userId = post.getUserId();
        this.userType = basic.getUserType();
        this.nickname = basic.getNickname();
        this.headPic = basic.getHeadPic();
        if (null != busiDTO) {
            this.headFrame = busiDTO.getHeadFrame();
        }
        this.realPerson = basic.getRealPerson();
        this.sex = basic.getSex();
        this.age = null == basic.getBirthDate() ? null : BusiUtils.getAgeByDate(basic.getBirthDate());
        this.content = post.getContent();
        if (StringUtils.isNotBlank(post.getPics())) {
            this.picList = buildTagPostPic(post.getPics());
        }
        this.videoCover = post.getVideoCover();
        this.videoUrl = post.getVideoUrl();
        this.greetNum = post.getGreetNum();
        this.praiseNum = post.getPraiseNum();
        this.commentNum = post.getCommentNum();
        this.rewardNum = post.getRewardNum();
        this.readNum = BusiUtils.buildPostDisplayNum(post.getReadNum());
        this.heatValue = post.getHeatValue();
        this.auditStatus = post.getAuditStatus();
        this.city = post.getCity();
        this.location = post.getLocation();
        this.postTime = post.getPostTime().getTime();
        this.deleteAuth = BoolType.False;
        if (null != currUserId) {
            this.deleteAuth = BoolType.valueOf(post.getUserId().equals(currUserId));
        }
        if (onlineInfo != null) {
            this.onlineStatus = onlineInfo.getOnlineStatus();
            this.onlineStatusText = onlineInfo.getOnlineStatusText();
        }
        this.vipType = BusiUtils.getVipType(basic);
    }

    public PostDTO(SPost post) {
        this.postId = post.getPostId();
        this.userId = post.getUserId();
        this.content = post.getContent();
        if (StringUtils.isNotBlank(post.getPics())) {
            this.picList = buildTagPostPic(post.getPics());
        }
        this.videoCover = post.getVideoCover();
        this.videoUrl = post.getVideoUrl();
        this.postTime = post.getPostTime().getTime();
    }


    /**
     * 标签动态图片信息封装
     *
     * @param pics
     * @return
     */
    public List<PostPicDTO> buildTagPostPic(String pics) {
        List<PostPicDTO> picList = new ArrayList<>();
        for (String pic : pics.split(",")) {
            picList.add(new PostPicDTO(pic));
        }
        return picList;
    }

    public Long getPostId() {
        return postId;
    }

    public void setPostId(Long postId) {
        this.postId = postId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public UserType getUserType() {
        return userType;
    }

    public void setUserType(UserType userType) {
        this.userType = userType;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getHeadPic() {
        return headPic;
    }

    public void setHeadPic(String headPic) {
        this.headPic = headPic;
    }

    public String getHeadFrame() {
        return headFrame;
    }

    public void setHeadFrame(String headFrame) {
        this.headFrame = headFrame;
    }

    public BoolType getRealPerson() {
        return realPerson;
    }

    public void setRealPerson(BoolType realPerson) {
        this.realPerson = realPerson;
    }

    public String getAuthMark() {
        return authMark;
    }

    public void setAuthMark(String authMark) {
        this.authMark = authMark;
    }

    public SexType getSex() {
        return sex;
    }

    public void setSex(SexType sex) {
        this.sex = sex;
    }

    public Integer getAge() {
        return age;
    }

    public void setAge(Integer age) {
        this.age = age;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Integer getGreetNum() {
        return greetNum;
    }

    public void setGreetNum(Integer greetNum) {
        this.greetNum = greetNum;
    }

    public Integer getPraiseNum() {
        return praiseNum;
    }

    public void setPraiseNum(Integer praiseNum) {
        this.praiseNum = praiseNum;
    }

    public Integer getCommentNum() {
        return commentNum;
    }

    public void setCommentNum(Integer commentNum) {
        this.commentNum = commentNum;
    }

    public Integer getRewardNum() {
        return rewardNum;
    }

    public void setRewardNum(Integer rewardNum) {
        this.rewardNum = rewardNum;
    }

    public Long getHeatValue() {
        return heatValue;
    }

    public void setHeatValue(Long heatValue) {
        this.heatValue = heatValue;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public Long getPostTime() {
        return postTime;
    }

    public void setPostTime(Long postTime) {
        this.postTime = postTime;
    }

    public GroupDTO getGroup() {
        return group;
    }

    public void setGroup(GroupDTO group) {
        this.group = group;
    }

    public ReviewResultType getAuditStatus() {
        return auditStatus;
    }

    public void setAuditStatus(ReviewResultType auditStatus) {
        this.auditStatus = auditStatus;
    }

    public BoolType getHasPraise() {
        return hasPraise;
    }

    public void setHasPraise(BoolType hasPraise) {
        this.hasPraise = hasPraise;
    }

    public BoolType getHasReward() {
        return hasReward;
    }

    public void setHasReward(BoolType hasReward) {
        this.hasReward = hasReward;
    }

    public BoolType getDeleteAuth() {
        return deleteAuth;
    }

    public void setDeleteAuth(BoolType deleteAuth) {
        this.deleteAuth = deleteAuth;
    }

    public OnlineStatus getOnlineStatus() {
        return onlineStatus;
    }

    public void setOnlineStatus(OnlineStatus onlineStatus) {
        this.onlineStatus = onlineStatus;
    }

    public String getOnlineStatusText() {
        return onlineStatusText;
    }

    public void setOnlineStatusText(String onlineStatusText) {
        this.onlineStatusText = onlineStatusText;
    }

    public String getVideoUrl() {
        return videoUrl;
    }

    public void setVideoUrl(String videoUrl) {
        this.videoUrl = videoUrl;
    }

    public String getVideoCover() {
        return videoCover;
    }

    public void setVideoCover(String videoCover) {
        this.videoCover = videoCover;
    }

    public void setPicList(List<PostPicDTO> picList) {
        this.picList = picList;
    }

    public List<PostPicDTO> getPicList() {
        return picList;
    }

    public String getReadNum() {
        return readNum;
    }

    public void setReadNum(String readNum) {
        this.readNum = readNum;
    }

    public PostTopUserDTO getTopUser() {
        return topUser;
    }

    public void setTopUser(PostTopUserDTO topUser) {
        this.topUser = topUser;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public VipType getVipType() {
        return vipType;
    }

    public void setVipType(VipType vipType) {
        this.vipType = vipType;
    }

    public String getIpCity() {
        return ipCity;
    }

    public void setIpCity(String ipCity) {
        this.ipCity = ipCity;
    }

    public PostTimeDTO getPostTimeDTO() {
        return postTimeDTO;
    }

    public void setPostTimeDTO(PostTimeDTO postTimeDTO) {
        this.postTimeDTO = postTimeDTO;
    }

    public BoolType getFollow() {
        return follow;
    }

    public void setFollow(BoolType follow) {
        this.follow = follow;
    }
}
