/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.social.data.persistence;

import com.easyooo.framework.sharding.annotation.Table;
import com.tuowan.yeliao.social.data.entity.FRelationChange;
import org.springframework.stereotype.Repository;

@Repository
@Table(value = "F_RELATION_CHANGE", schema = "YL_SOCIAL")
public interface FRelationChangeMapper {
    int deleteByPrimaryKey(FRelationChange record);

    int insert(FRelationChange record);

    FRelationChange selectByPrimaryKey(FRelationChange record);

    int updateByPrimaryKeySelective(FRelationChange record);

    int updateByPrimaryKey(FRelationChange record);
}