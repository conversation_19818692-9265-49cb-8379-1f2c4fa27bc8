package com.tuowan.yeliao.social.data.manager.friend;

import com.easyooo.framework.common.enums.DatePattern;
import com.easyooo.framework.common.util.DateUtils;
import com.tuowan.yeliao.commons.config.redis.RedisKey;
import com.tuowan.yeliao.commons.config.redis.template.SocialRedisTemplate;
import com.tuowan.yeliao.commons.config.utils.CallbackAfterTransactionUtil;
import com.tuowan.yeliao.commons.core.enums.redis.SocialKeyDefine;
import com.tuowan.yeliao.commons.core.exception.BusiException;
import com.tuowan.yeliao.commons.data.entity.user.UUserBasic;
import com.tuowan.yeliao.commons.data.enums.user.SexType;
import com.tuowan.yeliao.commons.data.manager.config.NewsManager;
import com.tuowan.yeliao.commons.data.manager.user.ChatMasterManager;
import com.tuowan.yeliao.commons.data.manager.user.UserInfoManager;
import com.tuowan.yeliao.commons.data.utils.BusiUtils;
import com.tuowan.yeliao.social.data.dto.friend.ChatConsumeDTO;
import com.tuowan.yeliao.social.data.entity.FChatConsume;
import com.tuowan.yeliao.social.data.entity.FChatConsumePresent;
import com.tuowan.yeliao.social.data.entity.FChatInvite;
import com.tuowan.yeliao.social.data.enums.friend.ChatInviteType;
import com.tuowan.yeliao.social.data.persistence.FChatConsumeMapper;
import com.tuowan.yeliao.social.data.persistence.FChatConsumePresentMapper;
import com.tuowan.yeliao.social.data.persistence.FChatInviteMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 聊天数据管理封装
 *
 * <AUTHOR>
 * @date 2020/7/6 10:28
 */
@Component
public class ChatManager {

    private static final Logger LOG = LoggerFactory.getLogger(ChatManager.class);

    /**
     * 每天邀请限制最大次数
     */
    private static final Long INVITE_MAX_DAY = 200L;

    @Autowired
    private NewsManager newsManager;
    @Autowired
    private FChatConsumeMapper fChatConsumeLogMapper;
    @Autowired
    private SocialRedisTemplate socialRedisTemplate;
    @Autowired
    private FChatInviteMapper chatInviteMapper;
    @Autowired
    private UserInfoManager userInfoManager;
    @Autowired
    private ChatMasterManager chatMasterManager;
    @Autowired
    private FChatConsumePresentMapper fChatConsumePresentMapper;

    /**
     * 添加聊天消费记录
     * 备注：该方法目前 私聊、通话 在用
     * @param dto
     * @param relationId
     */
    public void addChatConsumeLog(ChatConsumeDTO dto, String relationId) {
        FChatConsume log = new FChatConsume();
        log.setTid(dto.getTid());
        log.setCreateTime(dto.getConsumeTime());
        log.setRelationId(relationId);
        log.setFromUserId(dto.getFromUserId());
        log.setToUserId(dto.getToUserId());
        log.setSourceType(dto.getSourceType());
        log.setContentType(dto.getContentType());
        log.setBillUserId(dto.getFeeUserId());
        log.setProdId(dto.getProdId());
        log.setProdCnt(dto.getProdCnt());
        log.setTotalBeans(dto.getTotalBeans().intValue());
        log.setPlatformBeans(dto.getPlatformBeans().intValue());
        log.setTotalSilver(0);  // 这里我们写死 因为 私聊、通话 都不能用银币
        fChatConsumeLogMapper.insert(log);

        // 增加提成明细记录
        FChatConsumePresent present = new FChatConsumePresent();
        present.setTid(dto.getTid());
        present.setCreateTime(dto.getConsumeTime());
        // 直接用户提成
        present.setPresentUser(dto.getTargetPresentUserId());
        present.setPresentCash(dto.getTargetPresentCash());
        present.setPlatformCash(dto.getTargetPlatformCash());
        // 邀请人提成
        present.setInvitorUser(dto.getInvitorUserId());
        present.setInvitorPresentCash(dto.getInvitorPresentCash());
        present.setInvitorPlatformCash(dto.getInvitorPlatformCash());

        fChatConsumePresentMapper.insert(present);
    }

    /**
     * 添加聊天消费记录《适配礼物》
     *
     * @param dto
     */
    public void addChatConsumeLogForGift(ChatConsumeDTO dto, boolean fromBag) {
        FChatConsume log = new FChatConsume();
        log.setTid(dto.getTid());
        log.setCreateTime(dto.getConsumeTime());
        log.setRelationId(BusiUtils.generateRelationId(dto.getFromUserId(), dto.getToUserId()));
        log.setFromUserId(dto.getFromUserId());
        log.setToUserId(dto.getToUserId());
        log.setSourceType(dto.getSourceType());
        log.setContentType(dto.getContentType());
        log.setBillUserId(dto.getFeeUserId());
        log.setProdId(dto.getProdId());
        log.setProdCnt(dto.getProdCnt());
        log.setTotalBeans(fromBag ? 0 : dto.getTotalBeans().intValue());
        log.setPlatformBeans(fromBag ? 0 : dto.getPlatformBeans().intValue());
        log.setTotalSilver(fromBag ? 0 : dto.getTotalSilver().intValue());
        fChatConsumeLogMapper.insert(log);

        // 如果消耗的总金币为0 我们不记录提成明细 （适配银币礼物）
        if(dto.getTotalBeans() <= 0){
            return;
        }

        // 增加提成明细记录
        FChatConsumePresent present = new FChatConsumePresent();
        present.setTid(dto.getTid());
        present.setCreateTime(dto.getConsumeTime());
        // 直接用户提成
        present.setPresentUser(dto.getTargetPresentUserId());
        present.setPresentCash(dto.getTargetPresentCash());
        present.setPlatformCash(dto.getTargetPlatformCash());
        // 邀请人提成
        present.setInvitorUser(dto.getInvitorUserId());
        present.setInvitorPresentCash(dto.getInvitorPresentCash());
        present.setInvitorPlatformCash(dto.getInvitorPlatformCash());

        fChatConsumePresentMapper.insert(present);
    }

    /**
     * 邀请记录处理
     *
     * @param userId   邀请人
     * @param friendId 被邀请人
     */
    public void saveInviteInfo(Date now, Long userId, SexType sex, Long friendId, ChatInviteType inviteType) {
        FChatInvite chatInvite = new FChatInvite(userId, friendId, inviteType);
        chatInviteMapper.insert(chatInvite);
        CallbackAfterTransactionUtil.send(() -> {
            if(SexType.Female == sex && (ChatInviteType.VideoCall == inviteType || ChatInviteType.VoiceCall == inviteType)){
                String dateStr = DateUtils.toString(now, DatePattern.YMD2);
                String hourStr = DateUtils.toString(now, DatePattern.YMD_H2);
                // 邀请总次数限制
                socialRedisTemplate.incr(buildFemaleInviteCallDayTimesKey(dateStr, userId));
                // 30分钟次数限制
                socialRedisTemplate.incr(buildFemaleInviteCallHourTimesKey(hourStr, userId));
            }
            // 邀请频率限制 备注：男女邀请都要做个间隔判断 避免一个用户故意打扰另一个
            socialRedisTemplate.set(buildUserInviteCallRecordSign(userId, friendId), String.valueOf(now.getTime()));
        });
    }


    /**
     * 邀请限制过滤
     *
     * @param userBasic   邀请人
     * @param friendId 被邀请人
     */
    public void checkInviteLimit(Date now, UUserBasic userBasic, Long friendId, ChatInviteType inviteType) {
        // 判断上次邀请标识还是是否存在
        if(socialRedisTemplate.exists(buildUserInviteCallRecordSign(userBasic.getUserId(), friendId))){
            throw new BusiException("您不久前刚邀请过Ta哦，试试邀请别人吧~");
        }
        // 女用户次数判断
        if(SexType.Female == userBasic.getSex() && (ChatInviteType.VideoCall == inviteType || ChatInviteType.VoiceCall == inviteType)){
            String dateStr = DateUtils.toString(now, DatePattern.YMD2);
            String hourStr = DateUtils.toString(now, DatePattern.YMD_H2);
            Integer finishTimes01 = socialRedisTemplate.getInt(buildFemaleInviteCallDayTimesKey(dateStr, userBasic.getUserId()), true);
            if(finishTimes01 >= 200){
                throw new BusiException("今日邀请次数已达上限，明日再来吧~");
            }
            Integer finishTimes02 = socialRedisTemplate.getInt(buildFemaleInviteCallHourTimesKey(hourStr, userBasic.getUserId()), true);
            if(finishTimes02 >= 20){
                throw new BusiException("每小时邀请次数有限制哦，等会再来吧~");
            }
        }
    }

    /**
     * 记录今天搭讪用户
     * @param userId
     * @param friendId
     */
    public void saveChatUpUser(Date date, Long userId, SexType sexType, Long friendId) {
        String dateStr = DateUtils.toString(date, DatePattern.YMD2);
        RedisKey redisKey = buildChatUpUserKey(dateStr, userId);
        socialRedisTemplate.sadd(redisKey, friendId.toString());
        socialRedisTemplate.expire(redisKey);
        // 如果是女用户搭讪 还需要记录搭讪次数
        if(SexType.Female == sexType){
            socialRedisTemplate.incr(buildFemaleChatUpDayTimesKey(dateStr, userId));
            socialRedisTemplate.incr(buildFemaleChatUpMinTimesKey(DateUtils.toString(date, DatePattern.YMD_H2), userId));
        }
    }



    /**
     * 修改女用户今日已经搭讪次数
     */
    public void updateFemaleChatUpTimesForDay(Date now, Long femaleUserId, Long num){
        String dateStr = DateUtils.toString(now, DatePattern.YMD2);
        socialRedisTemplate.incrby(buildFemaleChatUpDayTimesKey(dateStr, femaleUserId), num);
    }

    /**
     * 获取女用户今日已经搭讪次数
     */
    public long getFemaleChatUpTimesForDay(Date now, Long femaleUserId){
        return socialRedisTemplate.getLong(buildFemaleChatUpDayTimesKey(DateUtils.toString(now, DatePattern.YMD2), femaleUserId));
    }

    /**
     * 获取女用户每分钟搭讪次数
     */
    public long getFemaleChatUpTimesForMin(Date now, Long femaleUserId){
        return socialRedisTemplate.getLong(buildFemaleChatUpMinTimesKey(DateUtils.toString(now, DatePattern.YMD_H2), femaleUserId));
    }

    /**
     * 今天是否搭讪
     * 一天仅能搭讪一次
     * @param userId
     * @param friendId
     */
    public boolean chatUpDay(Date now, Long userId,Long friendId) {
        String dateStr = DateUtils.toString(now, DatePattern.YMD2);
        RedisKey redisKey = buildChatUpUserKey(dateStr, userId);
        return socialRedisTemplate.sismember(redisKey, String.valueOf(friendId));
    }

    /**
     * 搭讪用户缓存Key
     *
     * @param userId
     * @return
     */
    private RedisKey buildChatUpUserKey(String dateStr, Long userId) {
        return RedisKey.create(SocialKeyDefine.ChatUpUser, dateStr, userId);
    }

    /**
     * 女用户搭讪次数缓存
     */
    private RedisKey buildFemaleChatUpDayTimesKey(String dateStr, Long femaleUserId){
        return RedisKey.create(SocialKeyDefine.FemaleChatUpDayTimes, dateStr, femaleUserId);
    }

    /**
     * 女用户每分钟搭讪次数
     */
    private RedisKey buildFemaleChatUpMinTimesKey(String minutesStr, Long femaleUserId){
        return RedisKey.create(SocialKeyDefine.FemaleChatUpMinTimes, minutesStr, femaleUserId);
    }

    /**
     * 女用户邀请通话次数缓存
     */
    private RedisKey buildFemaleInviteCallDayTimesKey(String dateStr, Long femaleUserId){
        return RedisKey.create(SocialKeyDefine.FemaleInviteCallDayTimes, dateStr, femaleUserId);
    }

    /**
     * 女用户30分钟邀请通话缓存
     */
    private RedisKey buildFemaleInviteCallHourTimesKey(String hourStr, Long femaleUserId){
        return RedisKey.create(SocialKeyDefine.FemaleInviteCallHourTimes, hourStr, femaleUserId);
    }

    /**
     * 用户邀请记录标识
     */
    private RedisKey buildUserInviteCallRecordSign(Long userId, Long friendId){
        return RedisKey.create(SocialKeyDefine.UserInviteCallRecord, userId, friendId);
    }
}
