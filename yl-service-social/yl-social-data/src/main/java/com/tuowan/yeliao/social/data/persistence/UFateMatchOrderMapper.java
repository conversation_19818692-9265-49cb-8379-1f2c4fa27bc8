/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.social.data.persistence;

import com.easyooo.framework.sharding.annotation.Table;
import com.tuowan.yeliao.social.data.dto.friend.chat.RedPacketReceiveRateDTO;
import com.tuowan.yeliao.social.data.entity.UFateMatchOrder;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
@Table(value = "U_FATE_MATCH_ORDER", schema = "YL_SOCIAL")
public interface UFateMatchOrderMapper {
    int deleteByPrimaryKey(UFateMatchOrder record);

    int insert(UFateMatchOrder record);

    UFateMatchOrder selectByPrimaryKey(UFateMatchOrder record);

    int updateByPrimaryKeySelective(UFateMatchOrder record);

    int updateByPrimaryKey(UFateMatchOrder record);

    List<RedPacketReceiveRateDTO> selectRecentReceiveInfo(@Param("userId") Long userId, @Param("startDate") Date startDate, @Param("endDate") Date endDate);
}