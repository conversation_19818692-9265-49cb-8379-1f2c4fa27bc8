/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.social.data.persistence;

import com.easyooo.framework.cache.annotations.GroupStrategy;
import com.easyooo.framework.sharding.annotation.Table;
import com.tuowan.yeliao.social.data.entity.UUserGiftStat;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@Table(value = "U_USER_GIFT_STAT", schema = "YL_SOCIAL")
public interface UUserGiftStatMapper {
    int deleteByPrimaryKey(UUserGiftStat record);

    int insert(UUserGiftStat record);

    UUserGiftStat selectByPrimaryKey(UUserGiftStat record);

    int updateByPrimaryKeySelective(UUserGiftStat record);

    int updateByPrimaryKey(UUserGiftStat record);

    @GroupStrategy
    List<UUserGiftStat> selectByUserId(UUserGiftStat uUserGiftStat);

    int init(UUserGiftStat uUserGiftStat);
}