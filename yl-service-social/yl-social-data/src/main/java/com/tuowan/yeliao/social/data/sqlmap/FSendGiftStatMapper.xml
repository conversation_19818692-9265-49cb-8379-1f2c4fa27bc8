<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tuowan.yeliao.social.data.persistence.FSendGiftStatMapper">
  <sql id="Base_Column_List">
    sign, user_id, friend_id, stat_money, is_limit, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="FSendGiftLimitStat" resultType="FSendGiftLimitStat">
    select 
    <include refid="Base_Column_List" />
    from f_send_gift_stat
    where sign = #{sign}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="FSendGiftLimitStat">
    delete from f_send_gift_stat
    where sign = #{sign}
  </delete>
  <insert id="insert" parameterType="FSendGiftLimitStat">
    insert into f_send_gift_stat (sign, user_id, friend_id, stat_money, is_limit, create_time
      )
    values (#{sign}, #{userId}, #{friendId}, #{statMoney}, #{isLimit}, #{createTime}
      )
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="FSendGiftLimitStat">
    update f_send_gift_stat
    <set>
      <if test="userId != null">
        user_id = #{userId},
      </if>
      <if test="friendId != null">
        friend_id = #{friendId},
      </if>
      <if test="statMoney != null">
        stat_money = stat_money + #{statMoney},
      </if>
      <if test="isLimit != null">
        is_limit = #{isLimit},
      </if>
      <if test="createTime != null">
        create_time = #{createTime},
      </if>
    </set>
    where sign = #{sign}
  </update>
  <update id="updateByPrimaryKey" parameterType="FSendGiftLimitStat">
    update f_send_gift_stat
    set user_id = #{userId},
      friend_id = #{friendId},
      stat_money = #{statMoney},
      is_limit = #{isLimit},
      create_time = #{createTime}
    where sign = #{sign}
  </update>
</mapper>