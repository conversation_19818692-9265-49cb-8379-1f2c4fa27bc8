/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.social.data.persistence.post;

import com.easyooo.framework.sharding.annotation.Table;
import com.tuowan.yeliao.social.data.entity.post.SCommentPraise;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@Table(value = "S_COMMENT_PRAISE", schema = "YL_SOCIAL")
public interface SCommentPraiseMapper {
    int deleteByPrimaryKey(SCommentPraise record);

    int insert(SCommentPraise record);

    SCommentPraise selectByPrimaryKey(SCommentPraise record);

    int updateByPrimaryKeySelective(SCommentPraise record);

    int updateByPrimaryKey(SCommentPraise record);

    List<SCommentPraise> selectByUserId(SCommentPraise record);
}