<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tuowan.yeliao.social.data.persistence.post.SPostCommentMapper">
    <sql id="Base_Column_List">
        comment_id, post_id, parent_id, user_id, content, reply_user_id, reply_num, praise_num, status,
        create_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="SPostComment" resultType="SPostComment">
        select
        <include refid="Base_Column_List"/>
        from s_post_comment
        where comment_id = #{commentId}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="SPostComment">
        delete from s_post_comment
        where comment_id = #{commentId}
    </delete>
    <insert id="insert" parameterType="SPostComment" useGeneratedKeys="true" keyProperty="commentId">
        insert into s_post_comment (comment_id, post_id, parent_id, user_id, content, reply_user_id,
        reply_num, praise_num, status, create_time)
        values (#{commentId}, #{postId}, #{parentId}, #{userId}, #{content}, #{replyUserId},
        #{replyNum}, #{praiseNum}, #{status}, #{createTime})
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="SPostComment">
        update s_post_comment
        <set>
            <if test="postId != null">
                post_id = #{postId},
            </if>
            <if test="parentId != null">
                parent_id = #{parentId},
            </if>
            <if test="userId != null">
                user_id = #{userId},
            </if>
            <if test="content != null">
                content = #{content},
            </if>
            <if test="replyUserId != null">
                reply_user_id = #{replyUserId},
            </if>
            <if test="replyNum != null">
                reply_num = if(reply_num + #{replyNum} &lt;= 0, 0, reply_num + #{replyNum}),
            </if>
            <if test="praiseNum != null">
                praise_num = if(praise_num + #{praiseNum} &lt;= 0, 0, praise_num + #{praiseNum}),
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
        </set>
        where comment_id = #{commentId}
    </update>
    <update id="updateByPrimaryKey" parameterType="SPostComment">
        update s_post_comment
        set post_id = #{postId},
        parent_id = #{parentId},
        user_id = #{userId},
        content = #{content},
        reply_user_id = #{replyUserId},
        reply_num = #{replyNum},
        praise_num = #{praiseNum},
        status = #{status},
        create_time = #{createTime}
        where comment_id = #{commentId}
    </update>
    <select id="selectByPostIdAndParentId" parameterType="map" resultType="SPostComment">
        select
        <include refid="Base_Column_List"/>
        from s_post_comment
        where post_id = #{postId} and parent_id = #{parentId}
        order by comment_id desc
        limit #{offset}, #{limit}
    </select>
</mapper>