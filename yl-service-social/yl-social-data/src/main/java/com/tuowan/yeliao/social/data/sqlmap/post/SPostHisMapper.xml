<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tuowan.yeliao.social.data.persistence.post.SPostHisMapper">
  <sql id="Base_Column_List">
    post_id, group_id, user_id, content, pics, anonymous, share_num, praise_num, comment_num, 
    city, lat, lng, post_time, audit_user_id, audit_time, audit_status, audit_reason, 
    his_reason_type, his_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="SPostHis" resultType="SPostHis">
    select 
    <include refid="Base_Column_List" />
    from s_post_his
    where post_id = #{postId}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="SPostHis">
    delete from s_post_his
    where post_id = #{postId}
  </delete>
  <insert id="insert" parameterType="SPostHis">
    insert into s_post_his (post_id, group_id, user_id, content, pics, anonymous, share_num, 
      praise_num, comment_num, city, lat, lng, post_time, audit_user_id, 
      audit_time, audit_status, audit_reason, his_reason_type, his_time)
    values (#{postId}, #{groupId}, #{userId}, #{content}, #{pics}, #{anonymous}, #{shareNum}, 
      #{praiseNum}, #{commentNum}, #{city}, #{lat}, #{lng}, #{postTime}, #{auditUserId}, 
      #{auditTime}, #{auditStatus}, #{auditReason}, #{hisReasonType}, #{hisTime})
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="SPostHis">
    update s_post_his
    <set>
      <if test="groupId != null">
        group_id = #{groupId},
      </if>
      <if test="userId != null">
        user_id = #{userId},
      </if>
      <if test="content != null">
        content = #{content},
      </if>
      <if test="pics != null">
        pics = #{pics},
      </if>
      <if test="anonymous != null">
        anonymous = #{anonymous},
      </if>
      <if test="shareNum != null">
        share_num = #{shareNum},
      </if>
      <if test="praiseNum != null">
        praise_num = #{praiseNum},
      </if>
      <if test="commentNum != null">
        comment_num = #{commentNum},
      </if>
      <if test="city != null">
        city = #{city},
      </if>
      <if test="lat != null">
        lat = #{lat},
      </if>
      <if test="lng != null">
        lng = #{lng},
      </if>
      <if test="postTime != null">
        post_time = #{postTime},
      </if>
      <if test="auditUserId != null">
        audit_user_id = #{auditUserId},
      </if>
      <if test="auditTime != null">
        audit_time = #{auditTime},
      </if>
      <if test="auditStatus != null">
        audit_status = #{auditStatus},
      </if>
      <if test="auditReason != null">
        audit_reason = #{auditReason},
      </if>
      <if test="hisReasonType != null">
        his_reason_type = #{hisReasonType},
      </if>
      <if test="hisTime != null">
        his_time = #{hisTime},
      </if>
    </set>
    where post_id = #{postId}
  </update>
  <update id="updateByPrimaryKey" parameterType="SPostHis">
    update s_post_his
    set group_id = #{groupId},
      user_id = #{userId},
      content = #{content},
      pics = #{pics},
      anonymous = #{anonymous},
      share_num = #{shareNum},
      praise_num = #{praiseNum},
      comment_num = #{commentNum},
      city = #{city},
      lat = #{lat},
      lng = #{lng},
      post_time = #{postTime},
      audit_user_id = #{auditUserId},
      audit_time = #{auditTime},
      audit_status = #{auditStatus},
      audit_reason = #{auditReason},
      his_reason_type = #{hisReasonType},
      his_time = #{hisTime}
    where post_id = #{postId}
  </update>
</mapper>