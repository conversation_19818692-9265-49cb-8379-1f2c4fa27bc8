package com.tuowan.yeliao.social.data.dto.friend;


import com.easyooo.framework.common.util.MsgUtils;
import com.tuowan.yeliao.commons.core.constant.IconConstant;
import com.tuowan.yeliao.social.data.enums.friend.IntimatePrivilege;
import com.tuowan.yeliao.social.data.enums.friend.IntimateStatus;

/**
 * 亲密度等级信息DTO
 *
 * <AUTHOR>
 * @date 2021/10/18 19:40
 */
public class IntimateLevelDTO {

    /** 亲密等级 */
    private Integer intimateLevel;
    /** 前置图标 */
    private String preIcon;
    /** 图标icon */
    private String icon;
    /** 图标参数 */
    private String iconAttr;
    /** 动作文本 */
    private String action;
    /** 解锁特权文本 */
    private String privilege;
    /** 后缀文本 */
    private String suffix;
    /** 等级提示图标 */
    private String levelTipIcon;
    /** 状态 */
    private IntimateStatus status;

    public IntimateLevelDTO(IntimatePrivilege privilege, boolean select, boolean nextSelect, IntimateStatus status) {
        this.intimateLevel = privilege.getLevel();
        String iconFormat = IntimateStatus.DoComplete != status ? IconConstant.INTIMATE_LIST_PRIVILEGE_ICON : IconConstant.INTIMATE_LIST_PRIVILEGE_ICON_W;
        this.icon = MsgUtils.format(iconFormat, privilege.getLevel());
        if (select) {
            this.levelTipIcon = IconConstant.INTIMATE_LIST_LEVEL_TIP_ICON_CURRENT;
        }
        if (nextSelect){
            this.levelTipIcon = IconConstant.INTIMATE_LIST_LEVEL_TIP_ICON_NEXT;
        }
        this.action = privilege.getAction();
        this.privilege = privilege.getName();
        this.suffix = privilege.getSuffix();
        this.iconAttr = privilege.getIconAttr();
        this.status = status;
        this.preIcon = MsgUtils.format(IconConstant.INTIMATE_LIST_PRE_ICON, status.getPreIcon());
    }

    public Integer getIntimateLevel() {
        return intimateLevel;
    }

    public void setIntimateLevel(Integer intimateLevel) {
        this.intimateLevel = intimateLevel;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public String getPrivilege() {
        return privilege;
    }

    public void setPrivilege(String privilege) {
        this.privilege = privilege;
    }

    public String getSuffix() {
        return suffix;
    }

    public void setSuffix(String suffix) {
        this.suffix = suffix;
    }

    public IntimateStatus getStatus() {
        return status;
    }

    public void setStatus(IntimateStatus status) {
        this.status = status;
    }

    public String getPreIcon() {
        return preIcon;
    }

    public void setPreIcon(String preIcon) {
        this.preIcon = preIcon;
    }

    public String getLevelTipIcon() {
        return levelTipIcon;
    }

    public void setLevelTipIcon(String levelTipIcon) {
        this.levelTipIcon = levelTipIcon;
    }

    public String getIconAttr() {
        return iconAttr;
    }

    public void setIconAttr(String iconAttr) {
        this.iconAttr = iconAttr;
    }
}
