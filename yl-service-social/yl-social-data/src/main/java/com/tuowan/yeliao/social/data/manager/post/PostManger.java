package com.tuowan.yeliao.social.data.manager.post;

import com.easyooo.framework.common.util.DateUtils;
import com.easyooo.framework.common.util.ListUtils;
import com.easyooo.framework.common.util.StringUtils;
import com.tuowan.yeliao.commons.config.redis.RedisKey;
import com.tuowan.yeliao.commons.config.redis.template.SocialRedisTemplate;
import com.tuowan.yeliao.commons.config.utils.CallbackAfterTransactionUtil;
import com.tuowan.yeliao.commons.core.constant.GlobalConstant;
import com.tuowan.yeliao.commons.core.enums.general.BoolType;
import com.tuowan.yeliao.commons.core.enums.redis.SocialKeyDefine;
import com.tuowan.yeliao.commons.core.exception.BusiException;
import com.tuowan.yeliao.commons.data.entity.user.UUserBasic;
import com.tuowan.yeliao.commons.data.enums.general.ReviewResultType;
import com.tuowan.yeliao.commons.data.enums.general.Status;
import com.tuowan.yeliao.commons.data.enums.user.SexType;
import com.tuowan.yeliao.commons.data.manager.commons.BusiManager;
import com.tuowan.yeliao.commons.data.manager.user.UserInfoManager;
import com.tuowan.yeliao.commons.data.persistence.user.UUserMoreMapper;
import com.tuowan.yeliao.commons.data.utils.BusiUtils;
import com.tuowan.yeliao.social.data.entity.post.*;
import com.tuowan.yeliao.social.data.enums.post.FriendPostOptType;
import com.tuowan.yeliao.social.data.enums.post.GroupType;
import com.tuowan.yeliao.social.data.enums.post.PostType;
import com.tuowan.yeliao.social.data.persistence.post.*;
import com.tuowan.yeliao.social.data.persistence.query.CommQueryMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Component;
import redis.clients.jedis.resps.Tuple;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 动态数据服务类
 *
 * <AUTHOR>
 * @date 2020/11/19 17:01
 */
@Component
public class PostManger {

    @Autowired
    private SPostMapper sPostMapper;
    @Autowired
    private BusiManager busiManager;
    @Autowired
    private SGroupMapper groupMapper;
    @Autowired
    private SPostPraiseMapper postPraiseMapper;
    @Autowired
    private SocialRedisTemplate socialRedisTemplate;
    @Autowired
    private UserInfoManager userInfoManager;
    @Autowired
    private UUserMoreMapper userMoreMapper;
    @Autowired
    private SPostHeatChangeLogMapper sPostHeatChangeLogMapper;
    @Autowired
    private SPostHisMapper sPostHisMapper;
    @Autowired
    private SPostRewardMapper sPostRewardMapper;
    @Autowired
    private SPostVoteMapper postVoteMapper;
    @Autowired
    private SPostVoteUserMapper postVoteUserMapper;
    @Autowired
    private SPostHotMapper postHotMapper;
    @Autowired
    private SPostCollectMapper postCollectMapper;
    @Autowired
    private SPostShareMapper postShareMapper;
    @Autowired
    private CommQueryMapper commQueryMapper;

    /**
     * 获取用户动态点赞数据
     */
    public Set<String> getUserPostPraisePostIds(Long userId){
        // 1、先从缓存中获取
        RedisKey redisKey = buildPostUserPraisePostIds(userId);
        Set<String> postIds = socialRedisTemplate.smembers(redisKey);
        if(ListUtils.isNotEmpty(postIds)){
            postIds.remove(GlobalConstant.EMPTY_VALUE);
            return postIds;
        }
        // 2、从数据库中获取
        List<SPostPraise> praises = postPraiseMapper.selectByUserId(new SPostPraise(userId));
        postIds = praises.stream().map(SPostPraise::getPostId).map(String::valueOf).collect(Collectors.toSet());
        if(ListUtils.isEmpty(praises)){
            postIds.add(GlobalConstant.EMPTY_VALUE);
        }
        // 3、插入进缓存
        socialRedisTemplate.sadd(redisKey, postIds.toArray(new String[0]));
        socialRedisTemplate.expire(redisKey);
        postIds.remove(GlobalConstant.EMPTY_VALUE);
        return postIds;
    }

    /**
     * 获取用户动态投票数据
     */
    public Set<String> getUserPostVoteLogIds(Long userId){
        // 1、先从缓存中获取
        RedisKey redisKey = buildPostUserVoteLogIds(userId);
        Set<String> logIds = socialRedisTemplate.smembers(redisKey);
        if(ListUtils.isNotEmpty(logIds)){
            logIds.remove(GlobalConstant.EMPTY_VALUE);
            return logIds;
        }
        // 2、从数据库中获取
        List<SPostVoteUser> voteUsers = postVoteUserMapper.selectByUserId(userId);
        logIds = voteUsers.stream().map(SPostVoteUser::getVoteId).map(String::valueOf).collect(Collectors.toSet());
        if(ListUtils.isEmpty(logIds)){
            logIds.add(GlobalConstant.EMPTY_VALUE);
        }
        // 3、插入进缓存
        socialRedisTemplate.sadd(redisKey, logIds.toArray(new String[0]));
        socialRedisTemplate.expire(redisKey);
        logIds.remove(GlobalConstant.EMPTY_VALUE);
        return logIds;
    }

    /**
     * 获取用户动态帮上热门数据
     */
    public List<String> getUserPostUpHotPostIds(Long userId){
        // 1、先从缓存中获取
        RedisKey redisKey = buildPostUserUpHotPostIds(userId);
        List<String> postIds = socialRedisTemplate.zrangeByScore(redisKey, System.currentTimeMillis(), Double.MAX_VALUE);
        if(ListUtils.isNotEmpty(postIds)){
            postIds.remove(GlobalConstant.EMPTY_VALUE);
            return postIds;
        }
        // 2、从数据库中获取
        Map<String, Double> dataMap = new HashMap<>();
        List<SPostHot> postHots = postHotMapper.listUserUpHotDatas(userId, GlobalConstant.POST_UP_HOT_MAINTAIN_HOUR);
        if(ListUtils.isEmpty(postHots)){
            dataMap.put(GlobalConstant.EMPTY_VALUE, (double)DateUtils.plusHours(new Date(), GlobalConstant.POST_UP_HOT_MAINTAIN_HOUR).getTime());
        }else{
            postHots.forEach(item -> {
                dataMap.put(item.getPostId().toString(), (double)DateUtils.plusHours(item.getCreateTime(), GlobalConstant.POST_UP_HOT_MAINTAIN_HOUR).getTime());
            });
        }
        // 3、插入进缓存
        socialRedisTemplate.zadd(redisKey, dataMap);
        socialRedisTemplate.expire(redisKey);
        return dataMap.keySet().stream().filter(f -> !GlobalConstant.EMPTY_VALUE.equals(f)).collect(Collectors.toList());
    }

    /**
     * 动态投票赞同人数统计
     */
    public void countPostVoteAgreeNum(Long logId, int num){
        postVoteMapper.countVoteAgree(logId, num);
    }

    /**
     * 删除用户投票信息
     */
    public void delPostVoteUser(SPostVoteUser voteUser){
        postVoteUserMapper.deleteByPrimaryKey(voteUser);
    }

    /**
     * 记录用户投票信息
     */
    public void savePostVoteUser(SPostVoteUser voteUser){
        postVoteUserMapper.insert(voteUser);
        // 删除用户动态投票logId记录缓存
        CallbackAfterTransactionUtil.send(() -> {
            socialRedisTemplate.del(buildPostUserVoteLogIds(voteUser.getUserId()));
        });
    }

    /**
     * 获取用户投票信息
     */
    public SPostVoteUser getPostVoteUser(Long userId, Long postId){
        return postVoteUserMapper.selectByPrimaryKey(new SPostVoteUser(userId, postId));
    }

    /**
     * 获取动态投票信息
     * 根据动态Id
     */
    public List<SPostVote> getPostVotePostId(Long postId){
        return postVoteMapper.getPostVoteByPostId(postId);
    }

    /**
     * 获取动态投票信息
     * 根据投票标识
     */
    public SPostVote getPostVoteLogId(Long logId){
        return postVoteMapper.selectByPrimaryKey(new SPostVote(logId));
    }

    /**
     * 记录动态分享
     */
    public void savePostShare(SPostShare share){
        postShareMapper.insert(share);
    }

    /**
     * 根据 postId 和 userId
     * 获取收藏记录
     */
    public SPostCollect getPostCollectByPu(Long postId, Long userId){
        SPostCollect collect = new SPostCollect();
        collect.setPostId(postId);
        collect.setUserId(userId);
        return postCollectMapper.selectByPu(collect);
    }

    /**
     * 收藏动态
     */
    public void savePostCollect(SPostCollect collect){
        postCollectMapper.insert(collect);
    }

    /**
     * 取消收藏
     * 备注：直接删除
     */
    public void cancelPostCollect(SPostCollect collect){
        postCollectMapper.deleteByPrimaryKey(collect);
    }

    /**
     * 保存动态上热门数据
     */
    public void savePostHot(SPostHot hot, UUserBasic postUserBasic){
        postHotMapper.insert(hot);
        CallbackAfterTransactionUtil.send(() -> {
            // 保存进热门动态
            socialRedisTemplate.zadd(buildHotPost(postUserBasic.getSex()), (double)DateUtils.plusHours(new Date(), GlobalConstant.POST_UP_HOT_MAINTAIN_HOUR).getTime(), hot.getPostId().toString());
            // 删除用户动态上热门缓存
            socialRedisTemplate.del(buildPostUserUpHotPostIds(hot.getFriendId()));
        });
    }

    /**
     * 获取三个上热门动态
     */
    public List<String> queryUserHotPost(Long userId, SexType sexType){
        // 先从自己缓存里面获取
        List<String> resultList = getUserPostUpHotPostIds(userId);
        if(resultList.size() < 3){
            // 从公共数据中获取
            List<String> commonHotPost = socialRedisTemplate.zrangeByIndex(buildHotPost(SexType.getTaSexType(sexType)), 0L, -1L);
            commonHotPost.removeAll(resultList);
            Collections.shuffle(commonHotPost);
            resultList.addAll(ListUtils.subList(commonHotPost, 0, 3 - resultList.size()));
        }else{
            Collections.shuffle(resultList);
            resultList = ListUtils.subList(resultList, 0, 3);
        }
        return resultList;
    }

    /**
     * 获取过期热门动态
     */
    public void queryExpireHotPost(){
        // 清理男
        socialRedisTemplate.zremrangeByScore(buildHotPost(SexType.Male), 0D, (double)System.currentTimeMillis());
        // 清理女
        socialRedisTemplate.zremrangeByScore(buildHotPost(SexType.Female), 0D, (double)System.currentTimeMillis());
    }

    /**
     * 获取动态置顶 待处理数据
     */
    public List<Tuple> queryPostTopUpWaitDeal(BoolType type){
        return socialRedisTemplate.zrangeByScoreWithScores(buildTopUpPost(type), 0D, System.currentTimeMillis());
    }

    /**
     * 从动态置顶处理队列中移除
     */
    public boolean removeFromPostTopUpQueue(String member, BoolType type){
        return socialRedisTemplate.zrem(buildTopUpPost(type), member) > 0;
    }

    /**
     * 加入动态置顶处理队列
     */
    public void addPostTopUpQueue(BoolType type, String member, Date dealTime){
        socialRedisTemplate.zadd(buildTopUpPost(type), (double)dealTime.getTime(), member);
    }

    /**
     * 保存动态投票数据
     */
    public void savePostVote(List<SPostVote> list){
        postVoteMapper.batchInsert(list);
    }

    /**
     * 更新动态
     */
    public void updatePost(SPost record) {
        sPostMapper.updateByPrimaryKeySelective(record);
    }

    public SGroup getGroup(Long groupId) {
        return groupMapper.selectByPrimaryKey(new SGroup(groupId));
    }

    /**
     * 更新话题
     */
    public void updateGroup(SGroup group) {
        groupMapper.updateByPrimaryKeySelective(group);
    }

    /**
     * 保存话题
     */
    public void saveGroup(SGroup group){
        groupMapper.insert(group);
    }

    /**
     * 删除动态
     */
    public void deletePost(SPost post) {
        sPostMapper.deleteByPrimaryKey(post);
    }

    /**
     * 插入动态历史
     */
    public void savePostHis(SPostHis record) {
        sPostHisMapper.insert(record);
    }

    /**
     * 移除图片URL中的参数
     *
     * @param str
     * @return
     */
    private String removeParameter(String str) {
        int charIndex = str.indexOf("?");
        if (charIndex != -1) {
            str = str.substring(0, charIndex);
        }
        return str;
    }

    /**
     * 获取类型话题
     *
     * @return
     */
    public List<SGroup> getGroupList(GroupType groupType) {
        List<SGroup> all = groupMapper.selectByGroupType(new SGroup(groupType));
        all = all.stream().sorted(Comparator.comparing(SGroup::getSeq).reversed().thenComparing(SGroup::getCreateTime).reversed()).collect(Collectors.toList());
        return all;
    }

    /**
     * 获取话题分页数据
     *
     * @return
     */
    public List<SGroup> getGroupPage(Integer offset, Integer limit, String likeContent) {
        return groupMapper.selectGroupPage(likeContent, offset, limit);
    }

    /**
     * 动态ID获取动态
     *
     * @param postId
     * @return
     */
    public SPost getPostById(Long postId) {
        return sPostMapper.selectByPrimaryKey(new SPost(postId));
    }

    /**
     * 获取所有审核通过的动态的postId
     */
    public List<Long> getAllPassPostIds(){
        return sPostMapper.queryAllPassPosts();
    }

    /**
     * 获取某个有效话题
     *
     * @param groupId
     * @return
     */
    public SGroup getValidGroup(Long groupId) {
        if (null == groupId) {
            return null;
        }
        SGroup group = groupMapper.selectByPrimaryKey(new SGroup(groupId));
        if (null == group || Status.Disable == group.getStatus()) {
            return null;
        }
        return group;
    }

    /**
     * 发布动态
     *
     * @param post
     */
    public void savePost(SPost post) {
        sPostMapper.insert(post);
    }

    /**
     * 分页查询用户收藏的动态
     */
    public List<SPost> getCollectPost(Long userId, Integer offset, Integer limit, PostType postType) {
        Map<String, Object> params = new HashMap<>();
        params.put("userId", userId);
        if (null != postType) {
            params.put("postType", postType.getId());
        }
        params.put("offset", offset);
        params.put("limit", limit);
        return commQueryMapper.getCollectPost(params);
    }

    /**
     * 分页查询用户参与的动态
     */
    public List<SPost> getInvolvedPost(Long userId, Integer offset, Integer limit, PostType postType) {
        Map<String, Object> params = new HashMap<>();
        params.put("userId", userId);
        if (null != postType) {
            params.put("postType", postType.getId());
        }
        params.put("offset", offset);
        params.put("limit", limit);
        return commQueryMapper.getCollectPost(params);
    }

    /**
     * 分页查询用户动态
     *
     * @param userId
     * @param offset
     * @param limit
     * @return
     */
    public List<SPost> getUserPost(Long userId, Integer offset, Integer limit, PostType postType) {
        Map<String, Object> params = new HashMap<>();
        params.put("userId", userId);
        if (null != postType) {
            params.put("postType", postType.getId());
        }
        params.put("offset", offset);
        params.put("limit", limit);
        return sPostMapper.getUserPost(params);
    }

    /**
     * 获取用户所有动态
     */
    public List<SPost> getUserAllPost(Long userId){
        return sPostMapper.getUserAllPost(userId);
    }

    /**
     * 获取普通动态最近4张图片
     *
     * @param userId
     * @return
     */
    public String getLastNormalPics(Long userId) {
        List<SPost> postList = sPostMapper.getLastNormalPostPics(userId);
        List<String> picList = new ArrayList<>();
        for (SPost post : postList) {
            String[] values = StringUtils.split(post.getPics(), ",");
            for (String value : values) {
                picList.add(removeParameter(value));
                if (picList.size() >= 4) {
                    break;
                }
            }
        }
        return StringUtils.join(picList, ",");
    }

    /**
     * 动态点赞
     *
     * @param userId
     * @param post
     */
    public void savePostPraise(Long userId, SPost post, Long increment) {
        // 动态点赞数改变
        SPost updatePost = new SPost(post.getPostId());
        updatePost.setPraiseNum(increment.intValue());
        SPostPraise praise = new SPostPraise(post.getPostId(), userId);
        if (increment > 0) { // 新增点赞记录
            if (sPostMapper.updateFirstPraise(userId, post.getPostId()) > 0) {
                // 抢到首赞
                updatePost.setFirstPraiseUserId(userId);
            }
            // 点赞记录
            praise.setCreateTime(new Date());
            try {
                postPraiseMapper.insert(praise);
            } catch (DuplicateKeyException e) {
                // 主键重复处理
                throw new BusiException("您已点赞过了");
            }
        } else { // 取消点赞
            postPraiseMapper.deleteByPrimaryKey(praise);
            // 判断点赞用户是否是首赞用户
            if (userId.equals(post.getFirstPraiseUserId())) {
                // 查询新的首赞用户
                SPostPraise sPostPraise = postPraiseMapper.selectFirstPraise(praise);
                updatePost.setFirstPraiseUserId(Objects.nonNull(sPostPraise) ? sPostPraise.getUserId() : -1L);
            }
        }
        sPostMapper.updateByPrimaryKeySelective(updatePost);

        // 删除用户动态点赞PostId记录缓存 备注：新增点赞和取消点赞 我们都去删除这个缓存
        CallbackAfterTransactionUtil.send(() -> {
            socialRedisTemplate.del(buildPostUserPraisePostIds(userId));
        });
    }

    /**
     * 更新评论数
     *
     * @param postId
     * @param increment
     */
    public void updateCommentNum(Long postId, Integer increment) {
        SPost post = new SPost(postId);
        post.setCommentNum(increment);
        sPostMapper.updateByPrimaryKeySelective(post);
    }

    /**
     * 判断用户是否已点赞该动态
     *
     * @param postId
     * @param userId
     * @return
     */
    public boolean hasPostPraise(Long postId, Long userId) {
        return Objects.nonNull(postPraiseMapper.selectByPrimaryKey(new SPostPraise(postId, userId)));
    }

    /**
     * 有效时间内点赞消息是否发送过
     */
    public boolean checkPostNoticeMsg(FriendPostOptType optType, Long userId, Long postUserId, Long objectId) {
        if(userId.equals(postUserId)){
            return false;
        }
        return !socialRedisTemplate.sismember(buildFriendPostOptMsgLimit(userId, optType), objectId.toString());
    }

    /**
     * 记录动态操作通知
     */
    public void recordPostOptNotice(FriendPostOptType optType, Long userId, Long objectId){
        CallbackAfterTransactionUtil.send(() -> {
            // 点赞消息限制
            RedisKey friendPostOptMsgLimit = buildFriendPostOptMsgLimit(userId, optType);
            socialRedisTemplate.sadd(friendPostOptMsgLimit, String.valueOf(objectId));
            socialRedisTemplate.expire(friendPostOptMsgLimit);
        });
    }

    /**
     * 动态点赞消息限制
     */
    private RedisKey buildFriendPostOptMsgLimit(Long userId, FriendPostOptType optType) {
        return RedisKey.create(SocialKeyDefine.FriendPostOptMsgLimit, userId, optType);
    }

    /**
     * 更新动态状态
     *
     * @param postId      动态ID
     * @param auditUserId 审核人ID
     * @param status      审核状态
     * @param auditReason 审核原因
     */
    public void updatePostStatus(Long postId, Long auditUserId, ReviewResultType status, String auditReason, String remark) {
        SPost update = new SPost(postId);
        update.setAuditStatus(status);
        update.setAuditReason((auditReason == null ? "" : auditReason) + (remark == null ? "" : remark));
        update.setAuditUserId(auditUserId);
        update.setAuditTime(DateUtils.nowTime());
        sPostMapper.updateByPrimaryKeySelective(update);
        if (ReviewResultType.Pass != status) {
            return;
        }
        // 话题动态数新增
        SPost post = getPostById(postId);
        if (null != post && StringUtils.isNotEmpty(post.getGroupId())) {
            List<String> list = BusiUtils.strToList(post.getGroupId(), ",");
            if(ListUtils.isNotEmpty(list)){
                list.forEach(item -> {
                    SGroup group = groupMapper.selectByPrimaryKey(new SGroup(Long.valueOf(item)));
                    // 有效动态数改变
                    SGroup updateGroup = new SGroup(Long.valueOf(item));
                    updateGroup.setGp(group.getGp());
                    updateGroup.setGroupType(group.getGroupType());
                    updateGroup.setPostNum(1L);
                    updateGroup.setTotalPost(1L);
                    updateGroup.setUserNum(busiManager.checkUserJoinedPostGroup(group.getGroupId(), post.getUserId()) ? 1L : 0L);
                    groupMapper.updateByPrimaryKeySelective(updateGroup);
                });
            }
        }
    }

    public List<SPost> getAllPost() {
        return sPostMapper.selectAll();
    }

    /**
     * 分页查询动态点赞列表
     */
    public List<SPostPraise> listPraise(Long postId, Integer offset, Integer limit) {
        return postPraiseMapper.selectByPostId(postId, offset, limit);
    }

    /**
     * 查询动态首赞信息
     *
     * @param postId
     * @param userId
     * @return
     */
    public SPostPraise getPostPraiseInfo(Long postId, Long userId) {
        return postPraiseMapper.selectByPrimaryKey(new SPostPraise(postId, userId));
    }

    /**
     * 分页查询动态点赞列表
     */
    public List<SPostReward> listReward(Long postId, Integer offset, Integer limit) {
        return sPostRewardMapper.selectByPostId(postId, offset, limit);
    }

    /**
     * 用户动态点赞保存
     */
    private RedisKey buildPostUserPraisePostIds(Long userId) {
        return RedisKey.create(SocialKeyDefine.PostUserPraisePostIds, userId);
    }

    /**
     * 用户动态投票保存
     */
    private RedisKey buildPostUserVoteLogIds(Long userId) {
        return RedisKey.create(SocialKeyDefine.PostUserVoteLogIds, userId);
    }

    /**
     * 用户动态上热门保存
     */
    private RedisKey buildPostUserUpHotPostIds(Long userId) {
        return RedisKey.create(SocialKeyDefine.PostUserUpHotPostIds, userId);
    }

    /**
     * 热门动态队列
     */
    private RedisKey buildHotPost(SexType sexType) {
        return RedisKey.create(SocialKeyDefine.HotPost, sexType);
    }

    /**
     * 置顶动态列表
     */
    private RedisKey buildTopUpPost(BoolType type) {
        return RedisKey.create(SocialKeyDefine.TopUpPost, type);
    }
}
