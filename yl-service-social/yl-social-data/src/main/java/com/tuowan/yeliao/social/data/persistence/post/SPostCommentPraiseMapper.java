/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.social.data.persistence.post;

import com.easyooo.framework.sharding.annotation.Table;
import com.tuowan.yeliao.social.data.entity.post.SPostCommentPraise;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@Table(value = "s_post_comment_praise", schema = "yl_social")
public interface SPostCommentPraiseMapper {
    int deleteByPrimaryKey(SPostCommentPraise record);

    int insert(SPostCommentPraise record);

    SPostCommentPraise selectByPrimaryKey(SPostCommentPraise record);

    int updateByPrimaryKeySelective(SPostCommentPraise record);

    int updateByPrimaryKey(SPostCommentPraise record);

    // 根据用户ID 获取动态评论点赞数据
    List<SPostCommentPraise> listByUserId(@Param("userId") Long userId);
}