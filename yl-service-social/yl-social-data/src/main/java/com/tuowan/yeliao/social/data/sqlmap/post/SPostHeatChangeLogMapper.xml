<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tuowan.yeliao.social.data.persistence.post.SPostHeatChangeLogMapper">
  <sql id="Base_Column_List">
    log_id, user_id, post_id, base_heat_value, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="SPostHeatChangeLog" resultType="SPostHeatChangeLog">
    select 
    <include refid="Base_Column_List" />
    from s_post_heat_change_log
    where log_id = #{logId}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="SPostHeatChangeLog">
    delete from s_post_heat_change_log
    where log_id = #{logId}
  </delete>
  <insert id="insert" parameterType="SPostHeatChangeLog">
    insert into s_post_heat_change_log (log_id, user_id, post_id, base_heat_value, create_time)
    values (#{logId}, #{userId}, #{postId}, #{baseHeatValue}, #{createTime})
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="SPostHeatChangeLog">
    update s_post_heat_change_log
    <set>
      <if test="userId != null">
        user_id = #{userId},
      </if>
      <if test="postId != null">
        post_id = #{postId},
      </if>
      <if test="baseHeatValue != null">
        base_heat_value = #{baseHeatValue},
      </if>
      <if test="createTime != null">
        create_time = #{createTime},
      </if>
    </set>
    where log_id = #{logId}
  </update>
  <update id="updateByPrimaryKey" parameterType="SPostHeatChangeLog">
    update s_post_heat_change_log
    set user_id = #{userId},
      post_id = #{postId},
      base_heat_value = #{baseHeatValue},
      create_time = #{createTime}
    where log_id = #{logId}
  </update>
</mapper>