<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tuowan.yeliao.social.data.persistence.UUserGiftStatMapper">
  <sql id="Base_Column_List">
    user_id, gift_id, gift_count
  </sql>
  <select id="selectByPrimaryKey" parameterType="UUserGiftStat" resultType="UUserGiftStat">
    select 
    <include refid="Base_Column_List" />
    from u_user_gift_stat
    where user_id = #{userId}
      and gift_id = #{giftId}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="UUserGiftStat">
    delete from u_user_gift_stat
    where user_id = #{userId}
      and gift_id = #{giftId}
  </delete>
  <insert id="insert" parameterType="UUserGiftStat">
    insert into u_user_gift_stat (user_id, gift_id, gift_count)
    values (#{userId}, #{giftId}, #{giftCount})
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="UUserGiftStat">
    update u_user_gift_stat
    <set>
      <if test="giftCount != null">
        gift_count = gift_count + #{giftCount},
      </if>
    </set>
    where user_id = #{userId}
      and gift_id = #{giftId}
  </update>
  <update id="updateByPrimaryKey" parameterType="UUserGiftStat">
    update u_user_gift_stat
    set gift_count = gift_count + #{giftCount}
    where user_id = #{userId}
      and gift_id = #{giftId}
  </update>

  <select id="selectByUserId" resultType="UUserGiftStat">
    select
    <include refid="Base_Column_List" />
    from u_user_gift_stat
    where user_id = #{userId}
  </select>

  <insert id="init" parameterType="UUserGiftStat">
    insert into u_user_gift_stat (user_id, gift_id)
    values (#{userId}, #{giftId})
  </insert>
</mapper>