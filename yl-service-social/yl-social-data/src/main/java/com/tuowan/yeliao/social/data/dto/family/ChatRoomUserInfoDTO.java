package com.tuowan.yeliao.social.data.dto.family;


import com.tuowan.yeliao.commons.data.dto.user.UserBusiDTO;
import com.tuowan.yeliao.commons.data.entity.user.UUserBasic;
import com.tuowan.yeliao.commons.data.enums.user.SexType;
import com.tuowan.yeliao.commons.data.utils.BusiUtils;
import com.tuowan.yeliao.social.data.enums.family.FamilyRoleType;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/7/15 11:45
 */
public class ChatRoomUserInfoDTO {

    /**
     * 发送者ID
     */
    private Long senderId;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 性别
     */
    private SexType sex;
    /**
     * 当前进入的家族ID
     */
    private Long familyId;
    /**
     * 用户昵称
     */
    private String nickname;
    /**
     * 头像
     */
    private String headPic;

    /**
     * 头像框
     */
    private String headFrame;

    /**
     * 盖章图片
     */
    private String sealPic;
    /**
     * 用户等级
     */
    private Integer userLevel;
    /**
     * 魅力等级
     */
    private Integer charmLevel;
    /**
     * 年龄
     */
    private Integer age;
    /**
     * 所在城市
     */
    private String city;
    /**
     * 家族角色
     */
    private String familyRole;
    /**
     * 角色图片
     */
    private String familyRolePic;

    private List<String> badgesPic;

    /**
     * 目标用户信息
     */
    private ChatRoomUserInfoDTO targetUserObj;

    public static ChatRoomUserInfoDTO create(UUserBasic basic, FamilyRoleType roleType, UserBusiDTO busiDTO, boolean hideLevel, String city) {
        ChatRoomUserInfoDTO vo = new ChatRoomUserInfoDTO();
        vo.setSenderId(basic.getUserId());
        vo.setUserId(basic.getUserId());
        vo.setNickname(basic.getNickname());
        vo.setAge(BusiUtils.getAgeByDate(basic.getBirthDate()));
        vo.setHeadPic(basic.getHeadPic());
        vo.setHeadFrame(busiDTO.getHeadFrame());
        vo.setSealPic(busiDTO.getSealPic());
        vo.setFamilyRole(roleType.getDesc());
        vo.setFamilyRolePic(roleType.getRolePic());
        vo.setSex(basic.getSex());
        vo.setBadgesPic(busiDTO.getBadgesPic());
        if (!hideLevel) {
            vo.setUserLevel(busiDTO.getUserLevel());
            vo.setCharmLevel(busiDTO.getCharmLevel());
        }
        vo.setCity(city);
        return vo;
    }

    public static ChatRoomUserInfoDTO createSimple(UUserBasic basic) {
        ChatRoomUserInfoDTO dto = new ChatRoomUserInfoDTO();
        dto.setUserId(basic.getUserId());
        dto.setNickname(basic.getNickname());
        dto.setHeadPic(basic.getHeadPic());
        return dto;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getSenderId() {
        return senderId;
    }

    public void setSenderId(Long senderId) {
        this.senderId = senderId;
    }

    public SexType getSex() {
        return sex;
    }

    public void setSex(SexType sex) {
        this.sex = sex;
    }

    public Long getFamilyId() {
        return familyId;
    }

    public void setFamilyId(Long familyId) {
        this.familyId = familyId;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getHeadPic() {
        return headPic;
    }

    public void setHeadPic(String headPic) {
        this.headPic = headPic;
    }

    public Integer getUserLevel() {
        return userLevel;
    }

    public void setUserLevel(Integer userLevel) {
        this.userLevel = userLevel;
    }

    public Integer getCharmLevel() {
        return charmLevel;
    }

    public void setCharmLevel(Integer charmLevel) {
        this.charmLevel = charmLevel;
    }

    public Integer getAge() {
        return age;
    }

    public void setAge(Integer age) {
        this.age = age;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getFamilyRole() {
        return familyRole;
    }

    public void setFamilyRole(String familyRole) {
        this.familyRole = familyRole;
    }

    public String getFamilyRolePic() {
        return familyRolePic;
    }

    public void setFamilyRolePic(String familyRolePic) {
        this.familyRolePic = familyRolePic;
    }

    public ChatRoomUserInfoDTO getTargetUserObj() {
        return targetUserObj;
    }

    public void setTargetUserObj(ChatRoomUserInfoDTO targetUserObj) {
        this.targetUserObj = targetUserObj;
    }

    public List<String> getBadgesPic() {
        return badgesPic;
    }

    public void setBadgesPic(List<String> badgesPic) {
        this.badgesPic = badgesPic;
    }

    public String getHeadFrame() {
        return headFrame;
    }

    public void setHeadFrame(String headFrame) {
        this.headFrame = headFrame;
    }

    public String getSealPic() {
        return sealPic;
    }

    public void setSealPic(String sealPic) {
        this.sealPic = sealPic;
    }
}
