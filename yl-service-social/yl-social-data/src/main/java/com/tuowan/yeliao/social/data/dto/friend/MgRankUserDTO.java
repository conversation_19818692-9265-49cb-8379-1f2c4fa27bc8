package com.tuowan.yeliao.social.data.dto.friend;

import com.tuowan.yeliao.commons.core.enums.general.BoolType;

public class MgRankUserDTO {
    // 动画01开关
    private BoolType animSwitch01;
    // 动画02开关
    private BoolType animSwitch02;

    // 送礼方昵称
    private String senderNickname;
    // 送礼方头像
    private String senderHeadPic;

    // 收礼方昵称
    private String receiverNickname;
    // 收礼方头像
    private String receiverHeadPic;

    public static MgRankUserDTO build1(BoolType animSwitch01, BoolType animSwitch02, String senderNickname, String senderHeadPic, String receiverNickname, String receiverHeadPic){
        MgRankUserDTO dto = new MgRankUserDTO();
        dto.setAnimSwitch01(animSwitch01);
        dto.setAnimSwitch02(animSwitch02);
        dto.setSenderNickname(senderNickname);
        dto.setSenderHeadPic(senderHeadPic);
        dto.setReceiverNickname(receiverNickname);
        dto.setReceiverHeadPic(receiverHeadPic);
        return dto;
    }

    public BoolType getAnimSwitch01() {
        return animSwitch01;
    }

    public void setAnimSwitch01(BoolType animSwitch01) {
        this.animSwitch01 = animSwitch01;
    }

    public BoolType getAnimSwitch02() {
        return animSwitch02;
    }

    public void setAnimSwitch02(BoolType animSwitch02) {
        this.animSwitch02 = animSwitch02;
    }

    public String getSenderNickname() {
        return senderNickname;
    }

    public void setSenderNickname(String senderNickname) {
        this.senderNickname = senderNickname;
    }

    public String getSenderHeadPic() {
        return senderHeadPic;
    }

    public void setSenderHeadPic(String senderHeadPic) {
        this.senderHeadPic = senderHeadPic;
    }

    public String getReceiverNickname() {
        return receiverNickname;
    }

    public void setReceiverNickname(String receiverNickname) {
        this.receiverNickname = receiverNickname;
    }

    public String getReceiverHeadPic() {
        return receiverHeadPic;
    }

    public void setReceiverHeadPic(String receiverHeadPic) {
        this.receiverHeadPic = receiverHeadPic;
    }
}
