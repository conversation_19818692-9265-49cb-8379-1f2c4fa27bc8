<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tuowan.yeliao.social.data.persistence.family.FFamilyMemberOperationRecordMapper">
  <sql id="Base_Column_List">
    record_id, family_id, user_id, operation_type, operation_time, optr_id, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="FFamilyMemberOperationRecord" resultType="FFamilyMemberOperationRecord">
    select 
    <include refid="Base_Column_List" />
    from f_family_member_operation_record
    where record_id = #{recordId}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="FFamilyMemberOperationRecord">
    delete from f_family_member_operation_record
    where record_id = #{recordId}
  </delete>
  <insert id="insert" parameterType="FFamilyMemberOperationRecord">
    insert into f_family_member_operation_record (record_id, family_id, user_id, operation_type, operation_time, 
      optr_id, create_time)
    values (#{recordId}, #{familyId}, #{userId}, #{operationType}, #{operationTime}, 
      #{optrId}, #{createTime})
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="FFamilyMemberOperationRecord">
    update f_family_member_operation_record
    <set>
      <if test="familyId != null">
        family_id = #{familyId},
      </if>
      <if test="userId != null">
        user_id = #{userId},
      </if>
      <if test="operationType != null">
        operation_type = #{operationType},
      </if>
      <if test="operationTime != null">
        operation_time = #{operationTime},
      </if>
      <if test="optrId != null">
        optr_id = #{optrId},
      </if>
      <if test="createTime != null">
        create_time = #{createTime},
      </if>
    </set>
    where record_id = #{recordId}
  </update>
  <update id="updateByPrimaryKey" parameterType="FFamilyMemberOperationRecord">
    update f_family_member_operation_record
    set family_id = #{familyId},
      user_id = #{userId},
      operation_type = #{operationType},
      operation_time = #{operationTime},
      optr_id = #{optrId},
      create_time = #{createTime}
    where record_id = #{recordId}
  </update>
  <insert id="insertBatch" parameterType="FFamilyMemberOperationRecord">
    insert into f_family_member_operation_record
    (family_id, user_id, operation_type, operation_time, optr_id, create_time)
    values
    <foreach collection ="recordList" item="l" index= "index" separator =",">
      (#{l.familyId}, #{l.userId}, #{l.operationType}, #{l.operationTime}, #{l.optrId}, #{l.createTime})
    </foreach >
  </insert >
  <select id="getLastRecordByType" resultType="FFamilyMemberOperationRecord">
    select
    <include refid="Base_Column_List" />
    from f_family_member_operation_record
    where family_id = #{familyId} and user_id = #{userId} and operation_type = #{operationType} and operation_time is not null
    order by record_id desc
    limit 0,1
  </select>
</mapper>