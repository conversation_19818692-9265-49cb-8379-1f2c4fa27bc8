package com.tuowan.yeliao.social.data.search.repository;

import com.easyooo.framework.common.util.CglibUtils;
import com.easyooo.framework.common.util.MapUtils;
import com.easyooo.framework.common.util.MsgUtils;
import com.tuowan.yeliao.commons.core.enums.general.BoolType;
import com.tuowan.yeliao.commons.data.entity.user.UUserBasic;
import com.tuowan.yeliao.commons.data.enums.user.SexType;
import com.tuowan.yeliao.commons.search.core.SearchRepository;
import com.tuowan.yeliao.social.data.entity.post.SPost;
import com.tuowan.yeliao.social.data.enums.post.GroupPostPageType;
import com.tuowan.yeliao.social.data.enums.post.PostType;
import com.tuowan.yeliao.social.data.search.document.PostFeature;
import org.elasticsearch.common.unit.DistanceUnit;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.script.Script;
import org.elasticsearch.script.ScriptType;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.ScriptSortBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 用户动态信息
 *
 * <AUTHOR>
 * @date 2022/6/21 13:31
 */
@Component
public class PostFeatureRepository extends SearchRepository<PostFeature, Long> {

    private Logger logger = LoggerFactory.getLogger(getClass());

    /**
     * 搜索关注的动态
     * 备注：按照发布时间降序排列
     */
    public List<PostFeature> searchFollow(PostType postType, Integer offset, Integer limit, List<Long> followUsers, SexType sexType) {
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        if(Objects.nonNull(postType)){
            boolQueryBuilder.must(QueryBuilders.termsQuery("postType", postType));
        }
        // 指定用户
        boolQueryBuilder.must(QueryBuilders.termsQuery("userId", followUsers));
        // 只看异性
        if (sexType != null) {
            boolQueryBuilder.must(QueryBuilders.termQuery("sex", SexType.Female == sexType ? SexType.Male : SexType.Female));
        }
        sourceBuilder.query(boolQueryBuilder);
        sourceBuilder.from(offset);
        sourceBuilder.size(limit);
        // 1.按创建时间降序
        sourceBuilder.sort("postTime", SortOrder.DESC);
        return search(sourceBuilder);
    }

    /**
     * 搜索最近的动态
     * 备注：按照动态的发布时间降序排列
     *
     * @param postType
     * @param offset
     * @param limit
     */
    public List<PostFeature> searchLately(PostType postType, Integer offset, Integer limit, SexType sexType) {
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        if(Objects.nonNull(postType)){
            boolQueryBuilder.must(QueryBuilders.termsQuery("postType", postType));
        }
        // 只看异性
        if (sexType != null) {
            boolQueryBuilder.must(QueryBuilders.termQuery("sex", SexType.Female == sexType ? SexType.Male : SexType.Female));
        }
        sourceBuilder.query(boolQueryBuilder);
        sourceBuilder.from(offset);
        sourceBuilder.size(limit);
        // 1.按创建时间降序
        sourceBuilder.sort("postTime", SortOrder.DESC);
        return search(sourceBuilder);
    }

    /**
     * 搜索附近的动态
     *
     * @param postType
     * @param lat        我的纬度
     * @param lng        我的经度
     * @param offset
     * @param limit
     * @param currUserId
     * @return
     */
    public List<PostFeature> searchNearby(PostType postType, Double lat, Double lng, Integer offset, Integer limit, Long currUserId, SexType sexType, boolean isReviewVersion) {
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        // 查询动态
        boolQueryBuilder.must(QueryBuilders.termsQuery("postType", postType));
        // 审核包看特定动态
        if (isReviewVersion) {
            boolQueryBuilder.must(QueryBuilders.termQuery("normal", "1"));
        }else{
            boolQueryBuilder.must(QueryBuilders.termQuery("normal", "0"));
        }
        // 排除自己
        if (currUserId != null) {
            boolQueryBuilder.mustNot(QueryBuilders.termsQuery("userId", Collections.singletonList(currUserId)));
        }
        // 只看异性
        if (sexType != null) {
            boolQueryBuilder.must(QueryBuilders.termQuery("sex", SexType.Female == sexType ? SexType.Male : SexType.Female));
        }
        // 如果有位置信息 则根据位置信息匹配 TODO-前期没有多少女用户 则把范围拉大，后期人数多了该小 （原始值：300）
        if(!isReviewVersion && Objects.nonNull(lat) && Objects.nonNull(lng)){
            boolQueryBuilder.must(QueryBuilders.geoDistanceQuery("loc").point(lat, lng).distance(1000, DistanceUnit.KILOMETERS));
        }
        sourceBuilder.query(boolQueryBuilder);
        sourceBuilder.from(offset);
        sourceBuilder.size(limit);
        // 1.按总热度降序
        sourceBuilder.sort(new ScriptSortBuilder(Script.parse("doc['baseHeatValue'].value + doc['heatValue'].value"), ScriptSortBuilder.ScriptSortType.NUMBER).order(SortOrder.DESC));
        // 2.如果有位置信息、按距离升序
        if(Objects.nonNull(lat) && Objects.nonNull(lng)) {
            sourceBuilder.sort(SortBuilders.geoDistanceSort("loc", lat, lng).unit(DistanceUnit.KILOMETERS).order(SortOrder.ASC));
        }
        // 3.按创建时间降序
        sourceBuilder.sort("postTime", SortOrder.DESC);
        return search(sourceBuilder);
    }

    /**
     * 搜索推荐的动态
     *
     * @param postType
     * @param offset
     * @param limit
     * @return
     */
    public List<PostFeature> searchRecom(PostType postType, Integer offset, Integer limit, SexType sexType) {
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        if(Objects.nonNull(postType)){
            boolQueryBuilder.must(QueryBuilders.termsQuery("postType", postType));
        }
        if (sexType != null) { // 只看异性
            boolQueryBuilder.must(QueryBuilders.termQuery("sex", SexType.Female == sexType ? SexType.Male : SexType.Female));
        }
        sourceBuilder.query(boolQueryBuilder);
        sourceBuilder.from(offset);
        sourceBuilder.size(limit);
        // 1.按发布时间、点赞数 来排序
        String scriptSortStr = "double praise = doc['heatValue'].value;" +
            "double hoursSincePublished = (params.currentTime - doc['postTime'].value) / 3600000.0;" +
            "return praise / (1 + hoursSincePublished);";
        Script script = new Script(ScriptType.INLINE, "painless", scriptSortStr, MapUtils.gmap("currentTime", System.currentTimeMillis()));
        sourceBuilder.sort(new ScriptSortBuilder(script, ScriptSortBuilder.ScriptSortType.NUMBER).order(SortOrder.DESC));
        // 2.按创建时间降序
        sourceBuilder.sort("postTime", SortOrder.DESC);
        return search(sourceBuilder);
    }

    /**
     * 搜索置顶动态
     */
    public List<PostFeature> searchTopUpPost(Integer offset, Integer limit, SexType sexType){
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.termsQuery("postType", PostType.TopUp));
        boolQueryBuilder.must(QueryBuilders.rangeQuery("data1").gt(0));
        if (sexType != null) { // 只看异性
            // boolQueryBuilder.must(QueryBuilders.termQuery("sex", SexType.Female == sexType ? SexType.Male : SexType.Female));
        }
        sourceBuilder.query(boolQueryBuilder);
        sourceBuilder.from(offset);
        sourceBuilder.size(limit);
        // 1.按照置顶时间降序排列（data1 字段）
        sourceBuilder.sort("data1", SortOrder.DESC);
        return search(sourceBuilder);
    }

    /**
     * 搜索话题的动态
     */
    public List<PostFeature> searchGroupPost(Long groupId, PostType postType, GroupPostPageType pageType, Integer offset, Integer limit, Long userId, SexType sexType) {
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        if(Objects.nonNull(postType)){
            boolQueryBuilder.must(QueryBuilders.termQuery("postType", postType));
        }
        boolQueryBuilder.must(QueryBuilders.matchQuery("groupId", MsgUtils.format("#{}#", groupId)));
        // boolQueryBuilder.should(QueryBuilders.termQuery("sex", SexType.Female == sexType ? SexType.Male : SexType.Female));
        // boolQueryBuilder.should(QueryBuilders.termQuery("userId", userId));
        // boolQueryBuilder.minimumShouldMatch(1);

        sourceBuilder.query(boolQueryBuilder);
        sourceBuilder.from(offset);
        sourceBuilder.size(limit);
        if(GroupPostPageType.Heat == pageType){
            // 1.按总热度降序（基础热度 + 获得热度）
            sourceBuilder.sort(new ScriptSortBuilder(Script.parse("doc['baseHeatValue'].value + doc['heatValue'].value"), ScriptSortBuilder.ScriptSortType.NUMBER).order(SortOrder.DESC));
            // 2.按创建时间降序
            sourceBuilder.sort("postTime", SortOrder.DESC);
        }else if(GroupPostPageType.Time == pageType){
            // 1.按创建时间降序
            sourceBuilder.sort("postTime", SortOrder.DESC);
        }
        return search(sourceBuilder);
    }

    /**
     * 搜索用户的动态
     */
    public List<PostFeature> searchUserPost(Long userId, PostType postType, Integer offset, Integer limit, SexType sexType) {
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        if(Objects.nonNull(postType)){
            boolQueryBuilder.must(QueryBuilders.termQuery("postType", postType));
        }
        boolQueryBuilder.must(QueryBuilders.termQuery("userId", userId));
        boolQueryBuilder.must(QueryBuilders.termQuery("realPerson", BoolType.False));
        if (sexType != null) { // 只看异性
            boolQueryBuilder.must(QueryBuilders.termQuery("sex", sexType == SexType.Female ? SexType.Male : SexType.Female));
        }
        sourceBuilder.query(boolQueryBuilder);
        sourceBuilder.from(offset);
        sourceBuilder.size(limit);
        // 1.按排序值降序
        sourceBuilder.sort("order", SortOrder.DESC);
        // 2.按创建时间降序
        sourceBuilder.sort("postTime", SortOrder.DESC);
        return search(sourceBuilder);
    }

    /**
     * 保存动态到搜索引擎
     * @param post
     * @param userBasic
     */
    public void refreshPost(SPost post, UUserBasic userBasic){
        if(Objects.isNull(post) || Objects.isNull(userBasic)){
            return;
        }
        try {
            this.save(new PostFeature(post, userBasic));
        }catch (Exception e){
            logger.error("同步动态数据到post-feature失败, 动态ID:{}, 原因:", post.getPostId(), e);
        }
    }

    /**
     * 更新动态热度分
     * @param postId
     * @param heatValue
     */
    public void updateHeat(long postId, Long heatValue, Long baseHeatValue){
        try {
            PostFeature update = new PostFeature();
            update.setPostId(postId);
            update.setHeatValue(heatValue);
            update.setBaseHeatValue(baseHeatValue);
            this.updateSelective(update);
        }catch (Exception e){
            logger.error("更新动态热度值错误, 动态ID:{}, 原因:", postId, e);
        }
    }

    /**
     * 更新动态排序
     */
    public void updateOrder(long postId, Long order){
        try {
            PostFeature update = new PostFeature();
            update.setPostId(postId);
            update.setOrder(order);
            this.updateSelective(update);
        }catch (Exception e){
            logger.error("更新动态排序值错误, 动态ID:{}, 原因:", postId, e);
        }
    }
}
