<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tuowan.yeliao.social.data.persistence.FMindGiftStatMapper">
  <sql id="Base_Column_List">
    id, user_id, friend_id, gift_id, beans, mg_message, create_time, tid
  </sql>
  <select id="selectByPrimaryKey" parameterType="FMindGiftStat" resultType="FMindGiftStat">
    select 
    <include refid="Base_Column_List" />
    from f_mind_gift_stat
    where id = #{id}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="FMindGiftStat">
    delete from f_mind_gift_stat
    where id = #{id}
  </delete>
  <insert id="insert" parameterType="FMindGiftStat" useGeneratedKeys="true" keyProperty="id">
    insert into f_mind_gift_stat (id, user_id, friend_id, gift_id, beans, mg_message, create_time, tid
      )
    values (#{id}, #{userId}, #{friendId}, #{giftId}, #{beans}, #{mgMessage}, #{createTime}, #{tid}
      )
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="FMindGiftStat">
    update f_mind_gift_stat
    <set>
      <if test="userId != null">
        user_id = #{userId},
      </if>
      <if test="friendId != null">
        friend_id = #{friendId},
      </if>
      <if test="giftId != null">
        gift_id = #{giftId},
      </if>
      <if test="beans != null">
        beans = #{beans},
      </if>
      <if test="mgMessage != null">
        mg_message = #{mgMessage},
      </if>
      <if test="createTime != null">
        create_time = #{createTime},
      </if>
      <if test="tid != null">
        tid = #{tid},
      </if>
    </set>
    where id = #{id}
  </update>
  <update id="updateByPrimaryKey" parameterType="FMindGiftStat">
    update f_mind_gift_stat
    set user_id = #{userId},
      friend_id = #{friendId},
      gift_id = #{giftId},
      beans = #{beans},
      mg_message = #{mgMessage},
      create_time = #{createTime},
      tid = #{tid}
    where id = #{id}
  </update>

  <select id="queryMgFriends" resultType="com.tuowan.yeliao.social.data.dto.friend.MgFriendDTO">
    select friend_id as friendId, count(1) as mindNum, sum(beans) as mindBeans
    from f_mind_gift_stat
    where user_id = #{userId}
    group by friend_id
    order by sum(beans) desc
    limit #{offset},#{limit}
  </select>

  <select id="queryMgRecord" resultType="FMindGiftStat">
    select
    <include refid="Base_Column_List" />
    from f_mind_gift_stat
    <where>
      <if test="userId != null">
        and user_id = #{userId}
      </if>
    </where>
    order by id desc
    limit #{offset},#{limit}
  </select>

  <select id="queryDayMgRank" resultType="com.tuowan.yeliao.social.data.dto.friend.DayMgRankDTO">
    select user_id as userId, sum(beans) as beans
    from f_mind_gift_stat
    <where>
      <if test="startTime != null">
        and create_time &gt;= #{startTime}
      </if>
      <if test="endTime != null">
        and create_time &lt;= #{endTime}
      </if>
    </where>
    group by user_id
    order by sum(beans) desc, user_id asc
    limit #{limit}
  </select>

  <select id="queryHisMgRank" resultType="com.tuowan.yeliao.social.data.dto.friend.HisMgRankDTO">
    select date, user_id
    from (
      select
      date(create_time) as date,
      user_id,
      sum(beans) as total_beans,
      rank() over (partition by date(create_time) order by sum(beans) desc, user_id asc) as rk
      from f_mind_gift_stat
      <where>
        <if test="startTime != null">
          and create_time &gt;= #{startTime}
        </if>
        <if test="endTime != null">
          and create_time &lt;= #{endTime}
        </if>
      </where>
      group by date(create_time), user_id
    ) as ranked
    where rk = 1
    limit #{limit}
  </select>

  <select id="queryMgRankUserEldestBrother" resultType="java.lang.Long">
    select friend_id
    from f_mind_gift_stat
    <where>
      <if test="userId != null">
        and user_id = #{userId}
      </if>
      <if test="startTime != null">
        and create_time &gt;= #{startTime}
      </if>
      <if test="endTime != null">
        and create_time &lt;= #{endTime}
      </if>
    </where>
    group by friend_id
    order by sum(beans) desc
    limit 1
  </select>
</mapper>