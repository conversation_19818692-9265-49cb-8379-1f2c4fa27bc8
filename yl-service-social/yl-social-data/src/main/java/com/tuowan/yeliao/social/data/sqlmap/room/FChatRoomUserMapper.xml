<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tuowan.yeliao.social.data.persistence.room.FChatRoomUserMapper">
  <sql id="Base_Column_List">
    room_id, user_id, type, audit_status, black, not_disturb, last_time, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="FChatRoomUser" resultType="FChatRoomUser">
    select 
    <include refid="Base_Column_List" />
    from f_chat_room_user
    where room_id = #{roomId}
      and user_id = #{userId}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="FChatRoomUser">
    delete from f_chat_room_user
    where room_id = #{roomId}
      and user_id = #{userId}
  </delete>
  <insert id="insert" parameterType="FChatRoomUser">
    insert into f_chat_room_user (room_id, user_id, type, audit_status, black, not_disturb, last_time, create_time)
    values (#{roomId}, #{userId}, #{type}, #{auditStatus}, #{black}, #{notDisturb}, #{lastTime}, #{createTime})
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="FChatRoomUser">
    update f_chat_room_user
    <set>
      <if test="type != null">
        type = #{type},
      </if>
      <if test="auditStatus != null">
        audit_status = #{auditStatus},
      </if>
      <if test="black != null">
        black = #{black},
      </if>
      <if test="notDisturb != null">
        not_disturb = #{notDisturb},
      </if>
      <if test="lastTime != null">
        last_time = #{lastTime},
      </if>
      <if test="createTime != null">
        create_time = #{createTime},
      </if>
    </set>
    where room_id = #{roomId}
      and user_id = #{userId}
  </update>
  <update id="updateByPrimaryKey" parameterType="FChatRoomUser">
    update f_chat_room_user
    set type = #{type},
      audit_status = #{auditStatus},
      black = #{black},
      not_disturb = #{notDisturb},
      last_time = #{lastTime},
      create_time = #{createTime}
    where room_id = #{roomId}
      and user_id = #{userId}
  </update>

  <select id="getChatRoomUserNum" resultType="int">
    select
    count(user_id)
    from f_chat_room_user
    where room_id = #{roomId} and audit_status in ('W', 'P')
  </select>

  <select id="getChatRoomWaitUserNum" resultType="int">
    select
    count(user_id)
    from f_chat_room_user
    where room_id = #{roomId} and audit_status = 'W'
  </select>

  <select id="queryChatRoomBlackUsers" resultType="FChatRoomUser">
    select
    <include refid="Base_Column_List" />
    from f_chat_room_user
    where room_id = #{roomId}
    and black = 'T'
    order by create_time desc
    limit #{offset},#{limit}
  </select>

  <select id="queryChatRoomWaitRejectUsers" resultType="FChatRoomUser">
    select
    <include refid="Base_Column_List" />
    from f_chat_room_user
    where room_id = #{roomId}
    and audit_status = 'W'
    order by create_time asc
    limit #{offset},#{limit}
  </select>

  <select id="queryChatRoomPassUsers" resultType="FChatRoomUser">
    select
    <include refid="Base_Column_List" />
    from f_chat_room_user
    where room_id = #{roomId}
    and audit_status = 'P'
    order by create_time asc
    limit #{offset},#{limit}
  </select>

  <select id="queryChatRoomPassWaitUsers" resultType="FChatRoomUser">
    select
    <include refid="Base_Column_List" />
    from f_chat_room_user
    where room_id = #{roomId}
    and audit_status in ('P', 'W')
    order by audit_status asc, create_time desc
  </select>

  <select id="queryChatRoomPassUsersNum" resultType="java.lang.Integer">
    select
    count(1)
    from f_chat_room_user
    where room_id = #{roomId}
    and audit_status = 'P'
  </select>
</mapper>