/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.social.data.persistence;

import com.easyooo.framework.sharding.annotation.Table;
import com.tuowan.yeliao.social.data.entity.UScoreChange;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@Table(value = "u_score_change", schema = "yl_social")
public interface UScoreChangeMapper {
    int deleteByPrimaryKey(UScoreChange record);

    int insert(UScoreChange record);

    UScoreChange selectByPrimaryKey(UScoreChange record);

    int updateByPrimaryKeySelective(UScoreChange record);

    int updateByPrimaryKey(UScoreChange record);

    List<UScoreChange> queryPage(@Param("userId") Long userId, @Param("offset") Integer offset, @Param("limit") Integer limit);
}