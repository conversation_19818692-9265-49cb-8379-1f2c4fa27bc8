<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tuowan.yeliao.social.data.persistence.SCosmosMapper">
    <sql id="Base_Column_List">
        cosmos_id
        , user_id, content, lng, lat, city, display_city, status, create_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="SCosmos" resultType="SCosmos">
        select
        <include refid="Base_Column_List"/>
        from s_cosmos
        where cosmos_id = #{cosmosId}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="SCosmos">
        delete
        from s_cosmos
        where cosmos_id = #{cosmosId}
    </delete>
    <insert id="insert" parameterType="SCosmos" keyProperty="cosmosId" useGeneratedKeys="true">
        insert into s_cosmos (cosmos_id, user_id, content, lng, lat, city, display_city, status, create_time)
        values (#{cosmosId}, #{userId}, #{content}, #{lng}, #{lat}, #{city}, #{displayCity}, #{status}, #{createTime})
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="SCosmos">
        update s_cosmos
        <set>
            <if test="userId != null">
                user_id = #{userId},
            </if>
            <if test="content != null">
                content = #{content},
            </if>
            <if test="lng != null">
                lng = #{lng},
            </if>
            <if test="lat != null">
                lat = #{lat},
            </if>
            <if test="city != null">
                city = #{city},
            </if>
            <if test="displayCity != null">
                display_city = #{displayCity},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
        </set>
        where cosmos_id = #{cosmosId}
    </update>
    <update id="updateByPrimaryKey" parameterType="SCosmos">
        update s_cosmos
        set user_id      = #{userId},
            content      = #{content},
            lng          = #{lng},
            lat          = #{lat},
            city         = #{city},
            display_city = #{displayCity},
            status       = #{status},
            create_time  = #{createTime}
        where cosmos_id = #{cosmosId}
    </update>
</mapper>