<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tuowan.yeliao.social.data.persistence.FChatNetCallFeeMapper">
  <sql id="Base_Column_List">
    fee_id, call_id, user_id, beans, platform_beans, deduct_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="FChatNetCallFee" resultType="FChatNetCallFee">
    select
    <include refid="Base_Column_List" />
    from f_chat_net_call_fee
    where fee_id = #{feeId}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="FChatNetCallFee">
    delete from f_chat_net_call_fee
    where fee_id = #{feeId}
  </delete>
  <insert id="insert" parameterType="FChatNetCallFee">
    insert into f_chat_net_call_fee (fee_id, call_id, user_id, beans, platform_beans, deduct_time)
    values (#{feeId}, #{callId}, #{userId}, #{beans}, #{platformBeans}, #{deductTime})
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="FChatNetCallFee">
    update f_chat_net_call_fee
    <set>
      <if test="callId != null">
        call_id = #{callId},
      </if>
      <if test="userId != null">
        user_id = #{userId},
      </if>
      <if test="beans != null">
        beans = #{beans},
      </if>
      <if test="platformBeans != null">
        platform_beans = #{platformBeans},
      </if>
      <if test="deductTime != null">
        deduct_time = #{deductTime},
      </if>
    </set>
    where fee_id = #{feeId}
  </update>
  <update id="updateByPrimaryKey" parameterType="FChatNetCallFee">
    update f_chat_net_call_fee
    set call_id = #{callId},
      user_id = #{userId},
      beans = #{beans},
    platform_beans = #{platformBeans},
    deduct_time = #{deductTime}
    where fee_id = #{feeId}
  </update>

  <select id="queryPreTotalBeans" resultType="com.tuowan.yeliao.social.data.dto.friend.netcall.NetCallFeeDTO">
    select ifnull(sum(beans), 0) beans, ifnull(sum(ifnull(platform_beans, 0)), 0) platformBeans
    from f_chat_net_call_fee
    where call_id = #{callId}
  </select>
</mapper>