<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tuowan.yeliao.social.data.persistence.post.SPostCollectMapper">
  <sql id="Base_Column_List">
    log_id, post_id, user_id, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="SPostCollect" resultType="SPostCollect">
    select 
    <include refid="Base_Column_List" />
    from s_post_collect
    where log_id = #{logId}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="SPostCollect">
    delete from s_post_collect
    where log_id = #{logId}
  </delete>
  <insert id="insert" parameterType="SPostCollect" useGeneratedKeys="true" keyProperty="logId">
    insert into s_post_collect (log_id, post_id, user_id, create_time)
    values (#{logId}, #{postId}, #{userId}, #{createTime})
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="SPostCollect">
    update s_post_collect
    <set>
      <if test="postId != null">
        post_id = #{postId},
      </if>
      <if test="userId != null">
        user_id = #{userId},
      </if>
      <if test="createTime != null">
        create_time = #{createTime},
      </if>
    </set>
    where log_id = #{logId}
  </update>
  <update id="updateByPrimaryKey" parameterType="SPostCollect">
    update s_post_collect
    set post_id = #{postId},
      user_id = #{userId},
      create_time = #{createTime}
    where log_id = #{logId}
  </update>

  <select id="selectByPu" resultType="SPostCollect">
    select
    <include refid="Base_Column_List" />
    from s_post_collect
    where post_id = #{postId} and user_id = #{userId}
  </select>
</mapper>