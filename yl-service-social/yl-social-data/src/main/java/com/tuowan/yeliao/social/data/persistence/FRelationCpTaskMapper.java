/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.social.data.persistence;

import com.easyooo.framework.sharding.annotation.Table;
import com.tuowan.yeliao.social.data.entity.FRelationCpTask;
import org.springframework.stereotype.Repository;

@Repository
@Table(value = "F_RELATION_CP_TASK", schema = "YL_SOCIAL")
public interface FRelationCpTaskMapper {
    int deleteByPrimaryKey(FRelationCpTask record);

    int insert(FRelationCpTask record);

    FRelationCpTask selectByPrimaryKey(FRelationCpTask record);

    int updateByPrimaryKeySelective(FRelationCpTask record);

    int updateByPrimaryKey(FRelationCpTask record);
}