package com.tuowan.yeliao.social.data.search.repository;

import com.easyooo.framework.common.util.MsgUtils;
import com.tuowan.yeliao.commons.config.UnifiedConfig;
import com.tuowan.yeliao.commons.core.enums.general.BoolType;
import com.tuowan.yeliao.commons.data.utils.BusiUtils;
import com.tuowan.yeliao.commons.search.core.SearchRepository;
import com.tuowan.yeliao.social.data.entity.family.FFamily;
import com.tuowan.yeliao.social.data.manager.family.FamilyManager;
import com.tuowan.yeliao.social.data.search.document.FamilyFeature;
import org.elasticsearch.common.unit.DistanceUnit;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 家族基本特征信息
 *
 * <AUTHOR>
 * @date 2021/12/14 19:31
 */
@Component
public class FamilyFeatureRepository extends SearchRepository<FamilyFeature, Integer> {

    private Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    private FamilyManager familyManager;


    /**
     * 搜索同城家族
     *
     * @param lat    我的纬度
     * @param lon    我的经度
     * @param offset
     * @param limit
     * @return
     */
    public List<FamilyFeature> searchSameCity(double lat, double lon, Integer offset, Integer limit, double distance) {
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.geoDistanceQuery("location").point(lat, lon).distance(distance, DistanceUnit.METERS));
        sourceBuilder.query(boolQueryBuilder);
        sourceBuilder.from(offset);
        sourceBuilder.size(limit);
        // 根据推荐分排序
        sourceBuilder.sort("recomScore", SortOrder.DESC);
        sourceBuilder.sort(SortBuilders.geoDistanceSort("location", lat, lon).unit(DistanceUnit.METERS).order(SortOrder.ASC));
        if (!UnifiedConfig.isProdEnv()) {
            logger.info("=====同城家族搜索条件：{}", sourceBuilder.toString());
        }
        return super.search(sourceBuilder);
    }

    /**
     * 批量更新家族热度分值
     */
    public void batchRefresh(Map<String, Double> scoreMap) {
        if (scoreMap.size() == 0) {
            return;
        }
        scoreMap.forEach((k, v) -> {
            refresh(Integer.valueOf(k), v.longValue());
        });
    }

    /**
     * 更新家族基本信息到搜索引擎
     *
     * @param familyId
     * @param recomScore
     */
    public void refresh(Integer familyId, Long recomScore) {
        try {
            FFamily family = familyManager.getFamily(familyId);
            if (family == null || BoolType.True == family.getFreeze()) {
                this.delete(familyId);
                return;
            }
            // 纬度值
            String lat = family.getLat();
            // 经度值
            String lng = family.getLng();
            // 非法数据相当于没有定位
            if (BusiUtils.isInValidLocationData(lat) || BusiUtils.isInValidLocationData(lng)) {
                return;
            }
            FamilyFeature feature = new FamilyFeature(family, recomScore);
            this.save(feature);
        } catch (Exception e) {
            logger.error(MsgUtils.format("同步家族数据到family-feature失败，家族ID:{}", familyId), e);
        }

    }
}
