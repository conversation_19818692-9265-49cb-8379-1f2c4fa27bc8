package com.tuowan.yeliao.social.data.enums.friend;

import com.easyooo.framework.common.util.EnumUtils;
import com.tuowan.yeliao.commons.data.enums.general.ClientTouchType;
import com.tuowan.yeliao.commons.data.enums.social.ChatTipsShowType;

/**
 * 互动提醒类型
 * <p>
 * 触发场景
 * 用户发生互动后，生成提醒，等待用户下一次打开私信界面时触发
 *
 * <AUTHOR>
 * @date 2020/7/4 11:02
 */
public enum InteractTipsType implements EnumUtils.IDEnum {

    UpdateHeadPic("RealHead", "上传头像", "对方对你很感兴趣哦，上传头像能获得更多的喜爱哦", "上传头像", ClientTouchType.EditMineHomePage, ChatTipsShowType.Sender),
    UpdateCover("VideoCall", "上传更多照片", "你的相册吸引了ta，上传更多照片能获得更多搭讪哦", "上传更多照片", ClientTouchType.EditMineHomePage, ChatTipsShowType.Sender),
    ;

    private String id;
    private String desc;
    private String textTpl;
    private String touchText;
    private ClientTouchType touchType;
    private ChatTipsShowType showType;


    InteractTipsType(String id, String desc) {
        this.id = id;
        this.desc = desc;
    }

    InteractTipsType(String id, String desc, String textTpl, String touchText, ClientTouchType touchType, ChatTipsShowType showType) {
        this.id = id;
        this.desc = desc;
        this.textTpl = textTpl;
        this.touchText = touchText;
        this.touchType = touchType;
        this.showType = showType;
    }

    @Override
    public String getId() {
        return this.id;
    }

    @Override
    public String getDesc() {
        return this.desc;
    }

    public String getTextTpl() {
        return textTpl;
    }

    public String getTouchText() {
        return touchText;
    }

    public ClientTouchType getTouchType() {
        return touchType;
    }

    public ChatTipsShowType getShowType() {
        return showType;
    }
}
