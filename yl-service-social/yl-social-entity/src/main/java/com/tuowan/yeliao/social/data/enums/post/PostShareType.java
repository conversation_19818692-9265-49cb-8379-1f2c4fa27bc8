package com.tuowan.yeliao.social.data.enums.post;

import com.easyooo.framework.common.util.EnumUtils;

/**
 * 动态分享的内容类型
 *
 * <AUTHOR>
 * @date 2020/11/25 20:13
 */
public enum PostShareType implements EnumUtils.IDEnum {

    PrivateMsg("PM", "私聊页面");

    private String id;
    private String desc;

    PostShareType(String id, String desc) {
        this.id = id;
        this.desc = desc;
    }

    @Override
    public String getId() {
        return id;
    }

    @Override
    public String getDesc() {
        return desc;
    }
}
