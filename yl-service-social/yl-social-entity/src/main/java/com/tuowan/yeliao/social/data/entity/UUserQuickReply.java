/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.social.data.entity;

import com.easyooo.framework.cache.annotations.Cache;
import com.easyooo.framework.cache.annotations.Group;
import com.easyooo.framework.cache.annotations.KeyProperty;
import com.tuowan.yeliao.commons.data.enums.general.ReviewResultType;
import com.tuowan.yeliao.commons.data.enums.general.Status;
import org.apache.ibatis.type.Alias;

import java.util.Date;

@Alias("UUserQuickReply")
@Cache(expire = 24 * 3600)
public class UUserQuickReply {
    /** 快捷回复ID */
    @KeyProperty
    private Long replyId;

    /** 用户ID */
    @Group
    private Long userId;

    /** 内容 */
    private String content;

    /** 创建时间 */
    private Date createTime;

    /** 状态：E(Enable，有效)， D(Disable，无效) */
    private Status status;

    /** 已使用次数 */
    private Integer usedTimes;

    /** 审核状态 */
    private ReviewResultType auditStatus;

    /** 审核人 */
    private Long auditUserId;

    /** 审核时间 */
    private Date auditTime;

    /** 审核原因 */
    private String auditReason;


    public UUserQuickReply() {
        
    }

    /** 根据主键初始化实例 **/
    public UUserQuickReply(Long replyId) {
        this.replyId = replyId;
    }

    public Long getReplyId() {
        return replyId;
    }

    public void setReplyId(Long replyId) {
        this.replyId = replyId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content == null ? null : content.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Status getStatus() {
        return status;
    }

    public void setStatus(Status status) {
        this.status = status;
    }

    public Integer getUsedTimes() {
        return usedTimes;
    }

    public void setUsedTimes(Integer usedTimes) {
        this.usedTimes = usedTimes;
    }

    public ReviewResultType getAuditStatus() {
        return auditStatus;
    }

    public void setAuditStatus(ReviewResultType auditStatus) {
        this.auditStatus = auditStatus;
    }

    public Long getAuditUserId() {
        return auditUserId;
    }

    public void setAuditUserId(Long auditUserId) {
        this.auditUserId = auditUserId;
    }

    public Date getAuditTime() {
        return auditTime;
    }

    public void setAuditTime(Date auditTime) {
        this.auditTime = auditTime;
    }

    public String getAuditReason() {
        return auditReason;
    }

    public void setAuditReason(String auditReason) {
        this.auditReason = auditReason;
    }
}