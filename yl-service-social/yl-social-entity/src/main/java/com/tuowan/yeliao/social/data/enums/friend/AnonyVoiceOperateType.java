package com.tuowan.yeliao.social.data.enums.friend;

import com.easyooo.framework.common.util.EnumUtils;

/**
 * 匿名语音操作类型枚举定义
 *
 * <AUTHOR>
 * @date 2020/8/19 10:09
 */
public enum AnonyVoiceOperateType implements EnumUtils.IDEnum {

    Open("O", "公开身份"),
    Unveil("U", "揭幕身份");

    private String id;
    private String desc;

    AnonyVoiceOperateType(String id, String desc) {
        this.id = id;
        this.desc = desc;
    }

    @Override
    public String getId() {
        return this.id;
    }

    @Override
    public String getDesc() {
        return this.desc;
    }
}
