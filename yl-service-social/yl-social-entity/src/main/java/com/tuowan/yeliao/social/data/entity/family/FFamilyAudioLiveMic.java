/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.social.data.entity.family;

import com.easyooo.framework.cache.annotations.KeyProperty;
import com.tuowan.yeliao.social.data.enums.family.AudioLiveMicOnType;
import org.apache.ibatis.type.Alias;

import java.util.Date;

@Alias("FFamilyAudioLiveMic")
public class FFamilyAudioLiveMic {
    /** 上麦用户ID */
    @KeyProperty
    private Long userId;

    /** 家族ID */
    private Integer familyId;

    /** 直播流水号 */
    private Long showTid;

    /** 麦序 */
    private Integer micNo;

    /** 创建时间，上麦时间 */
    private Date createTime;

    /** 上麦类型 */
    private AudioLiveMicOnType onType;

    public FFamilyAudioLiveMic() {
        
    }

    /** 根据主键初始化实例 **/
    public FFamilyAudioLiveMic(Long userId) {
        this.userId = userId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Integer getFamilyId() {
        return familyId;
    }

    public void setFamilyId(Integer familyId) {
        this.familyId = familyId;
    }

    public Long getShowTid() {
        return showTid;
    }

    public void setShowTid(Long showTid) {
        this.showTid = showTid;
    }

    public Integer getMicNo() {
        return micNo;
    }

    public void setMicNo(Integer micNo) {
        this.micNo = micNo;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public AudioLiveMicOnType getOnType() {
        return onType;
    }

    public void setOnType(AudioLiveMicOnType onType) {
        this.onType = onType;
    }
}