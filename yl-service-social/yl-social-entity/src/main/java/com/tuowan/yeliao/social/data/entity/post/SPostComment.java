/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.social.data.entity.post;

import com.easyooo.framework.cache.annotations.Cache;
import com.easyooo.framework.cache.annotations.KeyProperty;
import com.tuowan.yeliao.commons.core.enums.general.ReviewStatus;
import org.apache.ibatis.type.Alias;

import java.util.Date;

@Alias("SPostComment")
@Cache(expire = 24 * 3600)
public class SPostComment {
    /**
     * 评论ID
     */
    @KeyProperty
    private Long commentId;

    /**
     * 动态ID
     */
    private Long postId;

    /**
     * 父级评论ID，-1表示一级评论
     */
    private Long parentId;

    /**
     * 评论人ID
     */
    private Long userId;

    /**
     * 评论内容
     */
    private String content;

    /**
     * 评论人回复的用户ID
     */
    private Long replyUserId;

    /**
     * 回复数
     */
    private Integer replyNum;

    /**
     * 点赞次数
     */
    private Integer praiseNum;

    /**
     * 审核状态
     */
    private ReviewStatus status;

    /**
     * 评论时间
     */
    private Date createTime;

    public SPostComment() {

    }

    /**
     * 根据主键初始化实例
     **/
    public SPostComment(Long commentId) {
        this.commentId = commentId;
    }

    public Long getCommentId() {
        return commentId;
    }

    public void setCommentId(Long commentId) {
        this.commentId = commentId;
    }

    public Long getPostId() {
        return postId;
    }

    public void setPostId(Long postId) {
        this.postId = postId;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Long getReplyUserId() {
        return replyUserId;
    }

    public void setReplyUserId(Long replyUserId) {
        this.replyUserId = replyUserId;
    }

    public Integer getReplyNum() {
        return replyNum;
    }

    public void setReplyNum(Integer replyNum) {
        this.replyNum = replyNum;
    }

    public Integer getPraiseNum() {
        return praiseNum;
    }

    public void setPraiseNum(Integer praiseNum) {
        this.praiseNum = praiseNum;
    }

    public ReviewStatus getStatus() {
        return status;
    }

    public void setStatus(ReviewStatus status) {
        this.status = status;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}