package com.tuowan.yeliao.social.data.enums.family;

import com.easyooo.framework.common.util.EnumUtils;

/**
 * 语音房主题
 *
 * <AUTHOR>
 * @date 2021/8/27 17:17
 */
public enum AudioLiveThemeType implements EnumUtils.IDEnum {

    Chat("C", "闲聊唠嗑", "#AE6DE5", 1),
    <PERSON>("I", "K歌大赛", "#7377FF", 2),
    Friends("F", "相亲交友", "#FF6E96", 3),
    Sale("S", "拍卖现场", "#FF9A09", 4),
    ;

    private String id;
    private String desc;
    private String color;
    private Integer order;


    AudioLiveThemeType(String id, String desc, String color, Integer order) {
        this.id = id;
        this.desc = desc;
        this.color = color;
        this.order = order;
    }

    @Override
    public String getId() {
        return this.id;
    }

    @Override
    public String getDesc() {
        return this.desc;
    }

    public String getColor() {
        return color;
    }

    public Integer getOrder() {
        return order;
    }
}
