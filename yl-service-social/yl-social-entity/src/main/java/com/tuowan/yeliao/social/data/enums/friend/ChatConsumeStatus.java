package com.tuowan.yeliao.social.data.enums.friend;

import com.easyooo.framework.common.util.EnumUtils;

/**
 * 聊天消费状态枚举
 *
 * <AUTHOR>
 * @date 2020/7/4 11:12
 */
public enum ChatConsumeStatus implements EnumUtils.IDEnum {

    Prepay("P", "预扣"),
    CancelPrepay("C", "取消预扣"),
    WaitWithdraw("W", "待提成"),
    Success("S", "完成"),
    Refund("R", "扣费退回"),
    ;

    private String id;
    private String desc;

    ChatConsumeStatus(String id, String desc) {
        this.id = id;
        this.desc = desc;
    }

    @Override
    public String getId() {
        return this.id;
    }

    @Override
    public String getDesc() {
        return this.desc;
    }

}
