/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.social.data.entity.post;

import com.easyooo.framework.cache.annotations.KeyProperty;
import org.apache.ibatis.type.Alias;

import java.util.Date;

@Alias("SPostRewardDetail")
public class SPostRewardDetail {
    /** 创建时间 */
    @KeyProperty
    private Date createTime;

    /** 流水号 */
    @KeyProperty
    private Long tid;

    /** 动态ID */
    private Long postId;

    /** 用户ID */
    private Long userId;

    /** 礼物id */
    private Integer giftId;

    /** 打赏数量 */
    private Integer rewardCnt;

    /** 消耗金币 */
    private Long totalBeans;

    public SPostRewardDetail() {
        
    }

    /** 根据主键初始化实例 **/
    public SPostRewardDetail(Date createTime, Long tid) {
        this.createTime = createTime;
        this.tid = tid;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getTid() {
        return tid;
    }

    public void setTid(Long tid) {
        this.tid = tid;
    }

    public Long getPostId() {
        return postId;
    }

    public void setPostId(Long postId) {
        this.postId = postId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Integer getGiftId() {
        return giftId;
    }

    public void setGiftId(Integer giftId) {
        this.giftId = giftId;
    }

    public Integer getRewardCnt() {
        return rewardCnt;
    }

    public void setRewardCnt(Integer rewardCnt) {
        this.rewardCnt = rewardCnt;
    }

    public Long getTotalBeans() {
        return totalBeans;
    }

    public void setTotalBeans(Long totalBeans) {
        this.totalBeans = totalBeans;
    }
}