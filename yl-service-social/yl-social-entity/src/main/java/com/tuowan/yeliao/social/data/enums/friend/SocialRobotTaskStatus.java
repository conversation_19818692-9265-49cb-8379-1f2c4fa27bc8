package com.tuowan.yeliao.social.data.enums.friend;

import com.easyooo.framework.common.util.EnumUtils;

/**
 * 社交机器人任务状态
 *
 * <AUTHOR>
 * @date 2020/11/23 11:47
 */
public enum SocialRobotTaskStatus implements EnumUtils.IDEnum {

    Process("P", "执行中"),
    Cancel("C", "已取消"),
    Success("S", "执行完成"),
    ;

    private String id;
    private String desc;

    SocialRobotTaskStatus(String id, String desc) {
        this.id = id;

        this.desc = desc;
    }

    @Override
    public String getId() {
        return id;
    }

    @Override
    public String getDesc() {
        return desc;
    }
}
