/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.social.data.entity.family;

import com.easyooo.framework.cache.annotations.KeyProperty;
import com.tuowan.yeliao.social.data.enums.family.AudioLiveThemeType;
import com.tuowan.yeliao.social.data.enums.family.FamilyActionType;
import org.apache.ibatis.type.Alias;

import java.util.Date;

@Alias("FFamilyAudioLive")
public class FFamilyAudioLive {
    /** 家族ID */
    @KeyProperty
    private Integer familyId;

    /** 直播标题 */
    private String showTitle;

    /** 开播流水号 */
    private Long showTid;

    /** 总麦数 */
    private Integer micCount;

    /** 创建时间 */
    private Date createTime;

    /** 家族动作 */
    private FamilyActionType actionType;

    /** 家族主题 */
    private AudioLiveThemeType themeType;

    /** 创建人 */
    private Long creator;

    public FFamilyAudioLive() {
        
    }

    /** 根据主键初始化实例 **/
    public FFamilyAudioLive(Integer familyId) {
        this.familyId = familyId;
    }

    public Integer getFamilyId() {
        return familyId;
    }

    public void setFamilyId(Integer familyId) {
        this.familyId = familyId;
    }

    public String getShowTitle() {
        return showTitle;
    }

    public void setShowTitle(String showTitle) {
        this.showTitle = showTitle == null ? null : showTitle.trim();
    }

    public Long getShowTid() {
        return showTid;
    }

    public void setShowTid(Long showTid) {
        this.showTid = showTid;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getMicCount() {
        return micCount;
    }

    public void setMicCount(Integer micCount) {
        this.micCount = micCount;
    }

    public FamilyActionType getActionType() {
        return actionType;
    }

    public void setActionType(FamilyActionType actionType) {
        this.actionType = actionType;
    }

    public AudioLiveThemeType getThemeType() {
        return themeType;
    }

    public void setThemeType(AudioLiveThemeType themeType) {
        this.themeType = themeType;
    }

    public Long getCreator() {
        return creator;
    }

    public void setCreator(Long creator) {
        this.creator = creator;
    }
}