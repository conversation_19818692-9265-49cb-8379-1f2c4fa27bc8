/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.social.data.entity;

import com.easyooo.framework.cache.annotations.KeyProperty;
import org.apache.ibatis.type.Alias;

import java.util.Date;

@Alias("FCpTaskDaily")
public class FCpTaskDaily {
    /** 情侣关系ID */
    @KeyProperty
    private String relationId;

    /** 任务代码 */
    @KeyProperty
    private String taskCode;

    /** 统计日期 */
    @KeyProperty
    private Date statDate;

    /** 完成时间 */
    private Date completeTime;

    /** 奖励的甜蜜值 */
    private Integer awardSweetValue;

    /** 奖励亲密度 */
    private Integer awardIntimateValue;

    public FCpTaskDaily() {
        
    }

    /** 根据主键初始化实例 **/
    public FCpTaskDaily(String relationId, String taskCode, Date statDate) {
        this.relationId = relationId;
        this.taskCode = taskCode;
        this.statDate = statDate;
    }

    public String getRelationId() {
        return relationId;
    }

    public void setRelationId(String relationId) {
        this.relationId = relationId == null ? null : relationId.trim();
    }

    public String getTaskCode() {
        return taskCode;
    }

    public void setTaskCode(String taskCode) {
        this.taskCode = taskCode == null ? null : taskCode.trim();
    }

    public Date getStatDate() {
        return statDate;
    }

    public void setStatDate(Date statDate) {
        this.statDate = statDate;
    }

    public Date getCompleteTime() {
        return completeTime;
    }

    public void setCompleteTime(Date completeTime) {
        this.completeTime = completeTime;
    }

    public Integer getAwardSweetValue() {
        return awardSweetValue;
    }

    public void setAwardSweetValue(Integer awardSweetValue) {
        this.awardSweetValue = awardSweetValue;
    }

    public Integer getAwardIntimateValue() {
        return awardIntimateValue;
    }

    public void setAwardIntimateValue(Integer awardIntimateValue) {
        this.awardIntimateValue = awardIntimateValue;
    }
}