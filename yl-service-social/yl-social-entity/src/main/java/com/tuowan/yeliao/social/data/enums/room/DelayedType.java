package com.tuowan.yeliao.social.data.enums.room;

import com.easyooo.framework.common.util.EnumUtils;

public enum DelayedType implements EnumUtils.IDEnum {
    Min0("M0", 0, "立马可抢", false),
    Min3("M3", 3, "3分钟延时", true),
    ;

    private String id;
    private Integer minu;
    private String desc;
    private boolean delayed;

    DelayedType(String id, Integer minu, String desc, boolean delayed) {
        this.id = id;
        this.minu = minu;
        this.desc = desc;
        this.delayed = delayed;
    }

    @Override
    public String getId() {
        return id;
    }

    public Integer getMinu() {
        return minu;
    }

    @Override
    public String getDesc() {
        return desc;
    }

    public boolean isDelayed() {
        return delayed;
    }
}
