package com.tuowan.yeliao.social.data.enums.friend;

import com.easyooo.framework.common.util.EnumUtils;

/**
 * 音视频通话结束类型枚举定义
 *
 * <AUTHOR>
 * @date 2020/7/7 11:46
 */
public enum NetCallFinishType implements EnumUtils.IDEnum {

    Normal("N", "正常结束"),
    Timeout("H", "心跳超时"),
    Balance("B", "余额不足"),
    Illegal("I", "通话违规"),
    Notify("T", "声网通知"),
    Error("E", "系统错误"),
    /** 系统自动结束的 例如涉黄 */
    System("S", "系统强制结束"),
    /** 人工停止的; 直接结束通话;封禁账号;封禁音频 */
    Artificial("A", "人工强制结束"),
    OtherNoJoin("O", "另一个人没加入频道"),
    NoFace("F", "无人脸结束"),
    ;

    private String id;
    private String desc;

    NetCallFinishType(String id, String desc) {
        this.id = id;
        this.desc = desc;
    }

    @Override
    public String getId() {
        return this.id;
    }

    @Override
    public String getDesc() {
        return this.desc;
    }
}
