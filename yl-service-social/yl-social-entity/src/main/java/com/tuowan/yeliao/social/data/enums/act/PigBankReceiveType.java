package com.tuowan.yeliao.social.data.enums.act;

import com.easyooo.framework.common.util.EnumUtils;

public enum PigBankReceiveType implements EnumUtils.IDEnum {

    Received("RD", "已领取"),
    WaitReceive("RW", "待领取"),
    NoReceive("RN", "还不能领取"),
    ;

    private String id;
    private String desc;

    PigBankReceiveType(String id, String desc) {
        this.id = id;
        this.desc = desc;
    }

    @Override
    public String getId() {
        return id;
    }

    @Override
    public String getDesc() {
        return desc;
    }
}
