/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.social.data.entity;

import com.easyooo.framework.cache.annotations.KeyProperty;
import com.tuowan.yeliao.social.data.enums.friend.ChatInviteType;
import org.apache.ibatis.type.Alias;

import java.util.Date;

@Alias("FChatInvite")
public class FChatInvite {
    /** 邀请记录ID */
    @KeyProperty
    private Integer logId;

    /** 用户ID */
    private Long userId;

    /** 好友ID */
    private Long friendId;

    /** ChatInviteType 邀请类型 V 语音 O 视频 R 实名认证 */
    private ChatInviteType inviteType;

    /** 记录创建时间 */
    private Date createTime;

    public FChatInvite() {
        
    }

    /** 根据主键初始化实例 **/
    public FChatInvite(Integer logId) {
        this.logId = logId;
    }

    public FChatInvite(Long userId, Long friendId, ChatInviteType inviteType) {
        this.userId = userId;
        this.friendId = friendId;
        this.inviteType = inviteType;
        this.createTime = new Date();
    }

    public Integer getLogId() {
        return logId;
    }

    public void setLogId(Integer logId) {
        this.logId = logId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getFriendId() {
        return friendId;
    }

    public void setFriendId(Long friendId) {
        this.friendId = friendId;
    }

    public ChatInviteType getInviteType() {
        return inviteType;
    }

    public void setInviteType(ChatInviteType inviteType) {
        this.inviteType = inviteType;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}