/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.social.data.entity;

import com.easyooo.framework.cache.annotations.KeyProperty;
import com.tuowan.yeliao.commons.data.enums.social.CpMomentType;
import org.apache.ibatis.type.Alias;

import java.util.Date;

@Alias("FRelationCpMoment")
public class FRelationCpMoment {
    /**
     * 情侣动态ID
     */
    @KeyProperty
    private Long momentId;

    /**
     * 动态类型
     */
    private CpMomentType type;

    /**
     * 情侣关系ID
     */
    private String relationId;

    /**
     * 动态内容
     */
    private String content;

    /**
     * 创建时间
     */
    private Date createTime;

    public FRelationCpMoment() {

    }

    /**
     * 根据主键初始化实例
     **/
    public FRelationCpMoment(Long momentId) {
        this.momentId = momentId;
    }

    public Long getMomentId() {
        return momentId;
    }

    public CpMomentType getType() {
        return type;
    }

    public void setType(CpMomentType type) {
        this.type = type;
    }

    public void setMomentId(Long momentId) {
        this.momentId = momentId;
    }

    public String getRelationId() {
        return relationId;
    }

    public void setRelationId(String relationId) {
        this.relationId = relationId;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content == null ? null : content.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}