package com.tuowan.yeliao.social.data.enums.family;

import com.easyooo.framework.common.util.EnumUtils;

/**
 * 家族群聊加入类型
 *
 * <AUTHOR>
 * @date 2021/7/6 19:16
 */
public enum FamilyJoinStatus implements EnumUtils.IDEnum {

    Success("S", "加入成功"),
    Wait("W", "等待审核");

    private final String id;
    private final String desc;

    FamilyJoinStatus(String id, String desc) {
        this.id = id;
        this.desc = desc;
    }

    @Override
    public String getId() {
        return this.id;
    }

    @Override
    public String getDesc() {
        return this.desc;
    }
}
