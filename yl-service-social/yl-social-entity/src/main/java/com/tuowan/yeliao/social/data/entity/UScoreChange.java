/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.social.data.entity;

import com.easyooo.framework.cache.annotations.KeyProperty;
import java.util.Date;

import com.tuowan.yeliao.social.data.enums.user.ScoreChangeType;
import org.apache.ibatis.type.Alias;

@Alias("UScoreChange")
public class UScoreChange {
    /** 记录ID */
    @KeyProperty
    private Long recordId;

    /** 用户ID */
    private Long userId;

    /** 变化类型 */
    private ScoreChangeType type;

    /** 变化值 */
    private Long change;

    /** 描述 */
    private String remark;

    /** 创建时间 */
    private Date createTime;

    public UScoreChange() {
        
    }

    /** 根据主键初始化实例 **/
    public UScoreChange(Long recordId) {
        this.recordId = recordId;
    }

    public Long getRecordId() {
        return recordId;
    }

    public void setRecordId(Long recordId) {
        this.recordId = recordId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public ScoreChangeType getType() {
        return type;
    }

    public void setType(ScoreChangeType type) {
        this.type = type;
    }

    public Long getChange() {
        return change;
    }

    public void setChange(Long change) {
        this.change = change;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}