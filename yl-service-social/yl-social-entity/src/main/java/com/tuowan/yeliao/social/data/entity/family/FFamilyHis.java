/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.social.data.entity.family;

import com.tuowan.yeliao.commons.core.enums.general.BoolType;
import com.tuowan.yeliao.commons.data.enums.general.Status;
import com.tuowan.yeliao.social.data.enums.family.FamilyJoinCondition;
import com.easyooo.framework.cache.annotations.KeyProperty;
import org.apache.ibatis.type.Alias;

import java.util.Date;

@Alias("FFamilyHis")
public class FFamilyHis {
    /** 记录ID */
    @KeyProperty
    private Long logId;

    /** 家族ID */
    private Integer familyId;

    /** 家族名称 */
    private String familyName;

    /** 家族简介 */
    private String familyDesc;

    /** 家族封面 */
    private String coverPic;

    /** 成员数量 */
    private Integer memberNum;

    /** 加入的条件 */
    private FamilyJoinCondition joinCondition;

    /** 族长ID */
    private Long leaderId;

    /** 禁言状态：都可发言(E)、全员禁言(D) */
    private Status allMuteStatus;

    /** 家族标签ID */
    private String tagIds;

    /** 家族创建时间 */
    private Date createTime;

    /** 移入历史原因 */
    private String reason;

    /** 移入记录创建时间 */
    private Date hisCreateTime;

    /** 是否已归档 */
    private BoolType archive;

    /** 家族等级 */
    private Integer familyLevel;

    /** 总贡献值 */
    private Long totalContribution;

    /** 家族创建人 */
    private Long creator;

    /** 移入记录创建人 */
    private Long hisCreator;

    /** 家族所在城市 */
    private String city;

    /** 纬度值 */
    private String lat;

    /** 经度值 */
    private String lng;

    /** 冻结状态：T 冻结，F 未冻结 */
    private BoolType freeze;

    public FFamilyHis() {
        
    }

    /** 根据主键初始化实例 **/
    public FFamilyHis(Long logId) {
        this.logId = logId;
    }

    public Long getLogId() {
        return logId;
    }

    public void setLogId(Long logId) {
        this.logId = logId;
    }

    public Integer getFamilyId() {
        return familyId;
    }

    public void setFamilyId(Integer familyId) {
        this.familyId = familyId;
    }

    public String getFamilyName() {
        return familyName;
    }

    public void setFamilyName(String familyName) {
        this.familyName = familyName == null ? null : familyName.trim();
    }

    public String getFamilyDesc() {
        return familyDesc;
    }

    public void setFamilyDesc(String familyDesc) {
        this.familyDesc = familyDesc == null ? null : familyDesc.trim();
    }

    public String getCoverPic() {
        return coverPic;
    }

    public void setCoverPic(String coverPic) {
        this.coverPic = coverPic == null ? null : coverPic.trim();
    }

    public Integer getMemberNum() {
        return memberNum;
    }

    public void setMemberNum(Integer memberNum) {
        this.memberNum = memberNum;
    }

    public FamilyJoinCondition getJoinCondition() {
        return joinCondition;
    }

    public void setJoinCondition(FamilyJoinCondition joinCondition) {
        this.joinCondition = joinCondition;
    }

    public Long getLeaderId() {
        return leaderId;
    }

    public void setLeaderId(Long leaderId) {
        this.leaderId = leaderId;
    }

    public Status getAllMuteStatus() {
        return allMuteStatus;
    }

    public void setAllMuteStatus(Status allMuteStatus) {
        this.allMuteStatus = allMuteStatus;
    }

    public String getTagIds() {
        return tagIds;
    }

    public void setTagIds(String tagIds) {
        this.tagIds = tagIds;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason == null ? null : reason.trim();
    }

    public Date getHisCreateTime() {
        return hisCreateTime;
    }

    public void setHisCreateTime(Date hisCreateTime) {
        this.hisCreateTime = hisCreateTime;
    }

    public BoolType getArchive() {
        return archive;
    }

    public void setArchive(BoolType archive) {
        this.archive = archive;
    }

    public Integer getFamilyLevel() {
        return familyLevel;
    }

    public void setFamilyLevel(Integer familyLevel) {
        this.familyLevel = familyLevel;
    }

    public Long getTotalContribution() {
        return totalContribution;
    }

    public void setTotalContribution(Long totalContribution) {
        this.totalContribution = totalContribution;
    }

    public Long getCreator() {
        return creator;
    }

    public void setCreator(Long creator) {
        this.creator = creator;
    }

    public Long getHisCreator() {
        return hisCreator;
    }

    public void setHisCreator(Long hisCreator) {
        this.hisCreator = hisCreator;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getLat() {
        return lat;
    }

    public void setLat(String lat) {
        this.lat = lat;
    }

    public String getLng() {
        return lng;
    }

    public void setLng(String lng) {
        this.lng = lng;
    }

    public BoolType getFreeze() {
        return freeze;
    }

    public void setFreeze(BoolType freeze) {
        this.freeze = freeze;
    }
}