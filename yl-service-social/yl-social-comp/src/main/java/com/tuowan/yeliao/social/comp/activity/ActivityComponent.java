package com.tuowan.yeliao.social.comp.activity;

import com.easyooo.framework.common.util.*;
import com.tuowan.yeliao.commons.config.redis.RedisKey;
import com.tuowan.yeliao.commons.config.redis.template.SocialRedisTemplate;
import com.tuowan.yeliao.commons.core.enums.redis.SocialKeyDefine;
import com.tuowan.yeliao.commons.core.exception.BusiException;
import com.tuowan.yeliao.commons.data.enums.general.ClientTouchType;
import com.tuowan.yeliao.commons.data.manager.user.UserInfoManager;
import com.tuowan.yeliao.commons.data.utils.TimeRangeUtils;
import com.tuowan.yeliao.social.comp.activity.dto.SpringTaskConfig;
import com.tuowan.yeliao.social.comp.family.FamilyComponent;
import com.tuowan.yeliao.social.data.manager.family.FamilyManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 活动组件
 *
 * <AUTHOR>
 * @date 2021/8/11 14:58
 */
@Component
public class ActivityComponent {

    public static final TimeRangeUtils.TimeRange ACTIVITY_TIME = TimeRangeUtils.createTimeRange("2022-01-31 00:00:00", "2022-02-07 00:00:00");

    public static final Map<Integer, String> DISCOUNT_GIFT_MAP = new HashMap<>();

    /**
     * 万圣节头像
     */
    public static final String ACTIVITY_HEAD_PIC = "config/icon/halloween.png";

    public static final Integer TASK_GIFT_ID = 71;

    public static final Integer EXCHANGE_BUBBLE_ID = 573;

    public static final Integer EXCHANGE_CAR_ID = 574;

    // 春节活动任务Code
    public static final String TASK_RECHARGE = "Recharge";
    public static final String TASK_RECEIVE_FAMILY_BEANS = "ReceiveFamilyBeans";
    public static final String TASK_SEND_FAMILY_BEANS = "SendFamilyBeans";
    public static final String TASK_NET_CALL = "NetCall";
    public static final String TASK_RECEIVE_GIFT = "ReceiveGift";
    public static final String TASK_SEND_GIFT = "SendGift";

    public static final Map<String, SpringTaskConfig> TASK_CONFIG_MAP = new HashMap<>();

    static {
        TASK_CONFIG_MAP.put(TASK_RECHARGE, new SpringTaskConfig(TASK_RECHARGE, "每充值3次", 3L, "充值奖励", 1, ClientTouchType.Recharge));
        TASK_CONFIG_MAP.put(TASK_RECEIVE_FAMILY_BEANS, new SpringTaskConfig(TASK_RECEIVE_FAMILY_BEANS, "家族内每累计收到礼物达到1000金币", 1000L, "家族收礼奖励", 2));
        TASK_CONFIG_MAP.put(TASK_SEND_FAMILY_BEANS, new SpringTaskConfig(TASK_SEND_FAMILY_BEANS, "家族内每累计赠送礼物达到1000金币", 1000L, "家族送礼奖励", 3, ClientTouchType.EnterFamily));
        TASK_CONFIG_MAP.put(TASK_NET_CALL, new SpringTaskConfig(TASK_NET_CALL, "每累计视频通话30分钟", 30L, "视频奖励", 4));
        TASK_CONFIG_MAP.put(TASK_RECEIVE_GIFT, new SpringTaskConfig(TASK_RECEIVE_GIFT, "每累计收到3个\"新春祝福\"礼物", 3L, "收到\"新春祝福\"奖励", 5));
        TASK_CONFIG_MAP.put(TASK_SEND_GIFT, new SpringTaskConfig(TASK_SEND_GIFT, "每累计赠送3个\"新春祝福\"礼物", 3L, "送出\"新春祝福\"奖励", 6));
    }


    static {
        DISCOUNT_GIFT_MAP.put(101, "1.9折");
        DISCOUNT_GIFT_MAP.put(102, "5折");
        DISCOUNT_GIFT_MAP.put(103, "5折");
        DISCOUNT_GIFT_MAP.put(104, "5折");
        DISCOUNT_GIFT_MAP.put(105, "5折");
        DISCOUNT_GIFT_MAP.put(106, "4折");
        DISCOUNT_GIFT_MAP.put(107, "4折");

    }

    @Autowired
    private SocialRedisTemplate socialRedisTemplate;
    @Autowired
    private UserInfoManager userInfoManager;
    @Autowired
    private FamilyManager familyManager;
    @Autowired
    private FamilyComponent familyComponent;

    /**
     * 是否活动中
     */
    public boolean isActivityProcessing(Date date) {
        return DateUtils.isInTimeRange(ACTIVITY_TIME.getStartTime(), ACTIVITY_TIME.getEndTime(), date);
    }

    /**
     * 获取带领去的红包总数
     *
     * @param userId
     * @return
     */
    public Long getWaitReceiveRedPacketCount(Long userId) {
        Long waitReceiveCount = 0L;
        for (SpringTaskConfig config : TASK_CONFIG_MAP.values()) {
            Long receiveCount = socialRedisTemplate.hgetLong(buildTaskReceiveCountKey(userId), config.getTaskCode());
            Long nowCount = socialRedisTemplate.hgetLong(buildTaskProgressKey(userId), config.getTaskCode());
            // 待领取的进度值对应的可领取次数
            waitReceiveCount += (nowCount - receiveCount * config.getNeedCount()) / config.getNeedCount();
        }
        return waitReceiveCount;
    }


    /**
     * 领取任务奖励
     */
    public Long receiveTaskAward(Long userId, String taskCode) {
        SpringTaskConfig config = TASK_CONFIG_MAP.get(taskCode);
        Long receiveCount = socialRedisTemplate.hgetLong(buildTaskReceiveCountKey(userId), taskCode);
        Long nowCount = socialRedisTemplate.hgetLong(buildTaskProgressKey(userId), taskCode);
        // 待领取的进度值
        long waitReceiveCount = (nowCount - receiveCount * config.getNeedCount()) / config.getNeedCount();
        if (waitReceiveCount <= 0) {
            throw new BusiException("当前没有奖励可以领取哦~");
        }
        // 领取奖励
        socialRedisTemplate.hincrBy(buildCardCountKey(), userId.toString(), waitReceiveCount);
        socialRedisTemplate.expire(buildCardCountKey());
        socialRedisTemplate.hincrBy(buildRedPacketCountKey(), userId.toString(), waitReceiveCount);
        socialRedisTemplate.expire(buildRedPacketCountKey());
        // 记录领取次数
        socialRedisTemplate.hincrBy(buildTaskReceiveCountKey(userId), taskCode, waitReceiveCount);
        socialRedisTemplate.expire(buildTaskReceiveCountKey(userId));
        return waitReceiveCount;
    }

    /**
     * 获取可领取的红包次数
     */
    public Long getRedPacketCount(Long userId) {
        return socialRedisTemplate.hgetLong(buildRedPacketCountKey(), userId.toString());
    }

    public Long decrRedPacketCount(Long userId) {
        return socialRedisTemplate.hincrBy(buildRedPacketCountKey(), userId.toString(), -1L);
    }

    /**
     * 获取可领取的红包次数
     */
    public Long getCardCount(Long userId) {
        return socialRedisTemplate.hgetLong(buildCardCountKey(), userId.toString());
    }

    public Long decrCardCount(Long userId, Long count) {
        return socialRedisTemplate.hincrBy(buildCardCountKey(), userId.toString(), count);
    }

    /**
     * 处理春节任务进度
     */
    public void processSpringTask(String taskCode, Long userId, Long increment) {
        socialRedisTemplate.hincrBy(buildTaskProgressKey(userId), taskCode, increment);
        socialRedisTemplate.expire(buildTaskProgressKey(userId));
    }

    public RedisKey buildTaskProgressKey(Long userId) {
        return RedisKey.create(SocialKeyDefine.SpringTaskProgress, userId);
    }

    public RedisKey buildTaskReceiveCountKey(Long userId) {
        return RedisKey.create(SocialKeyDefine.SpringTaskReceiveCount, userId);
    }

    public RedisKey buildRedPacketCountKey() {
        return RedisKey.create(SocialKeyDefine.SpringTaskRedPacketCount);
    }

    public RedisKey buildCardCountKey() {
        return RedisKey.create(SocialKeyDefine.SpringTaskCardCount);
    }

    /**
     * 用户被踢出家族后，如果有活动，执行活动相关操作
     *
     * @param userId
     */
    public void processAfterOutFamily(Long userId, Integer familyId) {
        if (isActivityProcessing(DateUtils.nowTime())) {
            socialRedisTemplate.zrem(buildFamilyUserRankKey(familyId, DateUtils.nowTime().getTime()), userId.toString());
        }
    }

    public RedisKey buildFamilyUserRankKey(Integer familyId, Long time) {
        return RedisKey.create(SocialKeyDefine.ChristmasFamilyUserDayRank, familyId, RedisUtils.formatCacheDay(time));
    }


    public String getDiscount(Integer giftId) {
        String discount = DISCOUNT_GIFT_MAP.get(giftId);
        if (StringUtils.isNotEmpty(discount)) {
            return MsgUtils.format("({})", discount);
        }
        return "";
    }


    public Long getActTtl() {
        Date nowTime = DateUtils.nowTime();
        if (nowTime.before(ACTIVITY_TIME.getStartTime())) {
            return -1L;
        }
        return Math.max(0, DateUtils.getDiffSeconds(nowTime, ACTIVITY_TIME.getEndTime()));
    }

    public Long getRankTtl(Date startTime, Date endTime) {
        Date nowTime = DateUtils.nowTime();
        if (nowTime.before(startTime)) {
            return -1L;
        }
        return Math.max(0, DateUtils.getDiffSeconds(nowTime, endTime));
    }


    /**
     * 获取置顶的5条头条消息
     *
     * @return
     */
    public List<String> getActTopMsg() {
        List<String> topList = socialRedisTemplate.lrange(buildActMsgKey(), 0L, -1L);
        if (ListUtils.isNotEmpty(topList)) {
            return topList;
        }
        return socialRedisTemplate.lrange(buildActMsgKey(), 0L, 9L);
    }

    public void cachePassMsg(String msg) {
        RedisKey redisKey = buildActMsgKey();
        socialRedisTemplate.lpush(redisKey, msg);
        socialRedisTemplate.ltrim(redisKey, 0L, 9L);
        socialRedisTemplate.expire(redisKey);
    }

    public RedisKey buildActMsgKey() {
        return RedisKey.create(SocialKeyDefine.ActMsgList);
    }

}

