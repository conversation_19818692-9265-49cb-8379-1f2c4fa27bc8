package com.tuowan.yeliao.social.comp.activity.config;

import java.util.Arrays;
import java.util.List;

/**
 * 周收益奖励活动配置
 */
public class WeekIncomeAwardConfig {

    /** 周收益奖励活动奖励Code */
    public static final String AWARD_CODE = "WeekIncomeAwardAct";
    /** 发奖幂等控制字符串格式 WIAA_20230320_123456*/
    public static final String AWARD_IDPT_FORMAT = "WIAA_{}_{}";

    /** 活动梯度配置 */
    public static final List<WiaDTO> cfgList = Arrays.asList(
            new WiaDTO(1, 1600_0000L, 3200_0000L, 0.1),
            new WiaDTO(2, 3200_0000L, 12000_0000L, 0.15),
            new WiaDTO(3, 12000_0000L, 24000_0000L, 0.2),
            new WiaDTO(4, 24000_0000L, 36000_0000L, 0.25),
            new WiaDTO(5, 36000_0000L, Long.MAX_VALUE, 0.3)
    );

    /**
     * 活动配置对象
     */
    public static class WiaDTO{
        /** 奖励等级 */
        private Integer level;
        /** 最小额度 单位毫*/
        private Long min;
        /** 最大额度 单位毫； 取到等于 */
        private Long max;
        /** 奖励比例 */
        private Double awardRate;

        public WiaDTO(Integer level, Long min, Long max, Double awardRate) {
            this.level = level;
            this.min = min;
            this.max = max;
            this.awardRate = awardRate;
        }

        public Integer getLevel() {
            return level;
        }

        public void setLevel(Integer level) {
            this.level = level;
        }

        public Long getMin() {
            return min;
        }

        public void setMin(Long min) {
            this.min = min;
        }

        public Long getMax() {
            return max;
        }

        public void setMax(Long max) {
            this.max = max;
        }

        public Double getAwardRate() {
            return awardRate;
        }

        public void setAwardRate(Double awardRate) {
            this.awardRate = awardRate;
        }
    }
}
