package com.tuowan.yeliao.social.comp.chatmaster;

import com.easyooo.framework.common.util.MsgUtils;
import com.easyooo.framework.common.util.StringUtils;
import com.tuowan.yeliao.commons.comp.change.UserBusiChangeComponent;
import com.tuowan.yeliao.commons.comp.notice.NoticeComponent;
import com.tuowan.yeliao.commons.core.enums.errcode.ErrCodeType;
import com.tuowan.yeliao.commons.core.enums.general.ReviewStatus;
import com.tuowan.yeliao.commons.core.exception.DataException;
import com.tuowan.yeliao.commons.data.enums.config.NoticeSysType;
import com.tuowan.yeliao.social.data.entity.UUserExclusiveChatup;
import com.tuowan.yeliao.social.data.manager.chat.ExclusiveChatupManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Component
public class ExclusiveChatUpComponent {

    @Autowired
    private ExclusiveChatupManager exclusiveChatupManager;
    @Autowired
    private UserBusiChangeComponent userBusiChangeComponent;
    @Autowired
    private NoticeComponent noticeComponent;

    /**
     * 处理用户手动删除专属搭讪配置
     */
    public void dealUserRemoveEcu(Long cfgId) {
        UUserExclusiveChatup exclusiveChatup = exclusiveChatupManager.getExclusiveChatup(cfgId);
        if (Objects.isNull(exclusiveChatup)) {
            throw new DataException(ErrCodeType.RecordNotExists);
        }
        String oldValue = MsgUtils.format("type:{};cfgValue:{}", exclusiveChatup.getType().getId(), exclusiveChatup.getCfgValue());
        // 删除
        exclusiveChatupManager.deleteExclusiveChatUp(exclusiveChatup);
        // 操作记录
        userBusiChangeComponent.add("ECU-del-self", exclusiveChatup.getUserId(), oldValue, null);
    }

    /**
     * 处理专属搭讪配置 审核失败
     */
    public void dealEcuAuditRefuse(UUserExclusiveChatup chatup, String reason) {
        if (Objects.isNull(chatup)) {
            return;
        }
        // 删除
        exclusiveChatupManager.deleteExclusiveChatUp(chatup);
        // 操作记录
        String oldValue = MsgUtils.format("type:{};cfgValue:{}", chatup.getType(), chatup.getCfgValue());
        userBusiChangeComponent.add("ECU-del-sys", chatup.getUserId(), oldValue, null);
        // 发送系统通知
        Map<String, Object> params = new HashMap<>();
        params.put("reason", StringUtils.isEmpty(reason) ? "内容涉及违规！" : reason);
        params.put("type", chatup.getType().getDesc());
        noticeComponent.sendSystemNotice(chatup.getUserId(), NoticeSysType.UserExclusiveChatUpAuditRefuse, params);
    }

    /**
     * 处理专属搭讪配置 审核成功
     */
    public void dealEcuAuditPass(UUserExclusiveChatup chatup) {
        if (Objects.isNull(chatup)) {
            return;
        }
        // 修改状态
        UUserExclusiveChatup update = new UUserExclusiveChatup(chatup.getCfgId());
        update.setStatus(ReviewStatus.Pass);
        update.setUserId(chatup.getUserId());
        exclusiveChatupManager.updateExclusiveChatUp(update);
        // 操作记录
        String oldValue = MsgUtils.format("cfgId:{};status:{}", chatup.getCfgId(), chatup.getStatus());
        userBusiChangeComponent.add("exclusiveChatUp-system", chatup.getUserId(), oldValue, ReviewStatus.Pass);
        // 发送系统通知
        Map<String, Object> params = new HashMap<>();
        params.put("type", chatup.getType().getDesc());
        noticeComponent.sendSystemNotice(chatup.getUserId(), NoticeSysType.UserExclusiveChatUpAuditPass, params);
    }
}
