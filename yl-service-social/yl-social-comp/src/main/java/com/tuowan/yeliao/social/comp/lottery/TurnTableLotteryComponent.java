package com.tuowan.yeliao.social.comp.lottery;

import com.easyooo.framework.common.util.JsonUtils;
import com.easyooo.framework.common.util.MsgUtils;
import com.easyooo.framework.common.util.StringUtils;
import com.tuowan.yeliao.commons.comp.grant.GrantComponent;
import com.tuowan.yeliao.commons.comp.grant.dto.AwardConfigDTO;
import com.tuowan.yeliao.commons.comp.grant.dto.AwardDTO;
import com.tuowan.yeliao.commons.comp.message.MessageComponent;
import com.tuowan.yeliao.commons.config.redis.RedisKey;
import com.tuowan.yeliao.commons.config.redis.template.SocialRedisTemplate;
import com.tuowan.yeliao.commons.context.GlobalUtils;
import com.tuowan.yeliao.commons.core.enums.busicode.BackCodeDefine;
import com.tuowan.yeliao.commons.core.enums.business.BusinessDataKey;
import com.tuowan.yeliao.commons.core.enums.general.BoolType;
import com.tuowan.yeliao.commons.core.enums.redis.SocialKeyDefine;
import com.tuowan.yeliao.commons.data.entity.config.TAwardDetail;
import com.tuowan.yeliao.commons.data.persistence.config.TAwardDetailMapper;
import com.tuowan.yeliao.commons.data.utils.HtmlUrlUtils;
import com.tuowan.yeliao.social.comp.lottery.enums.ConsumeLotteryType;
import com.tuowan.yeliao.social.comp.lottery.enums.RechargeLotteryType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 转盘抽奖组件
 *
 * <AUTHOR>
 * @date 2021/4/8 15:00
 */
@Component
public class TurnTableLotteryComponent {

    private Logger logger = LoggerFactory.getLogger(this.getClass());


    /** 抽奖奖励code前缀 */
    private static final String LOTTERY_AWARD_PREFIX = "TurntableLottery_%V2";

    /** 抽奖券父类ID */
    public static final Integer LOTTERY_PARENT_GOODS_ID = 8;

    @Autowired
    private TAwardDetailMapper awardDetailMapper;
    @Autowired
    private SocialRedisTemplate socialRedisTemplate;
    @Autowired
    private MessageComponent messageComponent;
    @Autowired
    private GrantComponent grantComponent;


    /**
     * 奖池code 封装奖品列表
     *
     * @return
     */
    public List<AwardConfigDTO> buildAwardListByLikeAward() {
        RedisKey configKey = buildAwardConfigKey();
        String jsonInfo = socialRedisTemplate.get(configKey);
        if (StringUtils.isNotEmpty(jsonInfo)) {
            return JsonUtils.deserializeAsList(jsonInfo, AwardConfigDTO.class);
        }
        List<AwardConfigDTO> awardConfigList = new ArrayList<>();
        List<TAwardDetail> detailList = awardDetailMapper.selectLikeCode(LOTTERY_AWARD_PREFIX);
        for (TAwardDetail detail : detailList) {
            Map<String, Object> map = JsonUtils.toJsonMap(detail.getExtJsonCfg());
            awardConfigList.add(new AwardConfigDTO(detail, (String) map.get("name"), (String) map.get("pic"), Integer.valueOf((String) map.get("order"))));
        }
        awardConfigList = awardConfigList.stream().sorted(Comparator.comparing(AwardConfigDTO::getOrder)).collect(Collectors.toList());
        socialRedisTemplate.set(configKey, JsonUtils.seriazileAsString(awardConfigList));
        return awardConfigList;
    }


    /**
     * 获取配置详情
     *
     * @param awardCode
     * @return
     */
    public List<AwardConfigDTO> getAwardDetailConfig(String awardCode) {
        List<AwardConfigDTO> awardConfigList = new ArrayList<>();
        List<TAwardDetail> detailList = awardDetailMapper.selectByCode(new TAwardDetail(awardCode));
        for (TAwardDetail detail : detailList) {
            Map<String, Object> map = JsonUtils.toJsonMap(detail.getExtJsonCfg());
            BoolType bigAward = StringUtils.isEmpty((String) map.get("bigAward")) ? null : BoolType.valueOf((String) map.get("bigAward"));
            AwardConfigDTO dto = new AwardConfigDTO(detail, (String) map.get("name"), (String) map.get("pic"), bigAward);
            awardConfigList.add(dto);
        }
        return awardConfigList;
    }

    /**
     * 存储中奖消息
     *
     * @param awardResult
     */
    public void saveRecentCacheMsg(List<AwardConfigDTO> awardResult) {
        String nickName = GlobalUtils.nickname();
        RedisKey msgKey = buildRecentAwardMsgKey();
        for (AwardConfigDTO dto : awardResult) {
            if (BoolType.True == dto.getBigAward()) {
                String awardProdName = dto.getProdName();
                socialRedisTemplate.lpush(msgKey, MsgUtils.format("{} 抽中了 {}", nickName, awardProdName));
            }
        }
        socialRedisTemplate.ltrim(msgKey, 0L, 4L);
    }

    /**
     * 充值金额获取抽奖券
     *
     * @param money
     * @return
     */
    public void saveGrantRechargeLottery(Long userId, Long money) {
        try {
            String awardCode = RechargeLotteryType.getAwardCode(money);
            if (StringUtils.isEmpty(awardCode)) {
                logger.info("充值奖励抽奖券未找到奖励配置,用户：{}，充值金额(分):{}", userId, money);
                return;
            }
            // 奖励抽奖券
            saveGrantLottery(userId, awardCode);
            logger.info("充值奖励抽奖券,奖励code:{},用户：{}，充值金额(分):{}", awardCode, userId, money);
        } catch (Exception e) {
            logger.error("充值奖励抽奖券报错,用户：{}，原因:{}", userId, e);
        }
    }

    /**
     * 消费获取抽奖券
     *
     * @param beans
     * @return
     */
    public void saveGrantConsumeLottery(Long userId, Long beans) {
        try {
            String awardCode = ConsumeLotteryType.getAwardCode(beans);
            if (StringUtils.isEmpty(awardCode)) {
                logger.info("消费奖励抽奖券未找到奖励配置,用户：{}，消费金额:{}", userId, beans);
                return;
            }
            // 奖励抽奖券
            saveGrantLottery(userId, awardCode);
            logger.info("消费奖励抽奖券,奖励code:{},用户：{}，消费金额:{}", awardCode, userId, beans);
        } catch (Exception e) {
            logger.error("消费奖励抽奖券报错,用户：{}，原因:{}", userId, e);
        }
    }

    /**
     * 奖励抽奖券
     *
     * @param userId
     * @param awardCode
     */
    private void saveGrantLottery(Long userId, String awardCode) {
        // 发抽奖券
        AwardDTO awardDTO = AwardDTO.create(awardCode);
        grantComponent.saveAutoGrant(userId, awardDTO);
        List<AwardConfigDTO> awardConfigList = getAwardDetailConfig(awardCode);
        AwardConfigDTO awardConfig = awardConfigList.get(0);
        // 获得抽奖券事件消息
        Map<BusinessDataKey, Object> extMap = new HashMap<>();
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("prodName", awardConfig.getProdName());
        paramMap.put("prodPic", awardConfig.getProdPic());
        // 大转盘地址
        paramMap.put("touchUrl", HtmlUrlUtils.getTurntableUrl());
        extMap.put(BusinessDataKey.CustomMap, paramMap);
        messageComponent.sendMsgToUserInContext(BackCodeDefine.AwardLottery, extMap, userId);
    }

    /**
     * 获取最近的中奖消息
     *
     * @return
     */
    public List<String> getRecentMsgList() {
        return socialRedisTemplate.lrange(buildRecentAwardMsgKey(), 0L, -1L);
    }

    /**
     * 中奖配置缓存
     *
     * @return
     */
    private RedisKey buildAwardConfigKey() {
        return RedisKey.create(SocialKeyDefine.TurntableLotteryAwardConfigList);
    }

    /**
     * 最近几条中奖消息Key
     *
     * @return
     */
    private RedisKey buildRecentAwardMsgKey() {
        return RedisKey.create(SocialKeyDefine.TurntableLotteryAwardMsg);
    }
}
