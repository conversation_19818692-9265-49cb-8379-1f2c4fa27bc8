package com.tuowan.yeliao.social.comp.friend;

import com.easyooo.framework.common.util.JsonUtils;
import com.easyooo.framework.common.util.MapUtils;
import com.tuowan.yeliao.commons.comp.message.MessageComponent;
import com.tuowan.yeliao.commons.config.redis.RedisKey;
import com.tuowan.yeliao.commons.config.redis.template.SocialRedisTemplate;
import com.tuowan.yeliao.commons.context.GlobalUtils;
import com.tuowan.yeliao.commons.core.constant.IconConstant;
import com.tuowan.yeliao.commons.core.constant.VersionConstant;
import com.tuowan.yeliao.commons.core.enums.busicode.BackCodeDefine;
import com.tuowan.yeliao.commons.core.enums.business.BusinessDataKey;
import com.tuowan.yeliao.commons.core.enums.redis.BusiKeyDefine;
import com.tuowan.yeliao.commons.core.enums.redis.SocialKeyDefine;
import com.tuowan.yeliao.commons.data.dto.social.ChatTipsInfoDTO;
import com.tuowan.yeliao.commons.data.entity.user.UUserBasic;
import com.tuowan.yeliao.commons.data.entity.user.UUserExt;
import com.tuowan.yeliao.commons.data.enums.general.ClientTouchType;
import com.tuowan.yeliao.commons.data.enums.social.ChatTipsShowType;
import com.tuowan.yeliao.commons.data.enums.user.SexType;
import com.tuowan.yeliao.commons.data.enums.user.VipTouchValue;
import com.tuowan.yeliao.commons.data.enums.user.VipTriggerType;
import com.tuowan.yeliao.commons.data.manager.config.NewsManager;
import com.tuowan.yeliao.commons.data.manager.user.UserInfoManager;
import com.tuowan.yeliao.commons.data.utils.AppVersionUtils;
import com.tuowan.yeliao.commons.data.utils.BusiUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * VIP开通引导组件
 *
 * <AUTHOR>
 * @date 2022/7/8 13:47
 */
@Component
public class VipGuideComponent {

    @Autowired
    private MessageComponent messageComponent;
    @Autowired
    private UserInfoManager userInfoManager;
    @Autowired
    private NewsManager newsManager;
    @Autowired
    private SocialRedisTemplate socialRedisTemplate;

    /**
     * 异步处理通话结束后VIP引导消息
     *
     * @param userId
     * @param friendId
     */
    public void saveFinishCallVipGuide(Long userId, Long friendId) {
        // 发送开通VIP小灰条
        ChatTipsInfoDTO tips = buildFinishCallTouchText();
        UUserBasic friendUser = userInfoManager.getUserBasic(friendId);
        messageComponent.sendChatTipsMsg(friendUser, userId, tips);

        // 引导开通VIP弹窗(客户端默认延时1.5秒后，弹出开通VIP)
        sendOpenVipPopupMsg(userId, ClientTouchType.OpenVip, VipTouchValue.VipNetCall, VipTriggerType.AudioFinish);
    }

    private ChatTipsInfoDTO buildFinishCallTouchText() {
        ChatTipsInfoDTO dto = new ChatTipsInfoDTO();
        dto.setTextTpl("开通VIP享${discountText}，${touchText}享受优惠");
        dto.setTextParams(MapUtils.gmap("${discountText}", "视频8折", "${touchText}", "立即开通"));
        List<Map<String, Object>> styleList = new ArrayList<>();
        Map<String, Object> style = new HashMap<>();
        style.put("el", "${discountText}");
        style.put("styleType", "basic");
        style.put("color", "#FDE504");
        styleList.add(style);

        style = new HashMap<>();
        style.put("el", "${touchText}");
        style.put("styleType", "basic");
        style.put("color", "#16EBFF");
        style.put("underLineColor", "#16EBFF");
        styleList.add(style);
        dto.setTextStyles(JsonUtils.seriazileAsString(styleList));
        dto.setShowType(ChatTipsShowType.Receiver);
        dto.setTextTouch(ClientTouchType.OpenVip);
        dto.setTouchValue(buildOpenVipTouchValue(VipTouchValue.VipNetCall, VipTriggerType.AudioTips));
        return dto;
    }

    /**
     * 聊天框引导开通Vip
     *
     * @param userId
     */
    public void sendChatBannerMsg(Long userId) {
        if (SexType.Female == GlobalUtils.sexType()) {
            return;
        }
        UUserBasic basic = userInfoManager.getUserBasic(userId);
        if (BusiUtils.isVip(basic)) {
            return;
        }
        RedisKey bannerKey = buildInputBannerUserKey();
        if (socialRedisTemplate.sismember(bannerKey, userId.toString())) {
            return;
        }
        boolean newUser = newUser(basic.getCreateTime());
        if (newUser) {
            // 累计私信次数
            Long chatTimes = socialRedisTemplate.incr(buildDayChatTimesKey(userId));
            if (chatTimes < 5) {
                return;
            }
        }
        Map<BusinessDataKey, Object> extMap = new HashMap<>();
        extMap.put(BusinessDataKey.TouchType, ClientTouchType.OpenVip);
        extMap.put(BusinessDataKey.TouchValue, buildOpenVipTouchValue(VipTouchValue.VipChat, VipTriggerType.ChatBanner));
        extMap.put(BusinessDataKey.BgPic, IconConstant.VIP_CHAT_BANNER);
        messageComponent.sendMsgToUserInContext(BackCodeDefine.ChatInputBanner, extMap, userId);
        socialRedisTemplate.sadd(bannerKey, userId.toString());
        socialRedisTemplate.expire(bannerKey);
    }

    /**
     * Vip 开通弹窗消息
     *
     * @param userId
     */
    private void sendOpenVipPopupMsg(Long userId, ClientTouchType touchType, VipTouchValue touchValue, VipTriggerType triggerType) {
        Map<BusinessDataKey, Object> extMap = new HashMap<>();
        extMap.put(BusinessDataKey.TouchType, touchType);
        extMap.put(BusinessDataKey.TouchValue, buildOpenVipTouchValue(touchValue, triggerType));
        messageComponent.sendMsgToUserInContext(BackCodeDefine.OpenVipPopup, extMap, userId);
    }

    /**
     * 组装touchValue
     *
     * @param touchValue
     * @param triggerType
     * @return
     */
    public String buildOpenVipTouchValue(VipTouchValue touchValue, VipTriggerType triggerType) {
        Map<String, String> params = new HashMap<>();
        params.put("type", touchValue.name());
        params.put("triggerType", triggerType.name());
        return JsonUtils.seriazileAsString(params);
    }

    /**
     * 24小时内注册的用户
     *
     * @param date
     * @return
     */
    private boolean newUser(Date date) {
        Long now = System.currentTimeMillis();
        if (now - date.getTime() <= 24 * 3600 * 1000L) {
            return true;
        }
        return false;
    }

    /**
     * 用户每日弹出次数
     *
     * @param userId
     * @return
     */
    public Long incrDayPopupTimes(Long userId) {
        return socialRedisTemplate.incr(buildDayPopupKey(userId));
    }

    /**
     * 上次弹出时间
     *
     * @param userId
     * @return
     */
    public void setDayPopupLastTime(Long userId) {
        Long now = System.currentTimeMillis();
        socialRedisTemplate.set(buildDayPopupLastTimeKey(userId), now.toString());
    }

    /**
     * 用户是否上次弹出过
     *
     * @param userId
     * @return
     */
    public boolean hasDayPopupLastTime(Long userId) {
        return socialRedisTemplate.exists(buildDayPopupLastTimeKey(userId));
    }

    /**
     * 日金币不足弹窗次数
     *
     * @return
     */
    private RedisKey buildDayPopupKey(Long userId) {
        return RedisKey.create(BusiKeyDefine.UserNotEnoughDayPopup, userId);
    }

    /**
     * 日私信次数
     *
     * @return
     */
    private RedisKey buildDayChatTimesKey(Long userId) {
        return RedisKey.create(SocialKeyDefine.UserChatTimes, userId);
    }

    /**
     * 弹出输入框Banner 用户
     *
     * @return
     */
    private RedisKey buildInputBannerUserKey() {
        return RedisKey.create(SocialKeyDefine.ChatInputBannerUser);
    }

    /**
     * 用户日金币不足弹窗时间
     *
     * @return
     */
    private RedisKey buildDayPopupLastTimeKey(Long userId) {
        return RedisKey.create(BusiKeyDefine.UserNotEnoughPopupLastTime, userId);
    }
}
