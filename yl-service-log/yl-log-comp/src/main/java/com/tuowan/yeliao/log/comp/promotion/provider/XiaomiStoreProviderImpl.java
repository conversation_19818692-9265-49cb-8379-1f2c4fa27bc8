package com.tuowan.yeliao.log.comp.promotion.provider;

import com.easyooo.framework.common.extend.SimpleMap;
import com.easyooo.framework.common.util.EncryptUtils;
import com.easyooo.framework.common.util.JsonUtils;
import com.easyooo.framework.common.util.StringUtils;
import com.easyooo.framework.common.util.ThreadUtils;
import com.tuowan.yeliao.commons.config.UnifiedConfig;
import com.tuowan.yeliao.commons.core.enums.general.ClientType;
import com.tuowan.yeliao.commons.core.enums.general.PackageType;
import com.tuowan.yeliao.commons.core.http.HttpPoolManagerFactory;
import com.tuowan.yeliao.commons.data.entity.config.TPromotionConfig;
import com.tuowan.yeliao.commons.data.enums.config.PromotionProvider;
import com.tuowan.yeliao.log.comp.promotion.AbstractPromotionProvider;
import com.tuowan.yeliao.log.comp.promotion.PromotionUtils;
import com.tuowan.yeliao.log.comp.promotion.dto.ReportEventResult;
import com.tuowan.yeliao.log.comp.promotion.dto.XiaomiUploadInfoDTO;
import com.tuowan.yeliao.log.comp.promotion.enums.XiaomiAccountType;
import com.tuowan.yeliao.log.comp.promotion.enums.XiaomiActionType;
import com.tuowan.yeliao.log.data.entity.CPromotionAndroid;
import com.tuowan.yeliao.log.data.entity.PromotionBase;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Map;


/**
 * 小米商店推广业务数据上报实现
 *
 * <AUTHOR>
 * @date 2022/9/15 13:59
 */
@Component
public class XiaomiStoreProviderImpl extends AbstractPromotionProvider {

    /** 数据上报地址 */
    private static String UPLOAD_URL;


    static {
        if (UnifiedConfig.isProdEnv()) {
            UPLOAD_URL = "https://trail.e.mi.com/global/log";
        } else {
            UPLOAD_URL = "https://trail.e.mi.com/global/test";
        }
    }

    @Override
    public PromotionProvider getSupportType() {
        return PromotionProvider.XiaomiStore;
    }

    @Override
    public PromotionBase parseData(ClientType clientType, PackageType packageType, SimpleMap params) {
        if (ClientType.Android == clientType) {
            CPromotionAndroid entity = new CPromotionAndroid();
            //包体
            entity.setType(packageType);
            // 广告计划ID 备注：该字段现阶段用来存储 包体
            entity.setAdId(params.getString("adid"));
            entity.setIp(params.getString("ip"));
            String oaid = params.getString("oaid"); // 这个好像是原值
            if (StringUtils.isNotEmpty(oaid) && !PromotionUtils.isInvalidId(oaid)) {
                entity.setDeviceOaid(oaid);
                entity.setDeviceOaidMd5(EncryptUtils.md5(oaid).toLowerCase());
            }
            String imei = params.getString("imei"); // 文档上写了 这个是 IMEI的MD5加密字符串
            if (StringUtils.isNotEmpty(imei) && !PromotionUtils.isInvalidId(imei)) {
                entity.setDeviceImeiMd5(imei.toLowerCase());
            }
            entity.setCallback(params.getString("callback"));
            return entity;
        }
        return null;
    }

    @Override
    public ReportEventResult reportActiveEvent(ClientType clientType, PromotionBase promotion, TPromotionConfig promotionConfig) {
        return reportEvent(promotion, promotionConfig, XiaomiActionType.APP_ACTIVE, null);
    }

    @Override
    public ReportEventResult reportRegisterEvent(ClientType clientType, PromotionBase promotion, TPromotionConfig promotionConfig) {
        return reportEvent(promotion, promotionConfig,  XiaomiActionType.APP_REGISTER, null);
    }

    @Override
    public ReportEventResult reportPayEvent (ClientType clientType, PromotionBase promotion, TPromotionConfig promotionConfig, Long userId, Integer payAmount, boolean isFirstPaid) {
        return reportEvent(promotion, promotionConfig,  XiaomiActionType.APP_PAY, payAmount);
    }

    /**
     * 上报推广数据
     *
     * @param promotion
     * @param promotionConfig
     * @param eventType
     * @return
     */
    private ReportEventResult reportEvent(PromotionBase promotion, TPromotionConfig promotionConfig, XiaomiActionType eventType, Integer payAmount) {
        XiaomiAccountType type = getAccountType(promotionConfig.getPackageType());
        //构建请求url，小米商店支持的是get方法
        String url = buildUrl(promotion, type, eventType, payAmount);
        if (StringUtils.isEmpty(url)) {
            LOG.error("小米商店上报信息-XiaomiStoreProviderImpl-reportEvent-nullUrl");
            return ReportEventResult.buildNoNeed();
        }
        int i = 0;
        String jsonStr = null;
        do {
            try {
                jsonStr = HttpPoolManagerFactory.getPromotionPool().get(url);
                LOG.info("小米商店上报信息-XiaomiStoreProviderImpl-reportEvent, eventType: {}, url: {}, response: {}", eventType, url, jsonStr);
                if (StringUtils.isNotEmpty(jsonStr)) {
                    Map<String, Object> resultMap = JsonUtils.toJsonMap(jsonStr);
                    if ("1".equals(resultMap.get("code").toString())) {
                        return ReportEventResult.buildSuccess();
                    }
                }
            } catch (Exception e) {
                LOG.error("小米商店上报信息-XiaomiStoreProviderImpl-reportEvent-fail，response: {} ,reason:{}", jsonStr, e);
            }
            ThreadUtils.sleep(500);
        } while (++i < 3);
        LOG.error("XiaomiStoreProviderImpl-reportEvent-fail, response :{}",  jsonStr);
        return ReportEventResult.buildFailure();
    }

    private XiaomiAccountType getAccountType(PackageType packageType) {
        // 后续有马甲包根据包体判断
        return XiaomiAccountType.valueOf(packageType.getId());
    }

    /**
     * 构建请求参数
     *
     * @param promotion
     * @param type
     * @param eventType
     * @return
     */
    private String buildUrl(PromotionBase promotion, XiaomiAccountType type, XiaomiActionType eventType, Integer payAmount) {

        XiaomiUploadInfoDTO xiaomiUploadInfoDTO = new XiaomiUploadInfoDTO();
        xiaomiUploadInfoDTO.setUploadUrl(UPLOAD_URL);
        //imei
        if (StringUtils.isNotEmpty(promotion.getDeviceImeiMd5())) {
            xiaomiUploadInfoDTO.setImei(promotion.getDeviceImeiMd5());
        }
        //oaid
        if (StringUtils.isNotEmpty(promotion.getDeviceOaid())) {
            xiaomiUploadInfoDTO.setOaid(promotion.getDeviceOaid());
        }
        // ua
        if (StringUtils.isNotEmpty(promotion.getUa())) {
            xiaomiUploadInfoDTO.setUa(promotion.getUa());
        }

        //convTime
        //convType
        //si
        // 根据上报行为配置
        if (XiaomiActionType.APP_ACTIVE.equals(eventType)) {
            //配置激活类型、signKey、EncryptKey
            xiaomiUploadInfoDTO.setConvType(XiaomiActionType.APP_ACTIVE.getId());
            xiaomiUploadInfoDTO.setSignKey(XiaomiActionType.APP_ACTIVE.getSignKey());
            xiaomiUploadInfoDTO.setEncryptKey(XiaomiActionType.APP_ACTIVE.getEncryptKey());
        } else if (XiaomiActionType.APP_REGISTER.equals(eventType)){
            //拿到注册时间
            xiaomiUploadInfoDTO.setConvType(XiaomiActionType.APP_REGISTER.getId());
            xiaomiUploadInfoDTO.setSignKey(XiaomiActionType.APP_REGISTER.getSignKey());
            xiaomiUploadInfoDTO.setEncryptKey(XiaomiActionType.APP_REGISTER.getEncryptKey());
        } else if (XiaomiActionType.APP_PAY.equals(eventType)) {
            //拿到付费时间
            xiaomiUploadInfoDTO.setConvType(XiaomiActionType.APP_PAY.getId());
            xiaomiUploadInfoDTO.setSignKey(XiaomiActionType.APP_PAY.getSignKey());
            xiaomiUploadInfoDTO.setEncryptKey(XiaomiActionType.APP_PAY.getEncryptKey());
        }
        //clientIp
        if (StringUtils.isNotEmpty(promotion.getIp())) {
            xiaomiUploadInfoDTO.setClientIp(promotion.getIp());
        }
        //appId
        xiaomiUploadInfoDTO.setAppId(type.getAppId());
        //customerId
        xiaomiUploadInfoDTO.setCustomerId(type.getCustomerId());
        //测试用数据
        //xiaomiUploadInfoDTO.setOaid("38244221e0a4f993");
        xiaomiUploadInfoDTO.setConvTime(new Date().getTime());
        return xiaomiUploadInfoDTO.getInfo().getFinalUrl();
    }
}
