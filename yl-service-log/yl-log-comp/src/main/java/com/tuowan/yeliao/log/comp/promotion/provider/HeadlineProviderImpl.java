package com.tuowan.yeliao.log.comp.promotion.provider;

import com.alibaba.fastjson.JSONObject;
import com.easyooo.framework.common.extend.SimpleMap;
import com.easyooo.framework.common.util.*;
import com.tuowan.yeliao.commons.config.redis.template.LogRedisTemplate;
import com.tuowan.yeliao.commons.core.enums.general.ClientType;
import com.tuowan.yeliao.commons.core.enums.general.PackageType;
import com.tuowan.yeliao.commons.core.http.HttpPoolManagerFactory;
import com.tuowan.yeliao.commons.data.entity.config.TPromotionConfig;
import com.tuowan.yeliao.commons.data.enums.config.PromotionProvider;
import com.tuowan.yeliao.log.comp.promotion.AbstractPromotionProvider;
import com.tuowan.yeliao.log.comp.promotion.PromotionUtils;
import com.tuowan.yeliao.log.comp.promotion.dto.ReportEventResult;
import com.tuowan.yeliao.log.data.entity.CPromotionAndroid;
import com.tuowan.yeliao.log.data.entity.CPromotionIos;
import com.tuowan.yeliao.log.data.entity.PromotionBase;
import org.bouncycastle.util.Pack;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Map;

/**
 * 头条推广业务实现
 * <p>
 *
 * v1监测链接地址：https://promo.hnjingda168.cn/log/promotion/hl/v1/click?adid=__AID__&cId=__CID__&dId=__CAMPAIGN_ID__&idfa=__IDFA__&ip=__IP__&ua=__UA__&model=__MODEL__&imei=__IMEI__&oaid=__OAID__&oaIdMd5=__OAID_MD5__&ssaid=__ANDROIDID__&os=__OS__&TIMESTAMP=__TS__&callback=__CALLBACK_PARAM__&type=JTTS1
 * v1参数说明详见：https://open.oceanengine.com/labels/7/docs/1696710655781900
 *
 * v2监测链接地址：https://promo.hnjingda168.cn/log/promotion/hl/v1/click?cId=__PROMOTION_ID__&dId=__PROJECT_ID__&idfa=__IDFA__&ip=__IP__&ua=__UA__&model=__MODEL__&imei=__IMEI__&oaid=__OAID__&oaIdMd5=__OAID_MD5__&ssaid=__ANDROIDID__&os=__OS__&TIMESTAMP=__TS__&callback=__CALLBACK_PARAM__&type=JTTS1
 * v2参数说明详见：https://bytedance.feishu.cn/docs/doccnoYSvc9h6ZVIF634GAZPtSc
 *
 * <AUTHOR>
 * @date 2022/3/29 19:24
 */
@Component
public class HeadlineProviderImpl extends AbstractPromotionProvider {

    private final Logger LOG = LoggerFactory.getLogger(this.getClass());

    /**
     * 事件上报地址
     */
    private static final String API_URL = "https://analytics.oceanengine.com/api/v2/conversion";

    @Autowired
    private LogRedisTemplate logRedisTemplate;

    @Override
    public PromotionProvider getSupportType() {
        return PromotionProvider.HLV1;
    }

    @Override
    public PromotionBase parseData(ClientType clientType, PackageType packageType, SimpleMap params) {
        if (ClientType.iOS == clientType) {
            CPromotionIos entity = new CPromotionIos();
            // 广告计划ID(v1)
            entity.setAdId(params.getString("adid"));
            // 广告创意ID(v1)/广告ID(v2)
            entity.setPromotionId(params.getString("cId"));
            // V2版本没有AdId，但为了数据统计方便，adId与promotionId一致
            if (StringUtils.isEmpty(entity.getAdId())) {
                entity.setAdId(entity.getPromotionId());
            }
            // 广告组ID(v1)/项目ID(v1)
            entity.setProjectId(params.getString("dId"));
            String idfa = params.getString("idfa");
            if (StringUtils.isNotEmpty(idfa) && !PromotionUtils.isInvalidId(idfa)) {
                entity.setDeviceImeiMd5(EncryptUtils.md5(idfa).toLowerCase());
                entity.setDeviceImei(idfa);
            }
            entity.setIp(params.getString("ip"));
            entity.setUa(StringUtils.decode(params.getString("ua")));
            entity.setModel(StringUtils.decode(params.getString("model")));
            entity.setCallback(params.getString("callback"));
            return entity;
        }
        if (ClientType.Android == clientType) {
            CPromotionAndroid entity = new CPromotionAndroid();
            // 包体
            entity.setType(packageType);
            // 广告计划ID(v1)
            entity.setAdId(params.getString("adid"));
            // 广告创意ID(v1)/广告ID(v2)
            entity.setPromotionId(params.getString("cId"));
            // V2版本没有AdId，但为了数据统计方便，adId与promotionId一致
            if (StringUtils.isEmpty(entity.getAdId())) {
                entity.setAdId(entity.getPromotionId());
            }
            // 广告组ID(v1)/项目ID(v1)
            entity.setProjectId(params.getString("dId"));
            // 原始OAID
            String oaid = params.getString("oaid");
            if (StringUtils.isNotEmpty(oaid) && !PromotionUtils.isInvalidId(oaid)) {
                entity.setDeviceOaidMd5(EncryptUtils.md5(oaid).toLowerCase());
                entity.setDeviceOaid(oaid);
            }
            // IMEI(已经过md5加密)
            String imei = params.getString("imei");
            if (StringUtils.isNotEmpty(imei) && !PromotionUtils.isInvalidId(imei)) {
                entity.setDeviceImeiMd5(imei.toLowerCase());
            }
            entity.setCallback(params.getString("callback"));
            return entity;
        }
        return null;
    }

    @Override
    public ReportEventResult reportActiveEvent(ClientType clientType, PromotionBase promotion, TPromotionConfig promotionConfig) {
        return reportData(clientType, promotion, "active", null);
    }

    /**
     * 备注：遇见爱需要将注册 上报成 激活
     * @param clientType
     * @param promotion
     * @param promotionConfig
     * @return
     */
    @Override
    public ReportEventResult reportRegisterEvent(ClientType clientType, PromotionBase promotion, TPromotionConfig promotionConfig) {
        return reportData(clientType, promotion, "active", null); //active_register
    }

    @Override
    public ReportEventResult reportPayEvent(ClientType clientType, PromotionBase promotion, TPromotionConfig promotionConfig, Long userId, Integer payAmount, boolean isFirstPaid) {
        // 头条付费上报，单个计划每13次上报，放弃一次上报(缓存每天0点失效，0点开始重新计数)
        /*Long step = 13L;
        RedisKey redisKey = buildPromotionPayReportTimesKey(promotion.getAdId());
        Long newValue = logRedisTemplate.incr(redisKey);
        if (Objects.equals(newValue, step)) {
            logRedisTemplate.decrby(redisKey, step);
            return ReportEventResult.buildSuccess().setPayReport(false);
        }*/
        return reportData(clientType, promotion, "active_pay", payAmount);
    }

    /**
     * 今日头条激活数据上报
     *
     * @param clientType
     * @param promotion
     * @param eventType  上报类型 0 激活 1 注册 2、付费
     * @param money      单位分
     * @return
     */
    private ReportEventResult reportData(ClientType clientType, PromotionBase promotion, String eventType, Integer money) {
        JSONObject body = new JSONObject();
        body.put("event_type", eventType);

        JSONObject properties = new JSONObject();
        properties.put("pay_amount", money);
        body.put("properties", properties);
        body.put("timestamp", System.currentTimeMillis());

        JSONObject ad = new JSONObject();
        ad.put("callback", promotion.getCallback());
        JSONObject context = new JSONObject();
        context.put("ad", ad);
        body.put("context", context);
        String jsonStr = null;
        int i = 0;
        do {
            try {
                // 经测试，实际只要callback参数正确，就算有效的转化
                jsonStr = HttpPoolManagerFactory.getPromotionPool().post(API_URL, body);
                LOG.debug("hlv1Report -> {}", jsonStr);
                if (StringUtils.isNotEmpty(jsonStr)) {
                    Map<String, Object> resultMap = JsonUtils.toJsonMap(jsonStr);
                    if ("0".equals(resultMap.get("code").toString())) {
                        return ReportEventResult.buildSuccess();
                    }
                }
            } catch (Exception e) {
                LOG.error(MsgUtils.format("回调上报用户行为数据失败，请求结果：{}，原因：", jsonStr), e);
            }
            ThreadUtils.sleep(500);
        } while (++i < 3);
        LOG.error("上报用户行为数据失败,原因:{}", jsonStr);
        return ReportEventResult.buildFailure();
    }
}
