package com.tuowan.yeliao.log.comp.promotionpull;

import com.alibaba.fastjson.JSONObject;
import com.tuowan.yeliao.commons.core.enums.general.PackageType;
import com.tuowan.yeliao.log.data.dto.promotion.PromotionChannelAdDTO;
import com.tuowan.yeliao.log.data.enums.PromotionAdType;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/6/7 12:48
 */
@Component
public class PromotionPullComponent implements ApplicationContextAware {


    private Map<PromotionAdType, IPromotionPull> promotionPullImplMap = new HashMap<>();

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        Map<String, ? extends IPromotionPull> promotionPulls = applicationContext.getBeansOfType(IPromotionPull.class);
        for (IPromotionPull promotionPull : promotionPulls.values()) {
            promotionPullImplMap.put(promotionPull.getPromotionAdType(), promotionPull);
        }
    }

    public void pullDate(PromotionChannelAdDTO dto, String startDate, String endDate, String customerAdvertiserType) {
        promotionPullImplMap.get(dto.getAdvertiserType()).pullDate(dto, startDate, endDate, customerAdvertiserType);
    }

    public void pullCashCostData(PromotionChannelAdDTO dto, String startDate, String endDate, String customerAdvertiserType) {
        promotionPullImplMap.get(dto.getAdvertiserType()).pullCashCostData(dto, startDate, endDate, customerAdvertiserType);
    }

    public void saveAuthCodeCache(PromotionAdType promotionAdType, PackageType packageType, JSONObject params) {
        promotionPullImplMap.get(promotionAdType).saveAuthCodeCache(packageType, params);
    }

    public String rebuildToken(PromotionAdType promotionAdType, PackageType packageType) {
        return promotionPullImplMap.get(promotionAdType).rebuildToken(packageType);
    }

    public String getToken(PromotionAdType promotionAdType, PackageType packageType) {
        return promotionPullImplMap.get(promotionAdType).getToken(packageType);
    }

    public void pullAdGroupData(PromotionChannelAdDTO dto, String startDate, String endDate, String customerAdvertiserType) {
        promotionPullImplMap.get(dto.getAdvertiserType()).pullAdGroupData(dto, startDate, endDate, customerAdvertiserType);
    }
}
