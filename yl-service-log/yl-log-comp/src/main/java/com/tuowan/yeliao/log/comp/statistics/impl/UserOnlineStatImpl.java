package com.tuowan.yeliao.log.comp.statistics.impl;

import com.easyooo.framework.common.util.CglibUtils;
import com.easyooo.framework.common.util.DateUtils;
import com.tuowan.yeliao.commons.config.enums.MiniLockType;
import com.tuowan.yeliao.commons.config.lock.MiniLockTemplate;
import com.tuowan.yeliao.commons.config.redis.template.BusiRedisTemplate;
import com.tuowan.yeliao.commons.data.persistence.query.QueryUserMapper;
import com.tuowan.yeliao.log.comp.statistics.Stat10MinAbs;
import com.tuowan.yeliao.log.data.dto.rep.UserActiveDurationDTO;
import com.tuowan.yeliao.log.data.dto.rep.UserCountDataDTO;
import com.tuowan.yeliao.log.data.dto.rep.UserOnlineDateDTO;
import com.tuowan.yeliao.log.data.entity.rep.RUserOnlineLog;
import com.tuowan.yeliao.log.data.enums.statistics.StatType;
import com.tuowan.yeliao.log.data.persistence.rep.QueryBusiMapper;
import com.tuowan.yeliao.log.data.persistence.rep.QueryLogMapper;
import com.tuowan.yeliao.log.data.persistence.rep.RUserOnlineLogMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Objects;

/**
 * 用户在线状态统计
 *
 * <AUTHOR>
 * @date 2022/4/19 21:26
 */
@Component
public class UserOnlineStatImpl extends Stat10MinAbs {
    private final Logger LOG = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private BusiRedisTemplate busiRedisTemplate;
    @Autowired
    private MiniLockTemplate miniLockTemplate;
    @Autowired
    private RUserOnlineLogMapper userOnlineLogMapper;
    @Autowired
    private QueryLogMapper queryLogMapper;
    @Autowired
    private QueryBusiMapper queryBusiMapper;

    @Override
    public StatType support() {
        return StatType.UserOnline;
    }

    @Override
    public void init(Date date){
        RUserOnlineLog checkObj = new RUserOnlineLog(date);
        RUserOnlineLog oneCheck = userOnlineLogMapper.selectByPrimaryKey(checkObj);
        if(Objects.nonNull(oneCheck)){
            return;
        }
        // 初始化
        miniLockTemplate.execute(MiniLockType.InitStatDataRecord, support().getTable(), () -> {
            RUserOnlineLog twoChecke = userOnlineLogMapper.selectByPrimaryKey(checkObj);
            if(Objects.isNull(twoChecke)){
                userOnlineLogMapper.init(checkObj);
            }
            return null;
        });
    }

    @Override
    public void stat(Date date) {
        Date endTime = date;
        Date beginTime = DateUtils.plusMinutes(endTime, -10);
        // 获取用户数据
        RUserOnlineLog update = new RUserOnlineLog(date);
        // 用户注册信息
        UserCountDataDTO userCountDataDTO = queryBusiMapper.queryUserRegData(beginTime, endTime);
        if(Objects.nonNull(userCountDataDTO)){
            update.setmReg(userCountDataDTO.getMaleCount());
            update.setfReg(userCountDataDTO.getFemaleCount());
        }
        // 用户打开信息
        UserCountDataDTO userCountDataDTO1 = queryLogMapper.queryUserOnlineData(beginTime, endTime);
        if(Objects.nonNull(userCountDataDTO1)){
            update.setmOpen(userCountDataDTO1.getMaleCount());
            update.setfOpen(userCountDataDTO1.getFemaleCount());
        }
        // 用户在线数据
        UserOnlineDateDTO userOnlineDateDTO = queryBusiMapper.queryUserOnlineDate();
        CglibUtils.copy(userOnlineDateDTO, update);
        // 用户活跃信息
        UserActiveDurationDTO userActiveDurationDTO = queryLogMapper.queryUserActiveDuration(beginTime, endTime);
        CglibUtils.copy(userActiveDurationDTO, update);

        userOnlineLogMapper.updateByPrimaryKeySelective(update);
    }
}
