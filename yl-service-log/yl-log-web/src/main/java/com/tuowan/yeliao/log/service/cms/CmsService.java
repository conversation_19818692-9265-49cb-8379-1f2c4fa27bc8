package com.tuowan.yeliao.log.service.cms;

import com.easyooo.framework.common.util.DateUtils;
import com.easyooo.framework.common.util.ListUtils;
import com.easyooo.framework.common.util.StringUtils;
import com.tuowan.yeliao.commons.comp.notice.NoticeComponent;
import com.tuowan.yeliao.commons.comp.operate.UserOperateComponent;
import com.tuowan.yeliao.commons.context.GlobalUtils;
import com.tuowan.yeliao.commons.core.enums.general.BoolType;
import com.tuowan.yeliao.commons.core.exception.BusiException;
import com.tuowan.yeliao.commons.data.enums.config.NoticeSysType;
import com.tuowan.yeliao.commons.data.enums.general.ReviewResultType;
import com.tuowan.yeliao.commons.data.enums.user.UserActionRiskTriggerType;
import com.tuowan.yeliao.commons.web.proxy.config.BusiCode;
import com.tuowan.yeliao.log.data.entity.CUserActionRisk;
import com.tuowan.yeliao.log.data.persistence.CUserActionRiskMapper;
import com.tuowan.yeliao.log.web.form.cms.AdRiskAuditForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * CMS 逻辑实现
 */
@Service
public class CmsService {

    @Autowired
    private CUserActionRiskMapper userActionRiskMapper;
    @Autowired
    private UserOperateComponent userOperateComponent;
    @Autowired
    private NoticeComponent noticeComponent;

    /**
     * 审核广告风险预警
     *
     * @param form
     */
    @BusiCode
    public void saveAuditAdRisk(AdRiskAuditForm form) {
        CUserActionRisk risk = userActionRiskMapper.selectByPrimaryKeyForUpdate(new CUserActionRisk(form.getLogId()));
        if (risk == null || risk.getAuditStatus() != ReviewResultType.Wait) {
            throw new BusiException("记录不存在或已被审核，请刷新后重试");
        }
        risk.setAuditStatus(form.getValid() == BoolType.True ? ReviewResultType.Reject : ReviewResultType.Pass);
        risk.setAuditUserId(GlobalUtils.uid());
        risk.setAuditTime(DateUtils.nowTime());
        risk.setRemark(form.getRemark());
        if (ListUtils.isNotEmpty(form.getPics())) {
            risk.setPics(StringUtils.join(form.getPics(), ","));
        }
        userActionRiskMapper.updateByPrimaryKeySelective(risk);
        if (form.getValid() == BoolType.True) {
            // 预警有效，封禁设备, 通过CMS调用封禁设备接口，此处不做封禁处理
            // 同用户的其他预警同步处理
            userActionRiskMapper.batchUpdateOtherLogs(risk);
        } else {
            // 预警无效，解除限制
            userOperateComponent.saveUnBanChat(risk.getUserId());
            if (risk.getTriggerType() == UserActionRiskTriggerType.InvalidContent) {
                // 通知用户风险解除
                noticeComponent.sendSystemNotice(risk.getUserId(), NoticeSysType.ChatRiskRelease);
            }
        }
    }
}
