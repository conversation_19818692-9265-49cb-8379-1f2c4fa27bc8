package com.tuowan.yeliao.log.web.form.stat;

import com.easyooo.framework.validate.config.LMNotEmpty;
import com.tuowan.yeliao.commons.web.common.form.Form;
import com.tuowan.yeliao.log.data.enums.DataEventType;

/**
 * 数据上报表单
 *
 * <AUTHOR>
 * @date 2022/1/11 11:23
 */
public class DataReportForm implements Form {

    /**
     * 埋点数据代码
     */
    @LMNotEmpty
    private String code;
    /**
     * 链路
     */
    @LMNotEmpty
    private String trace;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getTrace() {
        return trace;
    }

    public void setTrace(String trace) {
        this.trace = trace;
    }
}
