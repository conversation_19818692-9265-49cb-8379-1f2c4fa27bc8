package com.tuowan.yeliao.log.service.promotion;

import com.tuowan.yeliao.commons.web.proxy.config.BusiCode;
import com.tuowan.yeliao.commons.web.proxy.config.JobInvoke;
import com.tuowan.yeliao.log.comp.promotionpull.PromotionPullComponent;
import com.tuowan.yeliao.log.data.dto.promotion.PromotionChannelAdDTO;
import com.tuowan.yeliao.log.data.persistence.CommonQueryMapper;
import com.tuowan.yeliao.log.web.form.promotion.PromotionAuthorizeForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 推广数据拉取业务封装
 *
 * <AUTHOR>
 * @date 2022/7/24 22:29
 */
@Service
public class PromotionPullService {

    @Autowired
    private CommonQueryMapper commonQueryMapper;
    @Autowired
    private PromotionPullComponent promotionPullComponent;

    /**
     * 获取可拉取数据的广告主合集
     *
     * @return
     */
    @JobInvoke
    public List<PromotionChannelAdDTO> getAdvertiserAdConsumeConfig() {
        return commonQueryMapper.queryWaitSyncChannelAdConfig();
    }
    /**
     * 获取可拉取数据的广告主合集(带折扣系数)
     *
     * @return
     */
    @JobInvoke
    public List<PromotionChannelAdDTO> getAdvertiserAdConsumeConfigWithRate() {
        return commonQueryMapper.queryWaitSyncChannelAdConfigWithRate();
    }


    /**
     * 拉取数据 并且同步
     *
     * @param dto
     * @param startDate
     * @param endDate
     * @param customerAdvertiserType
     */
    @JobInvoke
    public void savePullDate(PromotionChannelAdDTO dto, String startDate, String endDate, String customerAdvertiserType) {
        promotionPullComponent.pullDate(dto, startDate, endDate, customerAdvertiserType);
    }

    /**
     * 拉取数据 并且同步
     *
     * @param dto
     * @param startDate
     * @param endDate
     * @param customerAdvertiserType
     */
    @JobInvoke
    public void savePullCashCostData(PromotionChannelAdDTO dto, String startDate, String endDate, String customerAdvertiserType) {
        promotionPullComponent.pullCashCostData(dto, startDate, endDate, customerAdvertiserType);
    }

    /**
     * 拉取数据 并且同步
     *
     * @param dto
     * @param startDate
     * @param endDate
     * @param customerAdvertiserType
     */
    @JobInvoke
    public void savePullAdGroupData(PromotionChannelAdDTO dto, String startDate, String endDate, String customerAdvertiserType) {
        promotionPullComponent.pullAdGroupData(dto, startDate, endDate, customerAdvertiserType);
    }

    /**
     * 应用授权回调
     *
     * @param form
     */
    @BusiCode
    public void saveAuthorize(PromotionAuthorizeForm form) {
        // 存储授权code
        promotionPullComponent.saveAuthCodeCache(form.getAdType(), form.getPackageType(), form.getParams());
        // 刷新token
        promotionPullComponent.rebuildToken(form.getAdType(), form.getPackageType());
    }
}
