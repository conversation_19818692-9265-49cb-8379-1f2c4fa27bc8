package com.tuowan.yeliao.log.web.form.ccs;


import com.easyooo.framework.common.util.EnumUtils;
import com.tuowan.yeliao.commons.data.enums.ccs.ServiceStatus;
import com.tuowan.yeliao.commons.web.common.form.Form;

/**
 * 设置客服服务状态表单
 *
 * <AUTHOR>
 * @date 2020/4/23 17:18
 */
public class SetServiceStatusForm implements Form {

    /**
     * 服务状态
     */
    private ServiceStatus serviceStatus;
    /**
     * 访问令牌
     */
    private transient String accessToken;

    public static SetServiceStatusForm build(String accessToken, String status) {
        SetServiceStatusForm form = new SetServiceStatusForm();
        form.setAccessToken(accessToken);
        form.setServiceStatus(EnumUtils.byName(status, ServiceStatus.class));
        return form;
    }

    public ServiceStatus getServiceStatus() {
        return serviceStatus;
    }

    public void setServiceStatus(ServiceStatus serviceStatus) {
        this.serviceStatus = serviceStatus;
    }

    public String getAccessToken() {
        return accessToken;
    }

    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
    }
}
