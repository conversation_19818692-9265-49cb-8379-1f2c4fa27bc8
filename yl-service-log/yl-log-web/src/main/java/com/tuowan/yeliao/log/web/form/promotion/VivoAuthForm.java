package com.tuowan.yeliao.log.web.form.promotion;


import com.tuowan.yeliao.commons.web.common.form.Form;

/**
 * vivo 授权参数表单
 */
public class VivoAuthForm implements Form {

    /** 在开发者官网创建应用后获得 */
    private String clientId;

    /** vivo 授权code 可换取token */
    private String code;

    public VivoAuthForm(String clientId, String code) {
        this.clientId = clientId;
        this.code = code;
    }

    public String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }
}
