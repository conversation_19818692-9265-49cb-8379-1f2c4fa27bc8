/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.log.data.entity;

import com.easyooo.framework.cache.annotations.KeyProperty;
import org.apache.ibatis.type.Alias;

import java.util.Date;

@Alias("CShumeiAudioLog")
public class CShumeiAudioLog {
    /** 创建时间 */
    @KeyProperty
    private Date createTime;

    /** 业务流水号 */
    @KeyProperty
    private Long tid;

    /** 风险级别，PASS：正常内容，REVIEW：可疑内容，REJECT：违规内容 */
    private String riskLevel;

    /** 风险标签 */
    private String labels;

    /** 音频片段文本 */
    private String audioText;

    /** 回调时间 */
    private Date callbackTime;

    /** 渠道：S,语音签名，C,聊天 */
    private String channel;

    public CShumeiAudioLog() {
        
    }

    /** 根据主键初始化实例 **/
    public CShumeiAudioLog(Date createTime, Long tid) {
        this.createTime = createTime;
        this.tid = tid;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getTid() {
        return tid;
    }

    public void setTid(Long tid) {
        this.tid = tid;
    }

    public String getRiskLevel() {
        return riskLevel;
    }

    public void setRiskLevel(String riskLevel) {
        this.riskLevel = riskLevel == null ? null : riskLevel.trim();
    }

    public String getLabels() {
        return labels;
    }

    public void setLabels(String labels) {
        this.labels = labels == null ? null : labels.trim();
    }

    public String getAudioText() {
        return audioText;
    }

    public void setAudioText(String audioText) {
        this.audioText = audioText == null ? null : audioText.trim();
    }

    public Date getCallbackTime() {
        return callbackTime;
    }

    public void setCallbackTime(Date callbackTime) {
        this.callbackTime = callbackTime;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel == null ? null : channel.trim();
    }
}