/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.log.data.entity;

import com.easyooo.framework.cache.annotations.KeyProperty;
import org.apache.ibatis.type.Alias;

import java.util.Date;

@Alias("CChatUpLog")
public class CChatUpLog {
    /**
     * 创建时间
     */
    @KeyProperty
    private Date createTime;

    /**
     * 日志ID
     */
    @KeyProperty
    private Long logId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 好友用户ID
     */
    private Long friendId;

    /**
     * 系统派单时间
     */
    private Date dispatchTime;

    /**
     * 分发等级，1,2,3,4
     */
    private Integer dispatchLevel;

    /**
     * 执行时间，单位：ms
     */
    private Integer execTime;

    /**
     * 匹配次数
     */
    private Integer matchTimes;

    public CChatUpLog() {

    }

    /**
     * 根据主键初始化实例
     **/
    public CChatUpLog(Date createTime, Long logId) {
        this.createTime = createTime;
        this.logId = logId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getLogId() {
        return logId;
    }

    public void setLogId(Long logId) {
        this.logId = logId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getFriendId() {
        return friendId;
    }

    public void setFriendId(Long friendId) {
        this.friendId = friendId;
    }

    public Date getDispatchTime() {
        return dispatchTime;
    }

    public void setDispatchTime(Date dispatchTime) {
        this.dispatchTime = dispatchTime;
    }

    public Integer getDispatchLevel() {
        return dispatchLevel;
    }

    public void setDispatchLevel(Integer dispatchLevel) {
        this.dispatchLevel = dispatchLevel;
    }

    public Integer getExecTime() {
        return execTime;
    }

    public void setExecTime(Integer execTime) {
        this.execTime = execTime;
    }

    public Integer getMatchTimes() {
        return matchTimes;
    }

    public void setMatchTimes(Integer matchTimes) {
        this.matchTimes = matchTimes;
    }
}