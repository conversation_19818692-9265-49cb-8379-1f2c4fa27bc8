package com.tuowan.yeliao.log.data.enums;

import com.easyooo.framework.common.util.EnumUtils;

public enum EventCode implements EnumUtils.IDEnum {
    AgreePrivacyRule("AgreePrivacyRule", "用户同意隐私协议"),
    DontAgreePrivacyRule("DontAgreePrivacyRule", "用户不 同意隐私协议"),
    AppStart("AppStart", "设备启动 "),
    ArriveLoginPage("ArriveLoginPage", "到达登录页"),
    AgreePrivacyPolicy("AgreePrivacyPolicy", "用户同意隐私政策"),
    AgreePrivacyPolicyPop("AgreePrivacyPolicyPop", "用户同意隐私政策-弹窗"),
    ClickMobileLogin("ClickMobileLogin", "点击手机号登录"),

    // 微信登录
    WXLogin("WXLogin", "使用微信登录"),
    WXSucJump("WXSucJump", "成功跳转"),
    WXLoginSuc("WXLoginSuc", "微信登录成功"),
    WXRegisterSuc("WXRegisterSuc", "微信注册成功"),

    // 一键登录
    FastLogin("FastLogin", "使用一键登录"),
    FastLoginSuc("FastLoginSuc", "一键登录成功"),
    FastRegisterSuc("FastRegisterSuc", "一键注册成功"),

    // 手机号验证码登录
    CodeLogin("CodeLogin", "使用验证码登录"),
    ClickGetSmsCode("ClickGetSmsCode", "点击获取验证码"),
    WriteSmsCode("WriteSmsCode", "填写验证码"),
    CodeLoginSuc("CodeLoginSuc", "验证码登录成功"),
    CodeRegisterSuc("CodeRegisterSuc", "验证码注册成功"),

    // 苹果登录
    IosLogin("IosLogin", "使用苹果登录"),
    IosLoginSuc("IosLoginSuc", "苹果登录成功"),

    // 注册
    ArriveRegisterPage("ArriveRegisterPage", "到达注册页面"),
    ClickGetRandomNickname("ClickGetRandomNickname", "点击获取随机昵称"),
    UploadHeadPic("UploadHeadPic", "上传头像"),
    SelfEditNickname("SelfEditNickname", "手动修改昵称"),
    ChooseFemaleSex("ChooseFemaleSex", "选择女性"),
    ChooseMaleSex("ChooseMaleSex", "选择男性"),
    RegisterSuc("RegisterSuc", "注册成功"),
    GiveUpRegister("GiveUpRegister", "放弃注册 "),


    AgreeLocationFunction("AgreeLocationFunction", "同意定位"),
    PerfectInfoTipClickGo("PerfectInfoTipClickGo", "完善资料tip点击去完善"),
    PerfectInfoTipClickClose("PerfectInfoTipClickClose", "完善资料tip点击关闭"),
    aiyu_event_recommend_stay_time("aiyu_event_recommend_stay_time", "首页推荐列表停留时间"),
    aiyu_event_recommend_list_accost("aiyu_event_recommend_list_accost", "首页推荐列表搭讪"),
    aiyu_event_recommend_list_to_chat("aiyu_event_recommend_list_to_chat", "首页跳转私聊事件"),
    aiyu_event_recommend_list_to_homepage("aiyu_event_recommend_list_to_homepage", "首页推荐列表进入个人主页"),
    aiyu_event_video_stay_time("aiyu_event_video_stay_time", "首页视列表停留时间"),
    aiyu_event_video_list_to_homepage("aiyu_event_video_list_to_homepage", "首页视频列表进入个人主页"),
    aiyu_event_video_list_call_click("aiyu_event_video_list_call_click", "首页视频列表视频拨打点击"),


    //动态
    aiyu_event_dynamic_online_stay_time("aiyu_event_dynamic_online_stay_time", "动态在线列表停留时间"),
    aiyu_event_dynamic_hot_stay_time("aiyu_event_dynamic_hot_stay_time", "动态热门列表停留时间"),
    aiyu_event_dynamic_intimates_stay_time("aiyu_event_dynamic_intimates_stay_time", "动态密友列表停留时间"),
    aiyu_event_dynamic_list_to_homepage("aiyu_event_dynamic_list_to_homepage", "动态列表进入个人主页"),
    aiyu_event_dynamic_list_to_chat("aiyu_event_dynamic_list_to_chat", "动态列表进入聊天"),
    aiyu_event_dynamic_list_like("aiyu_event_dynamic_list_like", "动态列表点赞"),
    aiyu_event_dynamic_list_unlike("aiyu_event_dynamic_list_unlike", "动态列表取消点赞"),
    aiyu_event_dynamic_list_gift_click("aiyu_event_dynamic_list_gift_click", "打赏点击"),

    //今日缘分弹框事件
    aiyu_event_home_today_fate_show("aiyu_event_home_today_fate_show", "今日缘分弹框出现"),
    aiyu_event_home_today_fate_close("aiyu_event_home_today_fate_close", "今日缘分手动关闭"),
    aiyu_event_home_today_fate_accost("aiyu_event_home_today_fate_accost", "今日缘分一键搭讪"),

    //4. 个人主页
    aiyu_event_person_stay_time("aiyu_event_person_stay_time", "个人主页停留时间"),
    aiyu_event_person_accost_click("aiyu_event_person_accost_click", "个人主页搭讪点击"),
    aiyu_event_person_chat_click("aiyu_event_person_chat_click", "个人主页聊天点击"),
    aiyu_event_person_video_click("aiyu_event_person_video_click", "个人主页视频点击"),
    aiyu_event_person_enter("aiyu_event_person_enter", "个人主页进入上报"),
    aiyu_event_person_photo_click("aiyu_event_person_photo_click", "个人主页照片墙点击上报"),
    aiyu_event_person_dynamic_click("aiyu_event_person_dynamic_click", "个人主页动态点击上报"),

    //5. 私聊页面

    aiyu_event_private_chat_gift_click("aiyu_event_private_chat_gift_click", "私聊页面送礼物"),
    aiyu_event_private_chat_video_click("aiyu_event_private_chat_video_click", "私聊页面视频拨打"),
    aiyu_event_private_chat_voice_click("aiyu_event_private_chat_voice_click", "私聊页面语音拨打"),
    aiyu_event_private_chat_send_message("aiyu_event_private_chat_send_message", "私聊页面发送输入文本消息"),


    //6. 礼物赠送弹框
    aiyu_event_gift_dialog_show("aiyu_event_gift_dialog_show", "送礼弹框出现时间"),
    aiyu_event_gift_send("aiyu_event_gift_send", "礼物赠送事件"),



    //7. 充值页面

    aiyu_event_mine_enter_recharge_center("aiyu_event_mine_enter_recharge_center", "进入充值中心"),
    aiyu_event_enter_recharge_dialog("aiyu_event_enter_recharge_dialog", "进入充值弹框页面"),
    aiyu_event_mine_recharge_click("aiyu_event_mine_recharge_click", "充值点击（不确定是否成功吊起充值事件）"),
    aiyu_event_recharge_order_create("aiyu_event_recharge_order_create", "充值订单创建"),
    aiyu_event_mine_recharge_finished("aiyu_event_mine_recharge_finished", "充值完成(不代表最后充值结果)"),
    aiyu_event_mine_recharge_cancel("aiyu_event_mine_recharge_cancel", "充值取消"),
    aiyu_event_mine_recharge_fail("aiyu_event_mine_recharge_fail", "充值失败"),


    // 8. 充值折扣页

    aiyu_event_newer_discount_enter("aiyu_event_newer_discount_enter", "进入新人折扣页面"),
    aiyu_event_newer_discount_package_buy_click("aiyu_event_newer_discount_package_buy_click", "新人优惠礼包购买点击"),
    aiyu_event_newer_discount_order_create("aiyu_event_newer_discount_order_create", "充值订单创建"),
    // 9. 福利中心
    aiyu_event_task_center_enter("aiyu_event_task_center_enter", "进入福利中"),;

    private String id;
    private String desc;

    EventCode(String id, String desc) {
        this.id = id;
        this.desc = desc;
    }

    @Override
    public String getId() {
        return id;
    }

    @Override
    public String getDesc() {
        return desc;
    }
}
