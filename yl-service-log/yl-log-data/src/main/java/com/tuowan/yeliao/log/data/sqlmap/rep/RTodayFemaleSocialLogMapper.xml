<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tuowan.yeliao.log.data.persistence.rep.RTodayFemaleSocialLogMapper">
  <sql id="Base_Column_List">
    date, user_id, income, msg_income, voice_call_income, video_call_income, online_duration, 
    be_voice_times, re_voice_times, be_voice_duration, voice_rate, be_video_times, re_video_times, 
    be_video_duration, video_rate, video_user, msg_times, msg_round, chat_up_times, receive_msg, 
    invite_call,voice_user
  </sql>
  <select id="selectByPrimaryKey" parameterType="RTodayFemaleSocialLog" resultType="RTodayFemaleSocialLog">
    select 
    <include refid="Base_Column_List" />
    from r_today_female_social_log
    where date = #{date}
      and user_id = #{userId}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="RTodayFemaleSocialLog">
    delete from r_today_female_social_log
    where date = #{date}
      and user_id = #{userId}
  </delete>
  <insert id="insert" parameterType="RTodayFemaleSocialLog">
    insert into r_today_female_social_log (date, user_id, income, msg_income, voice_call_income, video_call_income, 
      online_duration, be_voice_times, re_voice_times, be_voice_duration, voice_rate, 
      be_video_times, re_video_times, be_video_duration, video_rate, video_user, 
      msg_times, msg_round, chat_up_times, receive_msg, invite_call, voice_user)
    values (#{date}, #{userId}, #{income}, #{msgIncome}, #{voiceCallIncome}, #{videoCallIncome}, 
      #{onlineDuration}, #{beVoiceTimes}, #{reVoiceTimes}, #{beVoiceDuration}, #{voiceRate}, 
      #{beVideoTimes}, #{reVideoTimes}, #{beVideoDuration}, #{videoRate}, #{videoUser}, 
      #{msgTimes}, #{msgRound}, #{chatUpTimes}, #{receiveMsg}, #{inviteCall},#{voiceUser})
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="RTodayFemaleSocialLog">
    update r_today_female_social_log
    <set>
      <if test="income != null">
        income = #{income},
      </if>
      <if test="msgIncome != null">
        msg_income = #{msgIncome},
      </if>
      <if test="voiceCallIncome != null">
        voice_call_income = #{voiceCallIncome},
      </if>
      <if test="videoCallIncome != null">
        video_call_income = #{videoCallIncome},
      </if>
      <if test="onlineDuration != null">
        online_duration = #{onlineDuration},
      </if>
      <if test="beVoiceTimes != null">
        be_voice_times = #{beVoiceTimes},
      </if>
      <if test="reVoiceTimes != null">
        re_voice_times = #{reVoiceTimes},
      </if>
      <if test="beVoiceDuration != null">
        be_voice_duration = #{beVoiceDuration},
      </if>
      <if test="voiceRate != null">
        voice_rate = #{voiceRate},
      </if>
      <if test="beVideoTimes != null">
        be_video_times = #{beVideoTimes},
      </if>
      <if test="reVideoTimes != null">
        re_video_times = #{reVideoTimes},
      </if>
      <if test="beVideoDuration != null">
        be_video_duration = #{beVideoDuration},
      </if>
      <if test="videoRate != null">
        video_rate = #{videoRate},
      </if>
      <if test="videoUser != null">
        video_user = #{videoUser},
      </if>
      <if test="msgTimes != null">
        msg_times = #{msgTimes},
      </if>
      <if test="msgRound != null">
        msg_round = #{msgRound},
      </if>
      <if test="chatUpTimes != null">
        chat_up_times = #{chatUpTimes},
      </if>
      <if test="receiveMsg != null">
        receive_msg = #{receiveMsg},
      </if>
      <if test="inviteCall != null">
        invite_call = #{inviteCall},
      </if>
      <if test="voiceUser != null" >
        voice_user = #{voiceUser,jdbcType=INTEGER}
      </if>
    </set>
    where date = #{date}
      and user_id = #{userId}
  </update>
  <update id="updateByPrimaryKey" parameterType="RTodayFemaleSocialLog">
    update r_today_female_social_log
    set income = #{income},
      msg_income = #{msgIncome},
      voice_call_income = #{voiceCallIncome},
      video_call_income = #{videoCallIncome},
      online_duration = #{onlineDuration},
      be_voice_times = #{beVoiceTimes},
      re_voice_times = #{reVoiceTimes},
      be_voice_duration = #{beVoiceDuration},
      voice_rate = #{voiceRate},
      be_video_times = #{beVideoTimes},
      re_video_times = #{reVideoTimes},
      be_video_duration = #{beVideoDuration},
      video_rate = #{videoRate},
      video_user = #{videoUser},
      msg_times = #{msgTimes},
      msg_round = #{msgRound},
      chat_up_times = #{chatUpTimes},
      receive_msg = #{receiveMsg},
      invite_call = #{inviteCall},
      voice_user= #{voiceUser}
    where date = #{date}
      and user_id = #{userId}
  </update>

  <insert id="init" parameterType="RTodayFemaleSocialLog">
    insert into r_today_female_social_log (date, user_id)
    values (#{date}, #{userId})
  </insert>
</mapper>