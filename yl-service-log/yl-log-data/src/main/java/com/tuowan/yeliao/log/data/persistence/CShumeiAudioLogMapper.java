/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.log.data.persistence;

import com.easyooo.framework.sharding.annotation.Table;
import com.tuowan.yeliao.log.data.entity.CShumeiAudioLog;
import org.springframework.stereotype.Repository;

@Repository
@Table(value = "C_SHUMEI_AUDIO_LOG", schema = "YL_LOG")
public interface CShumeiAudioLogMapper {
    int deleteByPrimaryKey(CShumeiAudioLog record);

    int insert(CShumeiAudioLog record);

    CShumeiAudioLog selectByPrimaryKey(CShumeiAudioLog record);

    int updateByPrimaryKeySelective(CShumeiAudioLog record);

    int updateByPrimaryKey(CShumeiAudioLog record);
}