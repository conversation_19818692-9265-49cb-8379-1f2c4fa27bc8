/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.log.data.persistence;

import com.easyooo.framework.sharding.annotation.Table;
import com.tuowan.yeliao.log.data.entity.CUserFirstMsgRecord;
import org.springframework.stereotype.Repository;

@Repository
@Table(value = "c_user_first_msg_record", schema = "YL_LOG")
public interface CUserFirstMsgRecordMapper {
    int deleteByPrimaryKey(CUserFirstMsgRecord record);

    int insert(CUserFirstMsgRecord record);

    CUserFirstMsgRecord selectByPrimaryKey(CUserFirstMsgRecord record);

    int updateByPrimaryKeySelective(CUserFirstMsgRecord record);

    int updateByPrimaryKey(CUserFirstMsgRecord record);
}