<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tuowan.yeliao.log.data.persistence.rep.RDayFemaleIncomeLogMapper">
  <sql id="Base_Column_List">
    date, user_id, income, msg_income, voice_call_income, video_call_income, gift_income, 
    recent_3_day_income
  </sql>
  <select id="selectByPrimaryKey" parameterType="RDayFemaleIncomeLog" resultType="RDayFemaleIncomeLog">
    select 
    <include refid="Base_Column_List" />
    from r_day_female_income_log
    where date = #{date}
      and user_id = #{userId}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="RDayFemaleIncomeLog">
    delete from r_day_female_income_log
    where date = #{date}
      and user_id = #{userId}
  </delete>
  <insert id="insert" parameterType="RDayFemaleIncomeLog">
    insert into r_day_female_income_log (date, user_id, income, msg_income, voice_call_income, video_call_income, 
      gift_income, recent_3_day_income)
    values (#{date}, #{userId}, #{income}, #{msgIncome}, #{voiceCallIncome}, #{videoCallIncome}, 
      #{giftIncome}, #{recent3DayIncome})
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="RDayFemaleIncomeLog">
    update r_day_female_income_log
    <set>
      <if test="income != null">
        income = #{income},
      </if>
      <if test="msgIncome != null">
        msg_income = #{msgIncome},
      </if>
      <if test="voiceCallIncome != null">
        voice_call_income = #{voiceCallIncome},
      </if>
      <if test="videoCallIncome != null">
        video_call_income = #{videoCallIncome},
      </if>
      <if test="giftIncome != null">
        gift_income = #{giftIncome},
      </if>
      <if test="recent3DayIncome != null">
        recent_3_day_income = #{recent3DayIncome},
      </if>
    </set>
    where date = #{date}
      and user_id = #{userId}
  </update>
  <update id="updateByPrimaryKey" parameterType="RDayFemaleIncomeLog">
    update r_day_female_income_log
    set income = #{income},
      msg_income = #{msgIncome},
      voice_call_income = #{voiceCallIncome},
      video_call_income = #{videoCallIncome},
      gift_income = #{giftIncome},
      recent_3_day_income = #{recent3DayIncome}
    where date = #{date}
      and user_id = #{userId}
  </update>

  <insert id="init" parameterType="RDayFemaleIncomeLog">
    insert into r_day_female_income_log (date, user_id)
    values (#{date}, #{userId})
  </insert>

  <select id="selectByUserIdAndDateGte" parameterType="RDayFemaleIncomeLog" resultType="RDayFemaleIncomeLog">
    select
    <include refid="Base_Column_List" />
    from r_day_female_income_log
    where date >= #{date}
    and user_id = #{userId}
  </select>

  <select id="selectCashByUserIdAndDateGte" parameterType="RDayFemaleIncomeLog" resultType="java.lang.Long">
    select
    sum(income)
    from r_day_female_income_log
    where date >= #{date}
    and user_id = #{userId}
  </select>
</mapper>