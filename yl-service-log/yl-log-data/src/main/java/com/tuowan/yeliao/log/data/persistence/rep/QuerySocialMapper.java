package com.tuowan.yeliao.log.data.persistence.rep;

import com.easyooo.framework.sharding.annotation.Table;
import com.tuowan.yeliao.log.data.dto.rep.*;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;

@Repository
@Table(schema = "YL_REP_QUERY")
public interface QuerySocialMapper {
    // 获取今日男用户社交消耗数据
    MaleSocialConsumeDTO queryMaleSocialConsume(@Param("userId") Long userId, @Param("beginTime") Date beginTime, @Param("endTime") Date endTime);

    // 获取今日女用户私信信息
    FemaleMsgInfoDTO queryFemaleMsgInfo(@Param("userId") Long userId, @Param("beginTime") Date beginTime, @Param("endTime") Date endTime);

    // 获取今日女用户被叫通话信息
    FemaleBeCallInfoDTO queryFemaleBeCallInfo(@Param("friendId") Long friendId, @Param("beginTime") Date beginTime, @Param("endTime") Date endTime);

    // 获取今日女用户主叫通话信息
    FemaleSeCallInfoDTO queryFemaleSeCallInfo(@Param("userId") Long userId, @Param("beginTime") Date beginTime, @Param("endTime") Date endTime);

    // 获取今日女用户刺激消费数据
    FemaleFuelBeanInfoDTO queryFemaleFuelBeanInfo(@Param("userId") Long userId, @Param("beginTime") Date beginTime, @Param("endTime") Date endTime);

    FemaleTodayFateDTO queryFemaleTodayFateInfo(@Param("userId") Long userId, @Param("beginTime") Date beginTime, @Param("endTime") Date endTime);

    //系统推荐回复率，时长
    FemaleSystemPushInfoDTO queryFemaleSystemPushInfo(@Param("userId") Long userId, @Param("beginTime") Date beginTime, @Param("endTime") Date endTime);

    //被搭讪数据
    FemaleBeChatUpInfoDTO queryFemaleBeChatUpInfo(@Param("userId") Long userId, @Param("beginTime") Date beginTime, @Param("endTime") Date endTime);

    //今日缘分曝光
    FemaleSystemPushInfoDTO queryFemaleTodayFateShowInfo(@Param("userId") Long userId, @Param("beginTime") Date beginTime, @Param("endTime") Date endTime);
}
