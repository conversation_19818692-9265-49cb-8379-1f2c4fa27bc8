/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.log.data.persistence;

import com.easyooo.framework.sharding.annotation.Table;
import com.tuowan.yeliao.log.data.entity.CAdvertiserData;
import org.springframework.stereotype.Repository;

@Repository
@Table(value = "C_ADVERTISER_DATA", schema = "YL_LOG")
public interface CAdvertiserDataMapper {
    int deleteByPrimaryKey(CAdvertiserData record);

    int insert(CAdvertiserData record);

    CAdvertiserData selectByPrimaryKey(CAdvertiserData record);

    int updateByPrimaryKeySelective(CAdvertiserData record);

    int updateByPrimaryKey(CAdvertiserData record);
}