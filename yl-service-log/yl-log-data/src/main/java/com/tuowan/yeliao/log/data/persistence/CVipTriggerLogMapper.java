/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.log.data.persistence;

import com.easyooo.framework.sharding.annotation.Table;
import com.tuowan.yeliao.log.data.entity.CVipTriggerLog;
import org.springframework.stereotype.Repository;

@Repository
@Table(value = "C_VIP_TRIGGER_LOG", schema = "YL_LOG")
public interface CVipTriggerLogMapper {
    int deleteByPrimaryKey(CVipTriggerLog record);

    int insert(CVipTriggerLog record);

    CVipTriggerLog selectByPrimaryKey(CVipTriggerLog record);

    int updateByPrimaryKeySelective(CVipTriggerLog record);

    int updateByPrimaryKey(CVipTriggerLog record);
}