<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tuowan.yeliao.log.data.persistence.CPromotionAndroidMapper">
  <sql id="Base_Column_List">
    log_id, create_time, type, type_code, ad_id, device_id, device_id2, device_oaid, device_imei,
    callback, active_status, active_status_time, register_status, register_status_time, 
    pay_status, pay_status_time, register_user_id, ext_params, promotion_id,
    project_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="CPromotionAndroid" resultType="CPromotionAndroid">
    select 
    <include refid="Base_Column_List" />
    from c_promotion_android
    where log_id = #{logId}
      and create_time = #{createTime}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="CPromotionAndroid">
    delete from c_promotion_android
    where log_id = #{logId}
      and create_time = #{createTime}
  </delete>
  <insert id="insert" parameterType="CPromotionAndroid">
    insert into c_promotion_android (log_id, create_time, type, type_code, ad_id, device_id, device_id2,
      device_oaid, device_imei, callback, active_status, active_status_time, 
      register_status, register_status_time, pay_status, pay_status_time,
      register_user_id, ext_params, promotion_id, project_id)
    values (#{logId}, #{createTime}, #{type}, #{typeCode}, #{adId}, #{deviceId}, #{deviceId2},
      #{deviceOaid}, #{deviceImei}, #{callback}, #{activeStatus}, #{activeStatusTime}, 
      #{registerStatus}, #{registerStatusTime}, #{payStatus}, #{payStatusTime},
      #{registerUserId}, #{extParams}, #{promotionId}, #{projectId})
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="CPromotionAndroid">
    update c_promotion_android
    <set>
      <if test="type != null">
        type = #{type},
      </if>
      <if test="typeCode != null">
        type_code = #{typeCode},
      </if>
      <if test="adId != null">
        ad_id = #{adId},
      </if>
      <if test="deviceId != null">
        device_id = #{deviceId},
      </if>
      <if test="deviceId2 != null">
        device_id2 = #{deviceId2},
      </if>
      <if test="deviceOaid != null">
        device_oaid = #{deviceOaid},
      </if>
      <if test="deviceImei != null">
        device_imei = #{deviceImei},
      </if>
      <if test="callback != null">
        callback = #{callback},
      </if>
      <if test="activeStatus != null">
        active_status = #{activeStatus},
      </if>
      <if test="activeStatusTime != null">
        active_status_time = #{activeStatusTime},
      </if>
      <if test="registerStatus != null">
        register_status = #{registerStatus},
      </if>
      <if test="registerStatusTime != null">
        register_status_time = #{registerStatusTime},
      </if>
      <if test="payStatus != null">
        pay_status = #{payStatus},
      </if>
      <if test="payStatusTime != null">
        pay_status_time = #{payStatusTime},
      </if>
      <if test="registerUserId != null">
        register_user_id = #{registerUserId},
      </if>
      <if test="extParams != null">
        ext_params = #{extParams},
      </if>
      <if test="promotionId != null">
        promotion_id = #{promotionId},
      </if>
      <if test="projectId != null">
        project_id = #{projectId},
      </if>
    </set>
    where log_id = #{logId}
      and create_time = #{createTime}
  </update>
  <update id="updateByPrimaryKey" parameterType="CPromotionAndroid">
    update c_promotion_android
    set type = #{type},
      type_code = #{typeCode},
      ad_id = #{adId},
      device_id = #{deviceId},
      device_id2 = #{deviceId2},
      device_oaid = #{deviceOaid},
      device_imei = #{deviceImei},
      callback = #{callback},
      active_status = #{activeStatus},
      active_status_time = #{activeStatusTime},
      register_status = #{registerStatus},
      register_status_time = #{registerStatusTime},
      pay_status = #{payStatus},
      pay_status_time = #{payStatusTime},
      register_user_id = #{registerUserId},
      ext_params = #{extParams},
      promotion_id = #{promotionId},
      project_id = #{projectId}
    where log_id = #{logId}
      and create_time = #{createTime}
  </update>

  <select id="selectByDeviceId" resultType="CPromotionAndroid">
    select
    <include refid="Base_Column_List" />
    from c_promotion_android
    where device_id = #{deviceId} and type = #{type} and create_time &gt; date_sub(now(), interval 30 day)
    <if test="isActive">
      and active_status = 'S'
    </if>
    <if test="isReg">
      and register_status = 'S'
    </if>
    <if test="isPay">
      and pay_status = 'S'
    </if>
    order by log_id desc limit 1
  </select>
  <select id="selectByDeviceId2" resultType="CPromotionAndroid">
    select
    <include refid="Base_Column_List" />
    from c_promotion_android
    where device_id2 = #{deviceId2} and type = #{type} and create_time &gt; date_sub(now(), interval 30 day)
    <if test="isActive">
      and active_status = 'S'
    </if>
    <if test="isReg">
      and register_status = 'S'
    </if>
    <if test="isPay">
      and pay_status = 'S'
    </if>
    order by log_id desc limit 1
  </select>
</mapper>