package com.tuowan.yeliao.user.data.manager;

import com.tuowan.yeliao.commons.config.redis.RedisKey;
import com.tuowan.yeliao.commons.config.redis.template.BusiRedisTemplate;
import com.tuowan.yeliao.commons.core.enums.redis.BusiKeyDefine;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Set;

/**
 * 行为数据归档封装
 *
 * <AUTHOR>
 * @date 2022/7/18 11:29
 */
@Component
public class ActionStatArchiveManager {

    @Autowired
    private BusiRedisTemplate busiRedisTemplate;

    /**
     * 获取待统计的队列
     */
    public Set<String> getFemaleWaitStatQueue(String statDate) {
        return busiRedisTemplate.spops(buildFemaleActionStatQueueKey(statDate));
    }

    /**
     * 获取待统计的队列(男性用户)
     */
    public Set<String> getMaleWaitStatQueue(String statDate) {
        return busiRedisTemplate.spops(buildMaleActionStatQueueKey(statDate));
    }

    private RedisKey buildFemaleActionStatInfoKey(Long userId, String statDate) {
        return RedisKey.create(BusiKeyDefine.FemaleActionStatInfo, statDate, userId);
    }

    private RedisKey buildMaleActionStatInfoKey(Long userId, String statDate) {
        return RedisKey.create(BusiKeyDefine.MaleActionStatInfo, statDate, userId);
    }

    private RedisKey buildFemaleActionStatQueueKey(String statDate) {
        return RedisKey.create(BusiKeyDefine.FemaleActionStatQueue, statDate);
    }

    private RedisKey buildMaleActionStatQueueKey(String statDate) {
        return RedisKey.create(BusiKeyDefine.MaleActionStatQueue, statDate);
    }
}
