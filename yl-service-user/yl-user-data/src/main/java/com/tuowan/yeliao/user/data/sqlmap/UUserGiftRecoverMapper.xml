<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tuowan.yeliao.user.data.persistence.UUserGiftRecoverMapper">
    <sql id="Base_Column_List">
        log_id
        , user_id, report_id, recover_type, recover_amount, recover_status, recover_group_status, creator,
    recover_reason, remark, agree_limit_time, appeal_reason, appeal_user_mobile, click_destroy, confirm_time, agree_time, status_time, create_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="UUserGiftRecover" resultType="UUserGiftRecover">
        select
        <include refid="Base_Column_List"/>
        from u_user_gift_recover
        where log_id = #{logId}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="UUserGiftRecover">
        delete
        from u_user_gift_recover
        where log_id = #{logId}
    </delete>
    <insert id="insert" parameterType="UUserGiftRecover" keyProperty="logId" useGeneratedKeys="true">
        insert into u_user_gift_recover (log_id, user_id, report_id, recover_type, recover_amount, recover_status,
                                         recover_group_status,
                                         creator, recover_reason, remark, agree_limit_time, appeal_reason,
                                         appeal_user_mobile, click_destroy, confirm_time, agree_time, status_time,
                                         create_time)
        values (#{logId}, #{userId}, #{reportId}, #{recoverType}, #{recoverAmount}, #{recoverStatus},
                #{recoverGroupStatus},
                #{creator}, #{recoverReason}, #{remark}, #{agreeLimitTime}, #{appealReason}, #{appealUserMobile},
                #{clickDestroy}, #{confirmTime}, #{agreeTime}, #{statusTime},
                #{createTime})
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="UUserGiftRecover">
        update u_user_gift_recover
        <set>
            <if test="userId != null">
                user_id = #{userId},
            </if>
            <if test="reportId != null">
                report_id = #{reportId},
            </if>
            <if test="recoverType != null">
                recover_type = #{recoverType},
            </if>
            <if test="recoverAmount != null">
                recover_amount = #{recoverAmount},
            </if>
            <if test="recoverStatus != null">
                recover_status = #{recoverStatus},
            </if>
            <if test="recoverGroupStatus != null">
                recover_group_status = #{recoverGroupStatus},
            </if>
            <if test="creator != null">
                creator = #{creator},
            </if>
            <if test="recoverReason != null">
                recover_reason = #{recoverReason},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
            <if test="agreeLimitTime != null">
                agree_limit_time = #{agreeLimitTime},
            </if>
            <if test="appealReason != null">
                appeal_reason = #{appealReason},
            </if>
            <if test="appealUserMobile != null">
                appeal_user_mobile = #{appealUserMobile},
            </if>
            <if test="clickDestroy != null">
                click_destroy = #{clickDestroy},
            </if>
            <if test="confirmTime != null">
                confirm_time = #{confirmTime},
            </if>
            <if test="agreeTime != null">
                agree_time = #{agreeTime},
            </if>
            <if test="statusTime != null">
                status_time = #{statusTime},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
        </set>
        where log_id = #{logId}
    </update>
    <update id="updateByPrimaryKey" parameterType="UUserGiftRecover">
        update u_user_gift_recover
        set user_id              = #{userId},
            report_id            = #{reportId},
            recover_type         = #{recoverType},
            recover_amount       = #{recoverAmount},
            recover_status       = #{recoverStatus},
            recover_group_status = #{recoverGroupStatus},
            creator              = #{creator},
            recover_reason       = #{recoverReason},
            remark               = #{remark},
            agree_limit_time     = #{agreeLimitTime},
            appeal_reason        = #{appealReason},
            appeal_user_mobile   = #{appealUserMobile},
            click_destroy        = #{clickDestroy},
            confirm_time         = #{confirmTime},
            agree_time           = #{agreeTime},
            status_time          = #{statusTime},
            create_time          = #{createTime}
        where log_id = #{logId}
    </update>

    <select id="listUserGiftRecover" parameterType="java.util.Map" resultType="UUserGiftRecover">
        select
        <include refid="Base_Column_List"/>
        from u_user_gift_recover
        where user_id = #{userId} and recover_status not in ('BS', 'CS')
        order by log_id desc
        limit #{offset},#{limit}
    </select>

    <select id="existGiftRecoverLog" resultType="java.lang.Integer">
        select count(1)
        from u_user_gift_recover
        where user_id = #{userId} limit 1
    </select>

    <select id="queryByReportId" resultType="UUserGiftRecover">
        select
        <include refid="Base_Column_List"/>
        from u_user_gift_recover
        where report_id = #{reportId}
    </select>
</mapper>