<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tuowan.yeliao.user.data.persistence.UUserAccountMapper">
    <sql id="Base_Column_List">
        uid, login_type, package_type, user_id, create_time, master_id, status
    </sql>
    <select id="selectByPrimaryKey" parameterType="UUserAccount" resultType="UUserAccount">
        select
        <include refid="Base_Column_List"/>
        from u_user_account
        where uid = #{uid}
        and login_type = #{loginType}
        and package_type = #{packageType}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="UUserAccount">
        delete from u_user_account
        where uid = #{uid}
        and login_type = #{loginType}
        and package_type = #{packageType}
    </delete>
    <insert id="insert" parameterType="UUserAccount">
        insert into u_user_account (uid, login_type, package_type, user_id, create_time, master_id, status, operator, audit_time, msg
        )
        values (#{uid}, #{loginType}, #{packageType}, #{userId}, #{createTime}, #{masterId}, #{status}, #{operator}, #{auditTime}, #{msg}
        )
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="UUserAccount">
        update u_user_account
        <set>
            <if test="userId != null">
                user_id = #{userId},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="masterId != null">
                master_id = #{masterId},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="operator != null">
                operator = #{operator},
            </if>
            <if test="auditTime != null">
                audit_time = #{auditTime},
            </if>
            <if test="msg != null">
                msg = #{msg},
            </if>
        </set>
        where uid = #{uid}
        and login_type = #{loginType}
        and package_type = #{packageType}
    </update>
    <update id="updateByPrimaryKey" parameterType="UUserAccount">
        update u_user_account
        set user_id = #{userId},
        create_time = #{createTime},
        master_id = #{masterId},
        status = #{status},
        operator = #{operator},
        audit_time = #{auditTime},
        msg = #{msg}
        where uid = #{uid}
        and login_type = #{loginType}
        and package_type = #{packageType}
    </update>

    <select id="selectByUserId" parameterType="map" resultType="UUserAccount">
        select
        <include refid="Base_Column_List"/>
        from u_user_account
        where user_id = #{userId}
    </select>
</mapper>