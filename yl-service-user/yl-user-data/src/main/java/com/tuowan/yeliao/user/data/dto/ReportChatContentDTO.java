package com.tuowan.yeliao.user.data.dto;

import com.tuowan.yeliao.commons.data.enums.social.ChatContentType;
import com.tuowan.yeliao.user.data.entity.UUserReportChat;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ReportChatContentDTO {
    /**
     * 发送方用户标识
     */
    private Long sendUserId;
    /**
     * 内容类型
     */
    private ChatContentType chatContentType;
    /**
     * 内容值
     * 文本：具体内容
     * 语音：url
     * 图片：url
     */
    private String contentValue;
    /**
     * 发送时间
     */
    private Date sendTime;

    public static List<UUserReportChat> createDatas(List<ReportChatContentDTO> list, Long reportId, Date createTime) {
        List<UUserReportChat> resultList = new ArrayList<>();
        list.forEach(item -> {
            resultList.add(ReportChatContentDTO.build1(item, reportId, createTime));
        });
        return resultList;
    }

    public static UUserReportChat build1(ReportChatContentDTO dto, Long reportId, Date createTime) {
        UUserReportChat chat = new UUserReportChat();
        chat.setReportId(reportId);
        chat.setSendUserId(dto.getSendUserId());
        chat.setContentType(dto.getChatContentType());
        chat.setContentValue(dto.getContentValue());
        chat.setSendTime(dto.getSendTime());
        chat.setCreateTime(createTime);
        return chat;
    }

    public Long getSendUserId() {
        return sendUserId;
    }

    public void setSendUserId(Long sendUserId) {
        this.sendUserId = sendUserId;
    }

    public ChatContentType getChatContentType() {
        return chatContentType;
    }

    public void setChatContentType(ChatContentType chatContentType) {
        this.chatContentType = chatContentType;
    }

    public String getContentValue() {
        return contentValue;
    }

    public void setContentValue(String contentValue) {
        this.contentValue = contentValue;
    }

    public Date getSendTime() {
        return sendTime;
    }

    public void setSendTime(Date sendTime) {
        this.sendTime = sendTime;
    }
}
