package com.tuowan.yeliao.user.data.persistence.query;

import com.easyooo.framework.sharding.annotation.Table;
import com.tuowan.yeliao.user.data.dto.RelationTotalDTO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
@Table(schema = "YL_QUERY")
public interface SocialQueryMapper {

    /**
     * 查询客服
     */
    RelationTotalDTO getRelationTotal(@Param("userId") Long userId);
}
