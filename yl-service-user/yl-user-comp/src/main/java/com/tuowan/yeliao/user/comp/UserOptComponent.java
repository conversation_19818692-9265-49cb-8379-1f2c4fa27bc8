package com.tuowan.yeliao.user.comp;

import com.alibaba.nacos.common.utils.Pair;
import com.easyooo.framework.common.util.MsgUtils;
import com.easyooo.framework.common.util.StringUtils;
import com.tuowan.yeliao.commons.comp.notice.NoticeComponent;
import com.tuowan.yeliao.commons.config.configuration.impl.AppConfig;
import com.tuowan.yeliao.commons.config.redis.RedisKey;
import com.tuowan.yeliao.commons.config.redis.template.UserRedisTemplate;
import com.tuowan.yeliao.commons.config.utils.CallbackAfterTransactionUtil;
import com.tuowan.yeliao.commons.core.enums.redis.UserKeyDefine;
import com.tuowan.yeliao.commons.data.enums.config.NoticeSysType;
import com.tuowan.yeliao.user.data.support.IPAddrSupport;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class UserOptComponent {

    @Autowired
    private NoticeComponent noticeComponent;
    @Autowired
    private UserRedisTemplate userRedisTemplate;

    /**
     * 用户权限关闭【不是永久关闭】我们将用户加入权限恢复队列
     */
    public void addUserRightReleaseQueue(String member, double releaseTime){
        CallbackAfterTransactionUtil.send(() -> {
            userRedisTemplate.zadd(buildUserRightReleaseQueue(), releaseTime, member);
        });
    }

    /**
     * 从权限恢复队列中移除
     */
    public void removeUserRightReleaseQueue(String member){
        userRedisTemplate.zrem(buildUserRightReleaseQueue(), member);
    }

    /**
     * 获取待权限恢复数据
     */
    public List<String> queryUserRightReleaseDatas(){
        return userRedisTemplate.zrangeByScore(buildUserRightReleaseQueue(), 0D, (double) System.currentTimeMillis());
    }

    /**
     * 根据IP获取地域信息
     * @return first 省份信息
     * @return second 市区信息
     */
    public Pair<String, String> getIpAreaInfo(String ip){
        if(StringUtils.isEmpty(ip)){
            return Pair.with(null, null);
        }
        IPAddrSupport instance = IPAddrSupport.getInstance();
        return instance.getProvinceAndCity(ip);
    }

    /**
     * 礼物追回订单状态发生改变
     * 发送系统消息
     */
    public void noticeGiftRecoverStatusChange(Long userId, Long logId){
        Map<String, Object> params = new HashMap<>();
        params.put("overrideTouchValue", AppConfig.H5_URL + MsgUtils.format("/report/back?logId={}", logId));
        noticeComponent.sendSystemNotice(userId, NoticeSysType.GiftRecoverStatusChange, params);
    }

    /**
     * 标记状态已变更
     */
    public void markStatusChange(Long userId, String type, String sign){
        CallbackAfterTransactionUtil.send(() -> {
            userRedisTemplate.set(buildStatusChangeNoSee(userId, type, sign), "T");
        });
    }

    /**
     * 删除 状态已变更标记
     */
    public void removeStatusChange(Long userId, String type, String sign){
        CallbackAfterTransactionUtil.send(() -> {
            userRedisTemplate.del(buildStatusChangeNoSee(userId, type, sign));
        });
    }

    /**
     * 判断 状态已变更标记 是否存在
     */
    public boolean checkStatusChange(Long userId, String type, String sign){
        return userRedisTemplate.exists(buildStatusChangeNoSee(userId, type, sign));
    }

    /**
     * 构建 状态变更未查看标识
     */
    private RedisKey buildStatusChangeNoSee(Long userId, String type, String sign){
        return RedisKey.create(UserKeyDefine.StatusChangeNoSee, userId, type, sign);
    }

    /**
     * 用户message-setting权限恢复
     */
    private RedisKey buildUserRightReleaseQueue(){
        return RedisKey.create(UserKeyDefine.UserRightReleaseQueue);
    }
}
