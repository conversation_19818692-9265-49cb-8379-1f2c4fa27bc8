package com.tuowan.yeliao.user.web.form.cms;

import com.easyooo.framework.validate.config.LMNotNull;
import com.tuowan.yeliao.commons.web.common.form.Form;

import java.util.List;

public class ReportUserForm implements Form {
    /**
     * 被举报人
     */
    @LMNotNull
    private Long userId;

    /**
     * 举报类型：取数据字典
     */
    @LMNotNull
    private String reportType;

    /**
     * 举报详情
     */
    @LMNotNull
    private String detail;

    /**
     * 截图：多个以逗号隔开
     */
    @LMNotNull
    private List<String> pics;

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getReportType() {
        return reportType;
    }

    public void setReportType(String reportType) {
        this.reportType = reportType;
    }

    public String getDetail() {
        return detail;
    }

    public void setDetail(String detail) {
        this.detail = detail;
    }

    public List<String> getPics() {
        return pics;
    }

    public void setPics(List<String> pics) {
        this.pics = pics;
    }
}
