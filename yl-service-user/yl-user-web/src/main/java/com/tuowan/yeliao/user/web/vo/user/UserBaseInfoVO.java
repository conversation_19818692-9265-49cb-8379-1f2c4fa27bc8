package com.tuowan.yeliao.user.web.vo.user;

import com.tuowan.yeliao.commons.core.enums.general.BoolType;
import com.tuowan.yeliao.commons.core.enums.general.user.UserType;
import com.tuowan.yeliao.commons.data.dto.user.TagDTO;
import com.tuowan.yeliao.commons.data.enums.general.Status;
import com.tuowan.yeliao.commons.data.enums.user.AcceptStatus;
import com.tuowan.yeliao.commons.data.enums.user.OnlineStatus;
import com.tuowan.yeliao.commons.data.enums.user.SexType;
import com.tuowan.yeliao.commons.data.enums.user.VipType;

import java.util.List;

/**
 * 用户基本信息
 *
 * <AUTHOR>
 * @date 2020/7/11 15:06
 */
public class UserBaseInfoVO {

    /** 用户userId */
    private Long userId;
    /** 用户号 */
    private String inviteCode;
    /** 用户昵称 */
    private String nickname;
    /** 头像 */
    private String headPic;
    /** 用户性别 */
    private SexType sex;
    /** 生日（出生日期）1996-10-10 */
    private String birthDay;
    /** 用户年龄 */
    private Integer age;
    /** 用户类型 */
    private UserType userType;
    /** 真人认证 */
    private BoolType realPerson;
    /** 是否实名认证 */
    private BoolType realName;
    /** 魅力等级 */
    private Integer charmLevel;
    /** 财富等级（用户等级）*/
    private Integer userLevel;
    /** 壕气等级 */
    private Integer hqLevel;
    /** 资料是否完成 */
    private BoolType infoSucc;
    /** 身高 178cm*/
    private Integer height;
    /** 体重 80kg*/
    private Integer weight;
    /** 现居地址 */
    private String stationCityAddr;
    /** 当前所在城市（客户端定位获取传给服务端：杭州市） */
    private String city;
    /** 家乡地址 */
    private String homeTownAddr;
    /** 婚姻状态 */
    private String marriageStatus;
    /** 学历 */
    private String education;
    /** 职业 */
    private String profession;
    /** 收入 */
    private String income;
    /** 是否接受婚前同居 */
    private String liveTogether;
    /** 是否接受约会 */
    private String appointment;
    /** 交友心愿（签名：u_user_basic my_sign） */
    private String mySign;
    /** 居住状态 */
    private String liveStatus;
    /** 购房状态 */
    private String houseStatus;
    /** 购车状态 */
    private String carStatus;
    /** 资料是否完善 */
    private BoolType dataInfoPerfect;
    /**
     * 已选标签数
     */
    private Integer myTagNum;
    /**
     * 可选标签数
     */
    private Integer totalMyTagNum;
    /**
     * 我的标签信息
     */
    private List<TagDTO> myTags;
    /**
     * 喜欢的TA标签信息
     */
    private List<TagDTO> taTags;
    /**
     * 想一起标签信息
     */
    private List<TagDTO> togetherTags;
    /**
     * 足迹标签信息
     */
    private List<String> footprints;
    /**
     * 封面信息
     */
    private List<String> coverInfos;
    /**
     * VIP类型
     */
    private VipType vipType;
    /**
     * VIP到期时间
     */
    private String vipExpireTimeText;
    /**
     * 是否显示等级信息
     */
    private BoolType hideLevel;
    /**
     * ip城市
     */
    private String ipCity;
    /**
     * 视频封面地址
     */
    private String videoCover;
    /**
     * 视频地址
     */
    private String videoUrl;
    /**
     * 用户金币余额
     */
    private Long beans;
    /**
     * 语音签名地址
     */
    private String voiceUrl;
    /**
     * 我的关注数
     */
    private Integer followCnt;
    /**
     * 我的粉丝数
     */
    private Integer fansCnt;
    /**
     * 我的访客数
     */
    private Integer visitCnt;
    /**
     * 星座
     */
    private String constellationText;
    /**
     * 在线状态
     */
    private OnlineStatus onlineStatus;
    /**
     * 状态状态
     */
    private Status status;

    public UserBaseInfoVO() {
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getInviteCode() {
        return inviteCode;
    }

    public void setInviteCode(String inviteCode) {
        this.inviteCode = inviteCode;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getHeadPic() {
        return headPic;
    }

    public void setHeadPic(String headPic) {
        this.headPic = headPic;
    }

    public SexType getSex() {
        return sex;
    }

    public void setSex(SexType sex) {
        this.sex = sex;
    }

    public Integer getAge() {
        return age;
    }

    public void setAge(Integer age) {
        this.age = age;
    }

    public UserType getUserType() {
        return userType;
    }

    public void setUserType(UserType userType) {
        this.userType = userType;
    }

    public BoolType getRealPerson() {
        return realPerson;
    }

    public void setRealPerson(BoolType realPerson) {
        this.realPerson = realPerson;
    }

    public BoolType getRealName() {
        return realName;
    }

    public void setRealName(BoolType realName) {
        this.realName = realName;
    }

    public Integer getCharmLevel() {
        return charmLevel;
    }

    public void setCharmLevel(Integer charmLevel) {
        this.charmLevel = charmLevel;
    }

    public Integer getUserLevel() {
        return userLevel;
    }

    public void setUserLevel(Integer userLevel) {
        this.userLevel = userLevel;
    }

    public Integer getHqLevel() {
        return hqLevel;
    }

    public void setHqLevel(Integer hqLevel) {
        this.hqLevel = hqLevel;
    }

    public BoolType getInfoSucc() {
        return infoSucc;
    }

    public void setInfoSucc(BoolType infoSucc) {
        this.infoSucc = infoSucc;
    }

    public Integer getHeight() {
        return height;
    }

    public void setHeight(Integer height) {
        this.height = height;
    }

    public Integer getWeight() {
        return weight;
    }

    public void setWeight(Integer weight) {
        this.weight = weight;
    }

    public String getStationCityAddr() {
        return stationCityAddr;
    }

    public void setStationCityAddr(String stationCityAddr) {
        this.stationCityAddr = stationCityAddr;
    }

    public String getHomeTownAddr() {
        return homeTownAddr;
    }

    public void setHomeTownAddr(String homeTownAddr) {
        this.homeTownAddr = homeTownAddr;
    }

    public String getMarriageStatus() {
        return marriageStatus;
    }

    public void setMarriageStatus(String marriageStatus) {
        this.marriageStatus = marriageStatus;
    }

    public String getEducation() {
        return education;
    }

    public void setEducation(String education) {
        this.education = education;
    }

    public String getProfession() {
        return profession;
    }

    public void setProfession(String profession) {
        this.profession = profession;
    }

    public String getIncome() {
        return income;
    }

    public void setIncome(String income) {
        this.income = income;
    }

    public String getLiveTogether() {
        return liveTogether;
    }

    public void setLiveTogether(String liveTogether) {
        this.liveTogether = liveTogether;
    }

    public String getAppointment() {
        return appointment;
    }

    public void setAppointment(String appointment) {
        this.appointment = appointment;
    }

    public String getMySign() {
        return mySign;
    }

    public void setMySign(String mySign) {
        this.mySign = mySign;
    }

    public List<String> getCoverInfos() {
        return coverInfos;
    }

    public void setCoverInfos(List<String> coverInfos) {
        this.coverInfos = coverInfos;
    }

    public List<TagDTO> getMyTags() {
        return myTags;
    }

    public void setMyTags(List<TagDTO> myTags) {
        this.myTags = myTags;
    }

    public List<TagDTO> getTaTags() {
        return taTags;
    }

    public void setTaTags(List<TagDTO> taTags) {
        this.taTags = taTags;
    }

    public List<TagDTO> getTogetherTags() {
        return togetherTags;
    }

    public void setTogetherTags(List<TagDTO> togetherTags) {
        this.togetherTags = togetherTags;
    }

    public List<String> getFootprints() {
        return footprints;
    }

    public void setFootprints(List<String> footprints) {
        this.footprints = footprints;
    }

    public String getBirthDay() {
        return birthDay;
    }

    public void setBirthDay(String birthDay) {
        this.birthDay = birthDay;
    }

    public String getLiveStatus() {
        return liveStatus;
    }

    public void setLiveStatus(String liveStatus) {
        this.liveStatus = liveStatus;
    }

    public String getHouseStatus() {
        return houseStatus;
    }

    public void setHouseStatus(String houseStatus) {
        this.houseStatus = houseStatus;
    }

    public String getCarStatus() {
        return carStatus;
    }

    public void setCarStatus(String carStatus) {
        this.carStatus = carStatus;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public BoolType getDataInfoPerfect() {
        return dataInfoPerfect;
    }

    public void setDataInfoPerfect(BoolType dataInfoPerfect) {
        this.dataInfoPerfect = dataInfoPerfect;
    }

    public Integer getMyTagNum() {
        return myTagNum;
    }

    public void setMyTagNum(Integer myTagNum) {
        this.myTagNum = myTagNum;
    }

    public Integer getTotalMyTagNum() {
        return totalMyTagNum;
    }

    public void setTotalMyTagNum(Integer totalMyTagNum) {
        this.totalMyTagNum = totalMyTagNum;
    }

    public VipType getVipType() {
        return vipType;
    }

    public void setVipType(VipType vipType) {
        this.vipType = vipType;
    }

    public String getVipExpireTimeText() {
        return vipExpireTimeText;
    }

    public void setVipExpireTimeText(String vipExpireTimeText) {
        this.vipExpireTimeText = vipExpireTimeText;
    }

    public BoolType getHideLevel() {
        return hideLevel;
    }

    public void setHideLevel(BoolType hideLevel) {
        this.hideLevel = hideLevel;
    }

    public String getIpCity() {
        return ipCity;
    }

    public void setIpCity(String ipCity) {
        this.ipCity = ipCity;
    }

    public String getVideoCover() {
        return videoCover;
    }

    public void setVideoCover(String videoCover) {
        this.videoCover = videoCover;
    }

    public String getVideoUrl() {
        return videoUrl;
    }

    public void setVideoUrl(String videoUrl) {
        this.videoUrl = videoUrl;
    }

    public Long getBeans() {
        return beans;
    }

    public void setBeans(Long beans) {
        this.beans = beans;
    }

    public String getVoiceUrl() {
        return voiceUrl;
    }

    public void setVoiceUrl(String voiceUrl) {
        this.voiceUrl = voiceUrl;
    }

    public Integer getFollowCnt() {
        return followCnt;
    }

    public void setFollowCnt(Integer followCnt) {
        this.followCnt = followCnt;
    }

    public Integer getFansCnt() {
        return fansCnt;
    }

    public void setFansCnt(Integer fansCnt) {
        this.fansCnt = fansCnt;
    }

    public Integer getVisitCnt() {
        return visitCnt;
    }

    public void setVisitCnt(Integer visitCnt) {
        this.visitCnt = visitCnt;
    }

    public String getConstellationText() {
        return constellationText;
    }

    public void setConstellationText(String constellationText) {
        this.constellationText = constellationText;
    }

    public OnlineStatus getOnlineStatus() {
        return onlineStatus;
    }

    public void setOnlineStatus(OnlineStatus onlineStatus) {
        this.onlineStatus = onlineStatus;
    }

    public Status getStatus() {
        return status;
    }

    public void setStatus(Status status) {
        this.status = status;
    }
}
