package com.tuowan.yeliao.user.web.form.acct.auth;

import com.easyooo.framework.validate.config.LMNotEmpty;
import com.tuowan.yeliao.commons.web.common.form.Form;

public class RealNameForm implements Form {
    /**
     * 真实姓名
     */
    @LMNotEmpty
    private String realName;
    /**
     * 省份证号
     */
    @LMNotEmpty
    private String certNum;
    /**
     * 元数据
     */
    @LMNotEmpty
    private String metaInfo;


    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public String getCertNum() {
        return certNum;
    }

    public void setCertNum(String certNum) {
        this.certNum = certNum;
    }

    public String getMetaInfo() {
        return metaInfo;
    }

    public void setMetaInfo(String metaInfo) {
        this.metaInfo = metaInfo;
    }
}
