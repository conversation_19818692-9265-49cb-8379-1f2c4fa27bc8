package com.tuowan.yeliao.user.web.form.acct.cancel;

import com.tuowan.yeliao.commons.core.enums.general.BoolType;
import com.tuowan.yeliao.commons.data.enums.config.DestroyReasonType;
import com.tuowan.yeliao.commons.web.common.form.Form;

public class DestroyAccountForm implements Form {
    /**
     * 注销原因类型
     */
    private DestroyReasonType reasonType = DestroyReasonType.Other;
    /**
     * 另外的原因
     */
    private String otherReason;
    /**
     * 是否需要验证标识
     * 用来适配 现金追回的注销
     */
    private BoolType needVerifySign = BoolType.True;

    public static DestroyAccountForm build1(String otherReason) {
        DestroyAccountForm form = new DestroyAccountForm();
        form.setReasonType(DestroyReasonType.Other);
        form.setOtherReason(otherReason);
        form.setNeedVerifySign(BoolType.False);
        return form;
    }

    public DestroyReasonType getReasonType() {
        return reasonType;
    }

    public void setReasonType(DestroyReasonType reasonType) {
        this.reasonType = reasonType;
    }

    public String getOtherReason() {
        return otherReason;
    }

    public void setOtherReason(String otherReason) {
        this.otherReason = otherReason;
    }

    public BoolType getNeedVerifySign() {
        return needVerifySign;
    }

    public void setNeedVerifySign(BoolType needVerifySign) {
        this.needVerifySign = needVerifySign;
    }
}
