package com.tuowan.yeliao.user.web.form.cms;

import com.tuowan.yeliao.commons.web.common.form.Form;

public class AwardGoodsForm implements Form {
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 物品ID
     */
    private Integer goodsId;
    /**
     * 数量或有效天
     */
    private Integer count;
    /**
     * 奖励说明
     */
    private String remark;
    /**
     * 过期有效天：按数量管理的物品才需要这个字段
     */
    private Integer expDays;

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Integer getGoodsId() {
        return goodsId;
    }

    public void setGoodsId(Integer goodsId) {
        this.goodsId = goodsId;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getExpDays() {
        return expDays;
    }

    public void setExpDays(Integer expDays) {
        this.expDays = expDays;
    }
}
