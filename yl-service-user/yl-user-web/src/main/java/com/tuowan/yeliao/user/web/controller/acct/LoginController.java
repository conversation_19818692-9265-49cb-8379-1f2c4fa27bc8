package com.tuowan.yeliao.user.web.controller.acct;

import com.tuowan.yeliao.commons.context.config.Request;
import com.tuowan.yeliao.commons.context.config.SessionType;
import com.tuowan.yeliao.commons.core.http.ReturnUtils;
import com.tuowan.yeliao.commons.core.http.Root;
import com.tuowan.yeliao.commons.web.common.form.SexForm;
import com.tuowan.yeliao.user.service.acct.LoginService;
import com.tuowan.yeliao.user.web.form.acct.login.*;
import com.tuowan.yeliao.user.web.vo.acct.login.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @title 用户登录控制器
 */
@RestController
@RequestMapping("/usr/acct")
public class LoginController {

    private final Logger LOG = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private LoginService loginService;

    /**
     * @title 发送验证码
     */
    @Request(SessionType.None)
    @RequestMapping("/sendSmsCode")
    public Root<Void> sendSmsCode(@RequestBody SendSmsCodeForm form) {
        loginService.saveSendSmsCode(form);
        return ReturnUtils.empty();
    }

    /**
     * @title 验证码验证
     */
    @Request(SessionType.None)
    @RequestMapping("/smsCodeVerify")
    public Root<Void> smsCodeVerify(@RequestBody SmsCodeVerifyForm form) {
        loginService.smsCodeVerify(form);
        return ReturnUtils.empty();
    }

    /**
     * @title 用户登录
     */
    @Request(SessionType.None)
    @RequestMapping("/login")
    public Root<LoginVO> login(@RequestBody UserLoginForm form) {
        // 参数前置校验，避免创建事务的开销
        form.paramsCheck();
        LoginVO result = loginService.saveLogin(form);
        return ReturnUtils.root(result);
    }

    /**
     * @title 用户注册
     */
    @Request(SessionType.None)
    @RequestMapping("/register")
    public Root<LoginVO> register(@RequestBody UserRegisterForm form) {
        return ReturnUtils.root(loginService.saveRegister(form));
    }

    /**
     * @title 用户注册手机号绑定
     */
    @Request(SessionType.None)
    @RequestMapping("/register/bindMobile")
    public Root<Void> bindMobile(@RequestBody BindMobileForm form) {
        loginService.saveBindMobile(form);
        return ReturnUtils.empty();
    }

    /**
     * @title 获取微信用户信息
     */
    @Request(SessionType.None)
    @RequestMapping("/wxUserInfo")
    public Root<WxUserInfoVO> wxUserInfo(@RequestBody WxUserInfoForm form) {
        return ReturnUtils.root(loginService.queryWxUserInfo(form));
    }

    /**
     * @title 退出登陆
     */
    @Request
    @RequestMapping("/logout")
    public Root<Void> logout() {
        loginService.logout();
        return ReturnUtils.empty();
    }

    /**
     * 获取随机昵称
     * 备注：仅男性支持随机昵称
     */
    @Request(session = SessionType.None)
    @RequestMapping("/randomNickname")
    public Root<RandomNicknameVO> randomNickname(@RequestBody SexForm form) {
        return ReturnUtils.root(loginService.randomNickname(form));
    }

    /**
     * 获取随机头像
     */
    @Request(session = SessionType.None)
    @RequestMapping("/randomHeadPic")
    public Root<RandomHeadPicVO> randomHeadPic(@RequestBody SexForm form) {
        return ReturnUtils.root(loginService.randomHeadPic(form));
    }

    /**
     * 【拾缘】女用户注册催审
     */
    @Request(session = SessionType.None)
    @RequestMapping("/urgeAudit")
    public Root<UrgeAuditVO> urgeAudit() {
        return ReturnUtils.root(loginService.urgeAudit());
    }
}