package com.tuowan.yeliao.user.web.vo.acct.login;

import com.easyooo.framework.common.enums.DatePattern;
import com.easyooo.framework.common.util.DateUtils;
import com.easyooo.framework.common.util.StringUtils;
import com.tuowan.yeliao.commons.context.GlobalUtils;
import com.tuowan.yeliao.commons.core.enums.general.BoolType;
import com.tuowan.yeliao.commons.core.enums.general.ClientType;
import com.tuowan.yeliao.commons.core.enums.general.LoginType;
import com.tuowan.yeliao.commons.core.enums.general.user.UserType;
import com.tuowan.yeliao.commons.data.entity.user.UUserBasic;
import com.tuowan.yeliao.commons.data.entity.user.UUserMore;
import com.tuowan.yeliao.commons.data.enums.check.RegComplete;
import com.tuowan.yeliao.commons.data.enums.user.SexType;
import com.tuowan.yeliao.commons.data.utils.BusiUtils;
import com.tuowan.yeliao.user.data.dto.LoginBanPopDTO;

/**
 * 微信和qq登录封装Dto
 *
 * <AUTHOR>
 * @date 2020/7/4 09:15
 */
public class LoginVO {

    /**
     * 用户昵称
     */
    private String nickname;
    /**
     * 头像
     */
    private String headPic;
    /**
     * 用户性别
     */
    private SexType sex;
    /**
     * 年龄
     */
    private Integer age;
    /**
     * 出生日期
     */
    private String birthDate;
    /**
     * 融云 token
     */
    private String imToken;
    /**
     * 用户sessionId
     */
    private String sessionId;
    /**
     * 登陆用户ID
     */
    private Long userId;
    /**
     * 用户类型
     */
    private UserType userType;
    /**
     * 客户端类型
     */
    private ClientType clientType;
    /**
     * 此次登录类型
     */
    private LoginType loginType;
    /**
     * 真人认证
     */
    private BoolType realPerson;
    /**
     * 是否实名认证
     */
    private BoolType realName;
    /**
     * 用户定位城市
     */
    private String city;

    /**
     * 注册标识
     * 需要注册时返回该字段
     */
    private RegComplete regComplete;
    /**
     * 注册uid
     */
    private String uid;
    /**
     * 是否需要走手机号绑定
     */
    private BoolType goBindMobile;
    /**
     * 封禁弹窗
     */
    private LoginBanPopDTO loginBanPopDTO;

    /**
     * 构建方法1
     * 提供登录成功实例化返回对象时使用
     *
     * @return
     */
    public static LoginVO build1(UUserBasic user, UUserMore userMore, String sessionId, LoginType loginType, String uid) {
        LoginVO vo = new LoginVO();
        vo.setNickname(user.getNickname());
        vo.setHeadPic(user.getHeadPic());
        vo.setSex(user.getSex());
        vo.setAge(BusiUtils.getAgeByDate(user.getBirthDate()));
        if (null != user.getBirthDate()) {
            vo.setBirthDate(DateUtils.toString(user.getBirthDate(), DatePattern.YMD));
        }
        // 如果为空设置为#，否则安卓客户端不会重新生成Token
        vo.setImToken(StringUtils.defaultString(userMore.getImToken(), "#"));
        vo.setSessionId(sessionId);
        vo.setUserId(user.getUserId());
        vo.setUserType(user.getUserType());
        vo.setClientType(GlobalUtils.clientType());
        vo.setLoginType(loginType);
        vo.setUid(uid);
        vo.setRegComplete(RegComplete.True);
        vo.setRealName(user.getRealName());
        vo.setRealPerson(user.getRealPerson());
        return vo;
    }

    /**
     * 构建方法2
     * 需要注册时，实例化对象
     *
     * @return
     */
    public static LoginVO build2(LoginType loginType, String uid) {
        LoginVO vo = new LoginVO();
        vo.setRegComplete(RegComplete.False);
        vo.setLoginType(loginType);
        vo.setUid(uid);
        return vo;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getHeadPic() {
        return headPic;
    }

    public void setHeadPic(String headPic) {
        this.headPic = headPic;
    }

    public Integer getAge() {
        return age;
    }

    public void setAge(Integer age) {
        this.age = age;
    }

    public SexType getSex() {
        return sex;
    }

    public void setSex(SexType sex) {
        this.sex = sex;
    }

    public String getBirthDate() {
        return birthDate;
    }

    public void setBirthDate(String birthDate) {
        this.birthDate = birthDate;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public UserType getUserType() {
        return userType;
    }

    public void setUserType(UserType userType) {
        this.userType = userType;
    }

    public ClientType getClientType() {
        return clientType;
    }

    public void setClientType(ClientType clientType) {
        this.clientType = clientType;
    }

    public LoginType getLoginType() {
        return loginType;
    }

    public void setLoginType(LoginType loginType) {
        this.loginType = loginType;
    }

    public RegComplete getRegComplete() {
        return regComplete;
    }

    public void setRegComplete(RegComplete regComplete) {
        this.regComplete = regComplete;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getImToken() {
        return imToken;
    }

    public void setImToken(String imToken) {
        this.imToken = imToken;
    }

    public BoolType getRealPerson() {
        return realPerson;
    }

    public void setRealPerson(BoolType realPerson) {
        this.realPerson = realPerson;
    }

    public BoolType getRealName() {
        return realName;
    }

    public void setRealName(BoolType realName) {
        this.realName = realName;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public BoolType getGoBindMobile() {
        return goBindMobile;
    }

    public void setGoBindMobile(BoolType goBindMobile) {
        this.goBindMobile = goBindMobile;
    }

    public LoginBanPopDTO getLoginBanPopDTO() {
        return loginBanPopDTO;
    }

    public void setLoginBanPopDTO(LoginBanPopDTO loginBanPopDTO) {
        this.loginBanPopDTO = loginBanPopDTO;
    }
}
