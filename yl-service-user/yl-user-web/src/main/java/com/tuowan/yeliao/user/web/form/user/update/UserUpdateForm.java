package com.tuowan.yeliao.user.web.form.user.update;

import com.easyooo.framework.common.util.StringUtils;
import com.easyooo.framework.validate.config.LMLength;
import com.tuowan.yeliao.commons.data.enums.config.EducationType;
import com.tuowan.yeliao.commons.data.enums.config.IncomeType;
import com.tuowan.yeliao.commons.data.enums.user.*;
import com.tuowan.yeliao.commons.web.common.form.Form;

import java.util.List;

/**
 * 修改用户信息表单
 *
 * <AUTHOR>
 * @date 2020/7/1 17:09
 */
public class UserUpdateForm implements Form {

    /**
     * 用户昵称
     */
    @LMLength(label = "用户昵称", max = 8)
    private String nickname;

    /**
     * 出生日期 格式 1993-09-04
     */
    private String birthday;

    /**
     * 个性签名
     */
    @LMLength(label = "个性签名", max = 50)
    private String mySign;

    /**
     * 头像
     */
    private String headPic;

    /**
     * 身高
     */
    private Integer height;

    /**
     * 体重
     */
    private Integer weight;

    /**
     * 婚姻状态
     */
    private MarriageStatus marriageStatus;

    /**
     * 居住状态
     */
    private LiveStatus liveStatus;

    /**
     * 住房状态
     */
    private HouseStatus houseStatus;

    /**
     * 购车状态
     */
    private CarStatus carStatus;

    /**
     * 是否接受婚前同居
     */
    private AcceptStatus liveTogetherStatus;

    /**
     * 是否接受约会
     */
    private AcceptStatus appointmentStatus;

    /**
     * 学历
     */
    private EducationType education;

    /**
     * 年收入
     */
    private IncomeType income;

    /**
     * 故乡城市id
     */
    private Integer hometownCityId;

    /**
     * 当前驻地城市id
     */
    //private Integer stationCityId;

    /**
     * 职业id
     */
    private Integer professionId;

    /**
     * 用户静态封面
     */
    private List<String> staticCovers;

    /**
     * 我的标签
     */
    private List<Integer> myTags;

    /**
     * 喜欢的Ta的标签
     */
    private List<Integer> loveTaTags;

    /**
     * 想要一起标签
     */
    private List<Integer> togetherTags;

    /**
     * 足迹
     */
    private List<String> footprints;

    public String getNickname() {
        return StringUtils.trimToNull(nickname);
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getBirthday() {
        return birthday;
    }

    public void setBirthday(String birthday) {
        this.birthday = birthday;
    }


    public String getMySign() {
        return mySign;
    }

    public void setMySign(String mySign) {
        this.mySign = mySign;
    }

    public Integer getHeight() {
        return height;
    }

    public void setHeight(Integer height) {
        this.height = height;
    }

    public Integer getWeight() {
        return weight;
    }

    public void setWeight(Integer weight) {
        this.weight = weight;
    }

    public MarriageStatus getMarriageStatus() {
        return marriageStatus;
    }

    public void setMarriageStatus(MarriageStatus marriageStatus) {
        this.marriageStatus = marriageStatus;
    }

    public LiveStatus getLiveStatus() {
        return liveStatus;
    }

    public void setLiveStatus(LiveStatus liveStatus) {
        this.liveStatus = liveStatus;
    }

    public HouseStatus getHouseStatus() {
        return houseStatus;
    }

    public void setHouseStatus(HouseStatus houseStatus) {
        this.houseStatus = houseStatus;
    }

    public CarStatus getCarStatus() {
        return carStatus;
    }

    public void setCarStatus(CarStatus carStatus) {
        this.carStatus = carStatus;
    }

    public AcceptStatus getLiveTogetherStatus() {
        return liveTogetherStatus;
    }

    public void setLiveTogetherStatus(AcceptStatus liveTogetherStatus) {
        this.liveTogetherStatus = liveTogetherStatus;
    }

    public AcceptStatus getAppointmentStatus() {
        return appointmentStatus;
    }

    public void setAppointmentStatus(AcceptStatus appointmentStatus) {
        this.appointmentStatus = appointmentStatus;
    }

    public EducationType getEducation() {
        return education;
    }

    public void setEducation(EducationType education) {
        this.education = education;
    }

    public IncomeType getIncome() {
        return income;
    }

    public void setIncome(IncomeType income) {
        this.income = income;
    }

    public Integer getHometownCityId() {
        return hometownCityId;
    }

    public void setHometownCityId(Integer hometownCityId) {
        this.hometownCityId = hometownCityId;
    }

    /*public Integer getStationCityId() {
        return stationCityId;
    }

    public void setStationCityId(Integer stationCityId) {
        this.stationCityId = stationCityId;
    }*/

    public Integer getProfessionId() {
        return professionId;
    }

    public void setProfessionId(Integer professionId) {
        this.professionId = professionId;
    }

    public String getHeadPic() {
        return headPic;
    }

    public void setHeadPic(String headPic) {
        this.headPic = headPic;
    }

    public List<String> getStaticCovers() {
        return staticCovers;
    }

    public void setStaticCovers(List<String> staticCovers) {
        this.staticCovers = staticCovers;
    }

    public List<Integer> getMyTags() {
        return myTags;
    }

    public void setMyTags(List<Integer> myTags) {
        this.myTags = myTags;
    }

    public List<Integer> getLoveTaTags() {
        return loveTaTags;
    }

    public void setLoveTaTags(List<Integer> loveTaTags) {
        this.loveTaTags = loveTaTags;
    }

    public List<Integer> getTogetherTags() {
        return togetherTags;
    }

    public void setTogetherTags(List<Integer> togetherTags) {
        this.togetherTags = togetherTags;
    }

    public List<String> getFootprints() {
        return footprints;
    }

    public void setFootprints(List<String> footprints) {
        this.footprints = footprints;
    }
}
