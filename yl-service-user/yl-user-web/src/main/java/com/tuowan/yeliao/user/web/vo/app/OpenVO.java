package com.tuowan.yeliao.user.web.vo.app;

import com.tuowan.yeliao.commons.core.enums.general.BoolType;

/**
 * App 控制接口返回参数
 */
public class OpenVO {
    /**
     * 是否允许进入APP
     */
    private BoolType isAgree;
    /**
     * 提示信息（当isAgree为False时这个值才有意义）
     */
    private String msg;
    /**
     * 提示信息展示倒计时（当isAgree为False时这个值才有意义）
     */
    private Integer countDown;


    public OpenVO() {
    }

    public OpenVO(BoolType isAgree) {
        this.isAgree = isAgree;
    }

    public BoolType getIsAgree() {
        return isAgree;
    }

    public void setIsAgree(BoolType isAgree) {
        this.isAgree = isAgree;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public Integer getCountDown() {
        return countDown;
    }

    public void setCountDown(Integer countDown) {
        this.countDown = countDown;
    }
}
