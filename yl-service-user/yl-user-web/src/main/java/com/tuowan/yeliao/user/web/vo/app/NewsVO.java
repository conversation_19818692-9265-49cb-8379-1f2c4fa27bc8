package com.tuowan.yeliao.user.web.vo.app;


import com.tuowan.yeliao.commons.core.enums.general.BoolType;

import java.util.List;

/**
 * app启动检查更新接口返回值
 *
 * <AUTHOR>
 * @date 2020/7/17 12:01
 */
public class NewsVO {

    /** 版本信息，如果没有更新，该字段未NULL */
    private VersionVO version;
    /** 启动页版本号 */
    private String startAdsVersion;
    /** 启动页列表 */
    private List<StartAdItemVO> startAds;
    /** IOS用户是否处于审核模式 */
    private BoolType iosUserInReviewVersion;

    public VersionVO getVersion() {
        return version;
    }

    public void setVersion(VersionVO version) {
        this.version = version;
    }

    public String getStartAdsVersion() {
        return startAdsVersion;
    }

    public void setStartAdsVersion(String startAdsVersion) {
        this.startAdsVersion = startAdsVersion;
    }

    public List<StartAdItemVO> getStartAds() {
        return startAds;
    }

    public void setStartAds(List<StartAdItemVO> startAds) {
        this.startAds = startAds;
    }

    public BoolType getIosUserInReviewVersion() {
        return iosUserInReviewVersion;
    }

    public void setIosUserInReviewVersion(BoolType iosUserInReviewVersion) {
        this.iosUserInReviewVersion = iosUserInReviewVersion;
    }
}
