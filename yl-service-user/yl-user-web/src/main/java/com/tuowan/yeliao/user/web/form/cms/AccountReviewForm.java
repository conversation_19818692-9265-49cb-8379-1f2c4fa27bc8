package com.tuowan.yeliao.user.web.form.cms;

import com.easyooo.framework.validate.config.LMNotEmpty;
import com.easyooo.framework.validate.config.LMNotNull;
import com.tuowan.yeliao.commons.core.enums.general.LoginType;
import com.tuowan.yeliao.commons.core.enums.general.PackageType;
import com.tuowan.yeliao.commons.core.enums.general.ReviewStatus;
import com.tuowan.yeliao.commons.web.common.form.Form;

public class AccountReviewForm implements Form {
    @LMNotNull
    private LoginType loginType;
    @LMNotEmpty
    private String uid;
    @LMNotNull
    private PackageType packageType;
    @LMNotNull
    private ReviewStatus status;
    private String msg;

    public LoginType getLoginType() {
        return loginType;
    }

    public void setLoginType(LoginType loginType) {
        this.loginType = loginType;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public PackageType getPackageType() {
        return packageType;
    }

    public void setPackageType(PackageType packageType) {
        this.packageType = packageType;
    }

    public ReviewStatus getStatus() {
        return status;
    }

    public void setStatus(ReviewStatus status) {
        this.status = status;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }
}
