package com.tuowan.yeliao.user.web.vo.task;

import com.tuowan.yeliao.commons.comp.grant.dto.AwardDTO;
import com.tuowan.yeliao.commons.data.entity.config.TTaskConfig;
import com.tuowan.yeliao.commons.data.enums.general.ClientTouchType;
import com.tuowan.yeliao.commons.data.enums.user.AwardStatus;

import java.util.List;

public class TaskItemVO {
    /**
     * 任务名称
     */
    private String taskName;
    /**
     * 任务描述
     */
    private String taskDesc;
    /**
     * 任务图片
     */
    private String taskPic;
    /**
     * 任务完成状态
     */
    private AwardStatus status;
    /**
     * 任务奖励
     */
    private List<AwardDTO> awardList;
    /**
     * 跳转类型
     */
    private ClientTouchType touchType;
    /**
     * 跳转值
     */
    private String touchValue;

    public static TaskItemVO build1(TTaskConfig taskConfig, AwardStatus status, List<AwardDTO> awardList, String taskNameExtInfo) {
        TaskItemVO vo = new TaskItemVO();
        vo.setTaskName(taskConfig.getTaskName() + taskNameExtInfo);
        vo.setTaskDesc(taskConfig.getTaskDesc());
        vo.setStatus(status);
        vo.setTaskPic(taskConfig.getTaskPic());
        vo.setTouchType(taskConfig.getTouchType());
        vo.setTouchValue(taskConfig.getTouchValue());
        vo.setAwardList(awardList);
        return vo;
    }

    public String getTaskName() {
        return taskName;
    }

    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    public String getTaskDesc() {
        return taskDesc;
    }

    public void setTaskDesc(String taskDesc) {
        this.taskDesc = taskDesc;
    }

    public String getTaskPic() {
        return taskPic;
    }

    public void setTaskPic(String taskPic) {
        this.taskPic = taskPic;
    }

    public AwardStatus getStatus() {
        return status;
    }

    public void setStatus(AwardStatus status) {
        this.status = status;
    }

    public List<AwardDTO> getAwardList() {
        return awardList;
    }

    public void setAwardList(List<AwardDTO> awardList) {
        this.awardList = awardList;
    }

    public ClientTouchType getTouchType() {
        return touchType;
    }

    public void setTouchType(ClientTouchType touchType) {
        this.touchType = touchType;
    }

    public String getTouchValue() {
        return touchValue;
    }

    public void setTouchValue(String touchValue) {
        this.touchValue = touchValue;
    }
}
