package com.tuowan.yeliao.user.service.signIn;

import com.alibaba.nacos.common.utils.Pair;
import com.easyooo.framework.common.enums.DatePattern;
import com.easyooo.framework.common.util.DateUtils;
import com.easyooo.framework.common.util.MsgUtils;
import com.tuowan.yeliao.commons.comp.signin.SignInConfig;
import com.tuowan.yeliao.commons.comp.signin.UserSignInComponent;
import com.tuowan.yeliao.commons.comp.signin.dto.NowRoundSignInfoDTO;
import com.tuowan.yeliao.commons.comp.signin.dto.SignInDayInfoDTO;
import com.tuowan.yeliao.commons.config.UnifiedConfig;
import com.tuowan.yeliao.commons.config.lock.UserLockTemplate;
import com.tuowan.yeliao.commons.context.GlobalUtils;
import com.tuowan.yeliao.commons.core.additions.UrlParamsMap;
import com.tuowan.yeliao.commons.core.constant.IconConstant;
import com.tuowan.yeliao.commons.core.enums.busicode.BusiCodeDefine;
import com.tuowan.yeliao.commons.core.enums.general.BoolType;
import com.tuowan.yeliao.commons.core.exception.BusiException;
import com.tuowan.yeliao.commons.data.entity.config.TAward;
import com.tuowan.yeliao.commons.data.entity.config.TAwardDetail;
import com.tuowan.yeliao.commons.data.entity.user.UMessageSetting;
import com.tuowan.yeliao.commons.data.enums.config.AwardType;
import com.tuowan.yeliao.commons.data.enums.general.ClientTouchType;
import com.tuowan.yeliao.commons.data.enums.user.SexType;
import com.tuowan.yeliao.commons.data.enums.user.SignInQueryType;
import com.tuowan.yeliao.commons.data.manager.user.MessageSettingManager;
import com.tuowan.yeliao.commons.data.manager.user.UserInfoManager;
import com.tuowan.yeliao.commons.data.support.qr.QrCodeGenWrapper;
import com.tuowan.yeliao.commons.data.utils.BusiUtils;
import com.tuowan.yeliao.commons.data.utils.HtmlUrlUtils;
import com.tuowan.yeliao.commons.web.proxy.config.BusiCode;
import com.tuowan.yeliao.user.data.enums.SignInAwardActPopType;
import com.tuowan.yeliao.user.web.form.signIn.AwardActPopForm;
import com.tuowan.yeliao.user.web.form.signIn.SignInQueryForm;
import com.tuowan.yeliao.user.web.vo.signIn.AwardActPopVO;
import com.tuowan.yeliao.user.web.vo.signIn.SignInAwardVO;
import com.tuowan.yeliao.user.web.vo.signIn.SignInInfoVO;
import com.tuowan.yeliao.user.web.vo.signIn.SignItemVO;
import com.tuowan.yeliao.user.web.vo.signIn.SignVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 用户签到相关业务
 *
 * <AUTHOR>
 * @date 2021/4/28 13:41
 */
@Service
public class SignInService {

    private static final Logger log = LoggerFactory.getLogger(SignInService.class);

    @Autowired
    private UserLockTemplate userLockTemplate;
    @Autowired
    private UserInfoManager userInfoManager;
    @Autowired
    private UserSignInComponent userSignInComponent;
    @Autowired
    private MessageSettingManager messageSettingManager;

    /**
     * 查询签到信息
     */
    @BusiCode
    public SignInInfoVO saveQuerySignInInfo(SignInQueryForm form) {
        Long userId = GlobalUtils.uid();
        SexType sexType = GlobalUtils.sexType();
        SignInInfoVO vo = new SignInInfoVO();
        //女的去掉签到
        if (SexType.Female == sexType) {
            vo.setShowPopup(BoolType.False);
            return vo;
        }
        Date nowDate = DateUtils.getStartOfDay(new Date());
        // 给一个测试缓存
        if(!UnifiedConfig.isProdEnv()){
            Date testTime = userSignInComponent.getSignInTextTime(userId);
            if(Objects.nonNull(testTime)){
                nowDate = DateUtils.getStartOfDay(testTime);
            }
        }
        NowRoundSignInfoDTO roundInfo = userSignInComponent.getNowRoundSignInfo(userId, nowDate);
        if (form.getQueryType() == SignInQueryType.OpenApp) {
            vo.setShowPopup(BoolType.False);
            return vo;
        }
        // 判断用户资料是否完善
        if(BoolType.True == userInfoManager.checkHaveEditDataAward(userId)){
            vo.setTaskInfo("完善资料领更多奖励");
            vo.setTips("下次再说");
            vo.setTouchType(ClientTouchType.EditMineHomePage);
        }
        // 获取奖励配置的图片和数量
        Integer nowDayIndex = userSignInComponent.getNowDayIndex(nowDate, roundInfo);
        List<SignInDayInfoDTO> awardInfoList = userSignInComponent.getAwardInfo(roundInfo, nowDayIndex);
        vo.setSignDetailList(awardInfoList);
        vo.setNowDayIndex(nowDayIndex);
        vo.setNowDaySigned(BoolType.False);
        int count = 0;
        for (SignInDayInfoDTO dto : awardInfoList) {
            if (dto.getDayIndex().equals(nowDayIndex)) {
                vo.setNowDaySigned(dto.getHasSigned());
            }
            if(BoolType.True == dto.getHasSigned()){
                count ++;
            }
        }
        if (form.getQueryType() == SignInQueryType.OpenApp && vo.getNowDaySigned() == BoolType.True) {
            // 今日已签到，不再弹窗
            vo.setShowPopup(BoolType.False);
            return vo;
        }
        vo.setSignRemind(BoolType.False);
        vo.setShowPopup(BoolType.True);
        vo.setDaysNum(count);
        vo.setInSignInAwardAct(BoolType.valueOf(userSignInComponent.inSignInAwardActTime(sexType)));
        return vo;
    }

    /**
     * 保存签到
     * 备注：未充值的用户仅能签到5次
     */
    @BusiCode(value = BusiCodeDefine.SIgnIn)
    public SignVO saveSignIn() {
        Long userId = GlobalUtils.uid();
        userLockTemplate.acquireTransactionLock(userId);
        Date nowTime = new Date();
        // 给一个测试缓存
        if(!UnifiedConfig.isProdEnv()){
            Date testTime = userSignInComponent.getSignInTextTime(userId);
            if(Objects.nonNull(testTime)){
                nowTime = testTime;
            }
        }
        Date nowTimeStart = DateUtils.getStartOfDay(nowTime);
        NowRoundSignInfoDTO roundInfo = userSignInComponent.getNowRoundSignInfo(userId, nowTimeStart);
        if(BoolType.True == roundInfo.getTodayHasSignIn()){
            throw new BusiException("您今日已签到，请明日再来哦~");
        }
        Integer nowDayIndex = userSignInComponent.getNowDayIndex(nowTimeStart, roundInfo);
        // 保存签到记录
        SignInConfig.SignInAwardRule rule = userSignInComponent.saveSignIn(userId, nowDayIndex, nowTimeStart, nowTime, roundInfo);
        // 返回值构建
        return SignVO.build(rule.getDpBigPic(), Collections.singletonList(SignItemVO.build(rule.getAwardPic(), MsgUtils.format("x{}", rule.getAwardValue()))));
    }

    /**
     * 设置签到提醒
     */
    @BusiCode
    public void saveSignRemind() {
        Long userId = GlobalUtils.uid();
        userLockTemplate.acquireTransactionLock(userId);
        UMessageSetting settings = messageSettingManager.getSetting(GlobalUtils.uid());
        if (Objects.isNull(settings.getSignRemind()) || settings.getSignRemind() == BoolType.False) {
            settings.setSignRemind(BoolType.True);
            userSignInComponent.putSignInRemindQueue(userId);
        } else {
            settings.setSignRemind(BoolType.False);
            userSignInComponent.removeSignInRemindQueue(userId);
        }
        messageSettingManager.updateSetting(settings);
    }

    /**
     * 签到抽奖活动弹窗信息
     */
    @BusiCode
    public AwardActPopVO querySignInAwardActPop(AwardActPopForm form){
        Long userId = GlobalUtils.uid();
        Date now = DateUtils.nowTime();
        // 给一个测试缓存
        if(!UnifiedConfig.isProdEnv()){
            Date testTime = userSignInComponent.getSignInTextTime(userId);
            if(Objects.nonNull(testTime)){
                now = testTime;
            }
        }
        String awardPic;
        String awardDesc;
        Pair<Integer, Integer> awardTimes = userSignInComponent.getSignInAwardActAwardTimes(userId, now);
        if(SignInAwardActPopType.SignIned == form.getPopType()){
            // 签到弹窗 弹出内容为今日签到奖励内容
            Date nowTimeStart = DateUtils.getStartOfDay(now);
            NowRoundSignInfoDTO roundInfo = userSignInComponent.getNowRoundSignInfo(userId, nowTimeStart);
            Integer nowDayIndex = userSignInComponent.getNowDayIndex(nowTimeStart, roundInfo);
            SignInConfig.SignInAwardRule rule = SignInConfig.getRuleByTypeAndDayIndex(nowDayIndex, roundInfo.getSignType());
            TAwardDetail awardDetail = userSignInComponent.getAwardDetail(rule.getAwardCode());
            UrlParamsMap build = UrlParamsMap.build(awardDetail.getExtJsonCfg());
            awardPic = build.getString("pic");
            awardDesc = build.getString("awardName");
        }else{
            awardPic = IconConstant.CASH_ICON_W108_H108;
            awardDesc = MsgUtils.format("{}/{}", awardTimes.getSecond() - awardTimes.getFirst(), awardTimes.getSecond());
        }
        // 封装返回值
        AwardActPopVO vo = new AwardActPopVO();
        vo.setAwardPic(awardPic);
        vo.setAwardDesc(awardDesc);
        Pair<Date, Date> times = userSignInComponent.getSignInAwardActTimes();
        vo.setActTime(MsgUtils.format("{}-{}", DateUtils.toString(times.getFirst(), DatePattern.YMD5),
                DateUtils.toString(times.getSecond(), DatePattern.YMD5)));
        vo.setIsShare(BoolType.valueOf(userSignInComponent.inSignInAwardActTime(GlobalUtils.sexType()) && awardTimes.getFirst() > 0));
        return vo;
    }

    /**
     * 签到抽奖
     */
    @BusiCode(BusiCodeDefine.SignInAwardAct)
    public SignInAwardVO saveSignInAward(){
        Long userId = GlobalUtils.uid();
        userLockTemplate.acquireTransactionLock(userId);
        if(SexType.Female != GlobalUtils.sexType()){
            throw new BusiException("不支持抽奖！");
        }
        // 判断签到抽奖活动是否开启
        Date now = DateUtils.nowTime();
        Pair<Date, Date> times = userSignInComponent.getSignInAwardActTimes();
        if(Objects.isNull(times) || now.before(times.getFirst()) || now.after(times.getSecond())){
            throw new BusiException("活动还未开始或已结束！");
        }
        // 判断用户当日还是否有抽奖机会
        if(!userSignInComponent.checkUserSignInAwardTimes(userId, now)){
            throw new BusiException("今日抽奖机会已用完，明日再来吧！");
        }
        // 判断用户当日是否签到
        if(!userSignInComponent.checkUserSignInToday(userId, DateUtils.getStartOfDay(new Date()))){
            throw new BusiException("今日还未签到，请先完成签到！");
        }
        // 抽奖 & 发奖
        Pair<TAward, TAwardDetail> awardPair = userSignInComponent.signInAward(userId);
        BoolType needShare = BoolType.False;
        AwardType awardType = awardPair.getSecond().getAwardType();
        if(AwardType.Cash == awardType && awardPair.getSecond().getCount() >= 10000){
            needShare = BoolType.True;
        }
        Pair<String, String> awardDescTip = getAwardDescTip(awardType);
        Pair<String, String> awardPicAndValue = getAwardPicAndValue(awardType, awardPair.getSecond().getCount());
        // 累计用户成功抽奖次数
        userSignInComponent.incrUserSignInAwardTimes(userId, now);
        return SignInAwardVO.build1(awardPicAndValue.getFirst(), awardPicAndValue.getSecond(), awardDescTip.getFirst(), awardDescTip.getSecond(), needShare, getSharePicBase64(userId));
    }

    /** ---------------------------- 辅助方法 ------------------------- */

    /**
     * 获取分享图片base64
     */
    private String getSharePicBase64(Long userId){
        try {
            return QrCodeGenWrapper.of(HtmlUrlUtils.getInviteDownloadUrl(null)).setWidth(120).setHeight(120).asBase64();
        }catch (Exception e){
            // do nothing
        }
        return null;
    }

    /**
     * 获取awardPic
     */
    private Pair<String, String> getAwardPicAndValue(AwardType awardType, Integer count){
        if(AwardType.Cash == awardType){
            return Pair.with(IconConstant.CASH_ICON_W108_H108, BusiUtils.cashToYuanSimplifyStr(count.longValue(), 2));
        }
        if(AwardType.PlatFormBeans == awardType || AwardType.Beans == awardType){
            return Pair.with(IconConstant.BEAN_ICON_W108_H108, count.toString());
        }
        return Pair.with(null, null);
    }

    /**
     * 获取award Desc Tip
     */
    private Pair<String, String> getAwardDescTip(AwardType awardType){
        if(AwardType.Cash == awardType){
            return Pair.with("现金红包", "已存入钱包");
        }
        if(AwardType.PlatFormBeans == awardType || AwardType.Beans == awardType){
            return Pair.with("金币", "已存入余额");
        }
        return Pair.with(null, null);
    }
}
