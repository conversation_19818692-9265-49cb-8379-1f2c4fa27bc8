package com.tuowan.yeliao.user.data.enums;

import com.easyooo.framework.common.util.EnumUtils;

/**
 * 注销处理状态
 *
 * <AUTHOR>
 * @date 2021/5/25 13:31
 */
public enum ProcessDestroyType implements EnumUtils.IDEnum {

    Pass("P", "成功注销"),

    CmsPass("CP", "CMS成功注销"),

    CancelLogin("LC", "重新登陆取消注销"),

    ActCancel("AC", "主动取消注销"),

    CancelCms("CC", "CMS取消注销");

    private String id;
    private String desc;


    ProcessDestroyType(String id, String desc) {
        this.id = id;
        this.desc = desc;

    }

    @Override
    public String getId() {
        return this.id;
    }

    @Override
    public String getDesc() {
        return this.desc;
    }

}
