package com.tuowan.yeliao.user.data.enums;

import com.tuowan.yeliao.commons.core.constant.IconConstant;
import com.tuowan.yeliao.commons.data.enums.general.ClientTouchType;

/**
 * 充值折扣类型
 */
public enum ChargeDiscountType {

    First(IconConstant.CHARGE_DISCOUNT_FIRST, ClientTouchType.Recharge, "首冲"),
    preferential(IconConstant.CHARGE_DISCOUNT_PREFERENTIAL, ClientTouchType.Recharge, "充值豪礼"),
    ;

    /**
     * 根据条件参数回去充值优惠
     *
     * @param finishFirstCharge 是否首冲
     * @return
     */
    public static ChargeDiscountType getByRule(boolean finishFirstCharge) {
        if (finishFirstCharge) {
            return preferential;
        } else {
            return First;
        }
    }

    ChargeDiscountType(String pic, ClientTouchType touchType, String desc) {
        this.pic = pic;
        this.touchType = touchType;
        this.desc = desc;
    }

    private String pic;
    private ClientTouchType touchType;
    private String desc;

    public String getPic() {
        return pic;
    }

    public ClientTouchType getTouchType() {
        return touchType;
    }

    public String getDesc() {
        return desc;
    }
}
