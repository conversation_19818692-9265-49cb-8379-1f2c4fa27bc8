/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.user.data.entity;

import com.easyooo.framework.cache.annotations.KeyProperty;
import org.apache.ibatis.type.Alias;

import java.util.Date;

@Alias("UUserLogin")
public class UUserLogin {
    /** 用户ID */
    @KeyProperty
    private Long userId;

    /** 登录名 */
    private String loginName;

    /** 登录密码 */
    private String password;

    /** 登录密码干扰码：不能轻易改变，修改必须保证密码同时修改。 */
    private String pwdMixupCode;

    /** 认证手机号 */
    private String authMobile;

    /** 认证手机号时间 */
    private Date authMobileTime;

    /** 登录动态口令 */
    private String totpSecret;

    /** 创建时间 */
    private Date createTime;

    public UUserLogin() {
        
    }

    /** 根据主键初始化实例 **/
    public UUserLogin(Long userId) {
        this.userId = userId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getLoginName() {
        return loginName;
    }

    public void setLoginName(String loginName) {
        this.loginName = loginName == null ? null : loginName.trim();
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password == null ? null : password.trim();
    }

    public String getPwdMixupCode() {
        return pwdMixupCode;
    }

    public void setPwdMixupCode(String pwdMixupCode) {
        this.pwdMixupCode = pwdMixupCode == null ? null : pwdMixupCode.trim();
    }

    public String getAuthMobile() {
        return authMobile;
    }

    public void setAuthMobile(String authMobile) {
        this.authMobile = authMobile == null ? null : authMobile.trim();
    }

    public Date getAuthMobileTime() {
        return authMobileTime;
    }

    public void setAuthMobileTime(Date authMobileTime) {
        this.authMobileTime = authMobileTime;
    }

    public String getTotpSecret() {
        return totpSecret;
    }

    public void setTotpSecret(String totpSecret) {
        this.totpSecret = totpSecret;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}