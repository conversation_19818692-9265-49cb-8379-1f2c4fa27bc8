package com.tuowan.yeliao.user.job;

import com.tuowan.yeliao.commons.context.proxy.ProxyExecutors;
import com.tuowan.yeliao.commons.core.enums.busicode.BackCodeDefine;
import com.tuowan.yeliao.commons.job.DefaultJob;
import com.tuowan.yeliao.user.service.UserDestroyService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 用户账号注销
 */
@Component
public class UserAcctDestroyJob extends DefaultJob {

    @Autowired
    private UserDestroyService userDestroyService;

    /**
     * 用户注销处理
     * <p>
     * Cron: 0 5 0 * * ?
     *
     * @param param
     * @return
     */
    @XxlJob(JobKeyDefine.UserDestroyHandler)
    public ReturnT<?> userDestroyHandler(String param) {
        // 获取待注销用户
        List<Long> userIds = userDestroyService.getDestroyUser();
        for (Long userId : userIds) {
            try {
                ProxyExecutors.doProxy(BackCodeDefine.DestroyAccount, context -> {
                    // 注销处理
                    userDestroyService.saveProcessDestroyUser(userId);
                    return null;
                });
            } catch (Exception e) {
                LOG.error("用户:{} 注销处理失败!，原因:", userId, e);
                return ReturnT.FAIL;
            }
            LOG.info("用户:{} 注销成功", userId);
        }
        return ReturnT.SUCCESS;
    }
}
