package com.tuowan.yeliao.user.job;

import com.easyooo.framework.common.util.ListUtils;
import com.tuowan.yeliao.commons.data.dto.common.RedisShardDTO;
import com.tuowan.yeliao.commons.job.DefaultJob;
import com.tuowan.yeliao.user.service.UserHeartService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Set;

/**
 * 用户心跳相关任务
 *
 * <AUTHOR>
 * @date 2020/7/18 22:12
 */
@Component
public class UserHeartJob extends DefaultJob {

    private static final Logger LOG = LoggerFactory.getLogger(UserHeartJob.class);

    @Autowired
    private UserHeartService userHeartService;

    /**
     * 用户心跳超时处理
     * <p>
     * Cron: 0/5 * * * * ?
     */
    @XxlJob(JobKeyDefine.UserHeartExpireHandler)
    public ReturnT<String> userHeartExpireHandler(String param) throws Throwable {
        Set<RedisShardDTO> values = userHeartService.findHeartExpiredUsers(getJobShardIndex(), getJobShardTotal());
        if (ListUtils.isEmpty(values)) {
            return ReturnT.SUCCESS;
        }
        Long successCnt = 0L;
        Long failCnt = 0L;
        for (RedisShardDTO shardDTO : values) {
            for (String member : shardDTO.getMembers()) {
                if (userHeartService.saveUserOffline(member)) {
                    successCnt++;
                } else {
                    failCnt++;
                }
            }
        }
        LOG.info("本次处理用户心跳超时，成功：{}， 失败：{}", successCnt, failCnt);
        return ReturnT.SUCCESS;
    }
}
