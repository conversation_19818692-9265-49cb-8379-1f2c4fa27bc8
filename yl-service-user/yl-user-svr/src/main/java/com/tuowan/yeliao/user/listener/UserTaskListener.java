package com.tuowan.yeliao.user.listener;

import com.tuowan.yeliao.commons.context.GlobalContext;
import com.tuowan.yeliao.commons.context.GlobalUtils;
import com.tuowan.yeliao.commons.core.enums.busicode.BusiCodeDefine;
import com.tuowan.yeliao.commons.core.enums.business.BusinessDataKey;
import com.tuowan.yeliao.commons.mq.annotation.Consumer;
import com.tuowan.yeliao.commons.mq.enums.MessageTag;
import com.tuowan.yeliao.commons.mq.listener.ContextDispatchMessageListener;
import com.tuowan.yeliao.user.service.UserTaskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 用户任务处理
 *
 * <AUTHOR>
 * @date 2022/4/13 13:07
 */
@Component
@Consumer(MessageTag.UserTask)
public class UserTaskListener extends ContextDispatchMessageListener {

    @Autowired
    private UserTaskService userTaskService;

    @Override
    public void doProcessMessage(GlobalContext globalContext) {
        userTaskService.saveDealUserTask();
    }

}
