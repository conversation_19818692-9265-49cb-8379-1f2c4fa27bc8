# P6Spy 3.9.1 集成总结

## 概述

已成功为xchat-server项目集成P6Spy 3.9.1，实现在dev和test环境下方便地输出SQL语句及其执行时长，同时确保生产环境不受影响。

## 修改文件清单

### 1. Maven配置修改
- **文件**: `pom.xml`
- **修改内容**: 
  - 在dev和test profile中添加p6spy 3.9.1依赖
  - 生产环境profile不包含p6spy依赖

### 2. 数据源配置修改
- **文件**: `yl-commons/yl-commons-config/src/main/java/com/tuowan/yeliao/commons/config/mybatis/JdbcDataSource.java`
- **修改内容**:
  - 添加p6spy驱动检测和包装逻辑
  - 根据环境自动选择配置文件
  - 在非生产环境下自动启用p6spy

### 3. 配置文件新增
- **spy.properties**: 统一的p6spy配置文件，适用于所有非生产环境

### 4. 工具类新增
- **P6spyConfigValidator.java**: P6Spy配置验证工具类
- 提供配置状态检查和验证功能

### 5. 文档和示例
- **P6SPY-README.md**: 详细使用说明
- **logback-p6spy-example.xml**: 日志配置示例
- **verify-p6spy.sh/bat**: 配置验证脚本

### 6. 自动配置修改
- **MybatisAutoConfiguration.java**: 添加P6Spy配置验证日志输出

## 功能特性

### ✅ 环境隔离
- **开发和测试环境**: 启用p6spy，使用统一配置记录SQL语句
- **生产环境**: 完全禁用p6spy，无性能影响

### ✅ 自动检测
- 自动检测p6spy依赖是否存在
- 依赖缺失时自动降级到原始配置
- 无需手动配置开关

### ✅ 智能配置
- 使用统一的配置文件，简化维护
- 与现有日志系统无缝集成
- 配置简洁，避免环境间差异

### ✅ 性能监控
- 显示SQL执行时间
- 支持慢SQL阈值配置
- 便于性能问题排查

## 使用方法

### 1. 编译不同环境
```bash
# 开发环境 (包含p6spy)
mvn clean compile -Pdev

# 测试环境 (包含p6spy)
mvn clean compile -Ptest

# 生产环境 (不包含p6spy)
mvn clean compile -Pprod
```

### 2. 查看SQL日志
启动应用后，SQL语句会自动输出到日志：

```
[SQL] 2025-07-04 10:30:15 | 执行时间: 25ms | 连接: 1 | SQL: SELECT * FROM t_user WHERE user_id = 12345
```

### 3. 验证配置
运行验证脚本：
```bash
# Linux/Mac
./verify-p6spy.sh

# Windows
verify-p6spy.bat
```

## 配置验证

应用启动时会自动输出P6Spy配置验证信息：

```
=== P6Spy配置验证结果 ===
当前环境: DEV
是否生产环境: false
✅ P6Spy依赖已正确引入
✅ 找到P6Spy配置文件: spy.properties
=== 验证完成 ===
```

## 日志配置建议

在logback配置中添加SQL日志配置：

```xml
<springProfile name="dev">
    <logger name="p6spy" level="INFO" additivity="false">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="SQL_FILE"/>
    </logger>
</springProfile>

<springProfile name="test">
    <logger name="p6spy" level="INFO" additivity="false">
        <appender-ref ref="SQL_FILE"/>
    </logger>
</springProfile>

<springProfile name="prod">
    <logger name="p6spy" level="OFF"/>
</springProfile>
```

## 自定义配置

### 修改执行时间阈值
编辑spy.properties配置文件：
```properties
# 只记录执行时间超过50ms的SQL
executionThreshold=50
```

### 过滤特定SQL
```properties
filter=true
exclude=SELECT 1,SHOW TABLES
```

### 修改日志格式
```properties
customLogMessageFormat=自定义格式: %(currentTime) | %(executionTime)ms | %(sqlSingleLine)
```

## 注意事项

1. **性能影响**: p6spy会对性能产生轻微影响，已限制在非生产环境
2. **日志量**: 开发环境会产生大量SQL日志，注意磁盘空间
3. **敏感信息**: SQL日志可能包含敏感数据，注意日志安全
4. **依赖管理**: 生产环境不会引入p6spy依赖，保持轻量级

## 故障排除

### 问题1: p6spy未生效
- 检查Maven profile是否正确
- 确认依赖已正确引入
- 查看启动日志中的验证信息

### 问题2: 配置文件未找到
- 确认配置文件在classpath中
- 检查文件名是否正确
- 查看验证工具输出

### 问题3: 日志未输出
- 检查日志级别配置
- 确认使用了SLF4J appender
- 查看是否被过滤规则排除

## 版本信息

- **P6Spy版本**: 3.9.1
- **支持数据库**: MySQL, PostgreSQL, Oracle等
- **Java版本**: JDK 8+
- **Spring Boot版本**: 2.4.2

## 总结

P6Spy集成已完成，具备以下优势：

1. **零侵入**: 不影响现有业务代码
2. **环境隔离**: 生产环境完全不受影响
3. **自动化**: 无需手动配置开关
4. **可配置**: 支持环境特定的配置
5. **易维护**: 提供完整的验证和文档

开发和测试人员现在可以方便地监控SQL执行情况，提高开发效率和问题排查能力。
