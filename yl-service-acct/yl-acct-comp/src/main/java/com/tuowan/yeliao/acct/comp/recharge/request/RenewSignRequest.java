package com.tuowan.yeliao.acct.comp.recharge.request;

import com.tuowan.yeliao.commons.core.enums.general.PayType;

import java.util.Map;

/**
 * 续订签约请求信息
 *
 * <AUTHOR>
 * @date 2022/7/6 16:19
 */
public class RenewSignRequest {

    /**
     * 支付类型
     */
    private PayType payType;
    /**
     * 商户签约号
     */
    private String signNo;
    /**
     * 扩展
     */
    private Map<String, String> extParams;

    public static RenewSignRequest build(PayType payType, String signNo, Map<String, String> extParams) {
        RenewSignRequest request = new RenewSignRequest();
        request.setPayType(payType);
        request.setSignNo(signNo);
        request.setExtParams(extParams);
        return request;
    }

    public PayType getPayType() {
        return payType;
    }

    public void setPayType(PayType payType) {
        this.payType = payType;
    }

    public String getSignNo() {
        return signNo;
    }

    public void setSignNo(String signNo) {
        this.signNo = signNo;
    }

    public Map<String, String> getExtParams() {
        return extParams;
    }

    public void setExtParams(Map<String, String> extParams) {
        this.extParams = extParams;
    }
}
