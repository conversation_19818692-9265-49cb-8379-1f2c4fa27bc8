package com.tuowan.yeliao.acct.comp.recharge.dto;

import com.tuowan.yeliao.acct.data.entity.URecharge;
import com.tuowan.yeliao.acct.data.entity.UVipRenewSign;

/**
 * 周期扣款信息
 *
 * <AUTHOR>
 * @date 2022/7/6 20:26
 */
public class CyclePayOrderDTO {

    /**
     * 订单信息
     */
    private URecharge rechargeInfo;

    /**
     * 签约信息
     */
    private UVipRenewSign signInfo;

    public static CyclePayOrderDTO build(URecharge rechargeInfo, UVipRenewSign signInfo) {
        CyclePayOrderDTO dto = new CyclePayOrderDTO();
        dto.setRechargeInfo(rechargeInfo);
        dto.setSignInfo(signInfo);
        return dto;
    }

    public URecharge getRechargeInfo() {
        return rechargeInfo;
    }

    public void setRechargeInfo(URecharge rechargeInfo) {
        this.rechargeInfo = rechargeInfo;
    }

    public UVipRenewSign getSignInfo() {
        return signInfo;
    }

    public void setSignInfo(UVipRenewSign signInfo) {
        this.signInfo = signInfo;
    }
}
