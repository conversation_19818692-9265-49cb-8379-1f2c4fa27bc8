package com.tuowan.yeliao.acct.comp.recharge.utils;

import com.easyooo.framework.common.util.MsgUtils;
import com.easyooo.framework.common.util.StringUtils;
import com.tuowan.yeliao.commons.core.enums.general.PayType;

/**
 * 充值工具类
 *
 * <AUTHOR>
 * @date 2022/7/8 09:42
 */
public class RechargeUtils {

    /**
     * 构建充值回传参数
     *
     * @param bizId
     * @param payType
     * @return
     */
    public static String buildPassbackParams(Long bizId, PayType payType) {
        return StringUtils.encode(MsgUtils.format("bizId={}&payType={}", bizId, payType.getId()));
    }
}
