package com.tuowan.yeliao.acct.comp.recharge.impl;

import com.alibaba.fastjson.JSONObject;
import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.domain.*;
import com.alipay.api.internal.util.AlipaySignature;
import com.alipay.api.request.*;
import com.alipay.api.response.*;
import com.easyooo.framework.common.enums.DatePattern;
import com.easyooo.framework.common.extend.SimpleMap;
import com.easyooo.framework.common.util.*;
import com.tuowan.yeliao.acct.comp.recharge.IPay;
import com.tuowan.yeliao.acct.comp.recharge.VipComponent;
import com.tuowan.yeliao.acct.comp.recharge.dto.RechargeOrderDTO;
import com.tuowan.yeliao.acct.comp.recharge.enums.CheckRefundStatus;
import com.tuowan.yeliao.acct.comp.recharge.request.RechargeRequest;
import com.tuowan.yeliao.acct.comp.recharge.request.RenewSignRequest;
import com.tuowan.yeliao.acct.comp.recharge.response.*;
import com.tuowan.yeliao.acct.comp.recharge.utils.RechargeUtils;
import com.tuowan.yeliao.acct.data.entity.URecharge;
import com.tuowan.yeliao.acct.data.entity.UVipRenewSign;
import com.tuowan.yeliao.acct.data.enums.VipRenewSignStatus;
import com.tuowan.yeliao.acct.data.enums.VipRenewUnSignType;
import com.tuowan.yeliao.commons.config.UnifiedConfig;
import com.tuowan.yeliao.commons.config.configuration.impl.AppConfig;
import com.tuowan.yeliao.commons.core.constant.GlobalConstant;
import com.tuowan.yeliao.commons.core.enums.general.ClientType;
import com.tuowan.yeliao.commons.core.enums.general.PackageType;
import com.tuowan.yeliao.commons.core.enums.general.PayType;
import com.tuowan.yeliao.commons.core.exception.ComponentException;
import com.tuowan.yeliao.commons.data.dto.config.ALPConfigDTO;
import com.tuowan.yeliao.commons.data.utils.MerchantUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.*;

/**
 * 支付宝支付实现
 *
 * <AUTHOR>
 * @date 2022/4/19 21:26
 */
@Component
public class AlipayImpl implements IPay {

    private final Logger LOG = LoggerFactory.getLogger(this.getClass());
    /**
     * 支付宝成功创建订单返回CODE
     */
    private final String CREATE_ORDER_SUC = "10000";
    /**
     * 支付回调地址
     */
    private final String PAY_NOTIFY_URL = MsgUtils.format("{}/acc/wallet/recharge/alipay", AppConfig.API_URL);
    /**
     * 周期扣款签约回调地址
     */
    private final String CYCLE_PAY_SIGN_NOTIFY_URL = MsgUtils.format("{}/acc/wallet/recharge/alipaySign", AppConfig.API_URL);
    /**
     * 周期扣款报文
     */
    private final String CYCLE_PAY_SCHEMA = "alipays://platformapi/startapp?appId=60000157&appClearTop=false&startMultApp=YES&sign_params={}";

    private Map<String, AlipayClient> clientMap = new HashMap<>();

    @Autowired
    private VipComponent vipComponent;

    @Override
    public List<PayType> getPayTypes() {
        return Arrays.asList(PayType.AlipayApp, PayType.AlipayWap);
    }

    @Override
    public String getMchId(PackageType packageType, String lastMchId, Long userId, PayType payType) {
        ALPConfigDTO config = MerchantUtils.getALPConfig(packageType, lastMchId, userId, payType.getMerchantType());
        if (config == null) {
            return null;
        }
        // 支付宝使用appId作为唯一表示标识
        return config.getAppId();
    }

    @Override
    public RechargeOrderDTO buildOrderInfo(String mchId, PayType payType, ClientType clientType, Long bizId, String orderNo, Integer payAmount, SimpleMap formMap) {
        Double totalAmount = payAmount / 100D;
        PackageType packageType = EnumUtils.byId(formMap.getString("packageType"), PackageType.class);
        if (!UnifiedConfig.isProdEnv()) {
            totalAmount = 0.01D;
        }
        if (PayType.AlipayApp == payType) {
            return buildAppOrderInfo(mchId, payType, clientType, bizId, orderNo, totalAmount, packageType);
        }
        if (PayType.AlipayWap == payType) {
            return buildWapOrderInfo(mchId, payType, bizId, orderNo, totalAmount, formMap, packageType);
        }
        throw new ComponentException("当前支付类型不支持");
    }

    @Override
    public String buildRenewSignString(String mchId, String signNo, Integer singleAmount, String executeTime, Integer period, String periodType) {
        AlipayUserAgreementPageSignRequest request = new AlipayUserAgreementPageSignRequest();
        request.setNotifyUrl(CYCLE_PAY_SIGN_NOTIFY_URL);

        AlipayUserAgreementPageSignModel model = new AlipayUserAgreementPageSignModel();
        // 周期扣款销售产品码固定为CYCLE_PAY_AUTH。
        model.setProductCode("CYCLE_PAY_AUTH");
        // 协议签约场景
        model.setSignScene("INDUSTRY|SOCIALIZATION");
        // 周期扣款个人签约产品码固定为CYCLE_PAY_AUTH_P
        model.setPersonalProductCode("CYCLE_PAY_AUTH_P");
        // 商户签约号，代扣协议中标示用户的唯一签约号（确保在商户系统中唯一）
        model.setExternalAgreementNo(signNo);
        AccessParams accessParams = new AccessParams();
        accessParams.setChannel("ALIPAYAPP");
        model.setAccessParams(accessParams);
        // 周期管控规则参数period_rule_params，在签约周期扣款产品时必传
        PeriodRuleParams periodRuleParams = new PeriodRuleParams();
        // 周期类型period_type是周期扣款产品必填，枚举值为DAY和MONTH。
        periodRuleParams.setPeriodType(periodType);
        // 周期数period是周期扣款产品必填
        periodRuleParams.setPeriod(period.longValue());
        // 首次执行时间execute_time是周期扣款产品必填，即商户发起首次扣款的时间
        periodRuleParams.setExecuteTime(executeTime);
        // 单次扣款最大金额single_amount是周期扣款产品必填，即每次发起扣款时限制的最大金额，单位为元
        periodRuleParams.setSingleAmount(String.valueOf(singleAmount / 100));
        model.setPeriodRuleParams(periodRuleParams);
        // 不支持透传参数，设置了该值会提示：开通失败，系统异常，请稍后再试
//        model.setPassParams(MsgUtils.format("uid={}", userId));
        request.setBizModel(model);
        try {
            String body = getClient(mchId).sdkExecute(request).getBody();
            return MsgUtils.format(CYCLE_PAY_SCHEMA, StringUtils.encode(body));
        } catch (Exception e) {
            LOG.error("签约周期扣款失败，原因：", e);
            throw new ComponentException("支付宝周期扣款签约失败");
        }
    }

    @Override
    public RechargeResponse verifyResult(URecharge recharge, RechargeRequest request) {
        Map<String, String> params = request.getNotifyParams();
        ALPConfigDTO config = MerchantUtils.getALPConfig(params.get("app_id"));
        if (!verifyParams(params, config.getAppId(), config.getSellerId())) {
            LOG.warn("支付宝应用参数校验不通过，回调参数：{}", JsonUtils.seriazileAsString(params));
            return RechargeResponse.buildFailure();
        }
        String tradeStatus = params.get("trade_status");
        // 交易创建，等待买家付款
        if ("WAIT_BUYER_PAY".equals(tradeStatus)) {
            return RechargeResponse.buildFailure();
        }
        // 验证签名
        if (!verifySign(params, config.getPublicKey())) {
            LOG.error("支付宝验证签名失败，回调参数：{}", JsonUtils.seriazileAsString(params));
            return RechargeResponse.buildFailure();
        }
        // 生产环境，验证充值金额
        if (UnifiedConfig.isProdEnv()) {
            // 单位元
            Double totalAmount = Double.parseDouble(params.get("total_amount"));
            if (recharge.getPayAmount() / 100D != totalAmount.doubleValue()) {
                LOG.error("支付金额和订单金额不一致");
                return RechargeResponse.buildFailure();
            }
        }
        // 交易支付成功
        if ("TRADE_SUCCESS".equals(tradeStatus)) {
            // 设置回调时间(买家付款时间)
            String payCallbackTime = params.get("gmt_payment");
            if (StringUtils.isNotEmpty(payCallbackTime)) {
                request.setPayCallbackTime(DateUtils.parse(payCallbackTime, DatePattern.YMD_HMS));
            }
            return RechargeResponse.buildSuccess();
        }
        return RechargeResponse.buildFailure();
    }

    @Override
    public RenewSignResponse verifyRenewSignParams(UVipRenewSign signInfo, RenewSignRequest request) {
        Map<String, String> params = request.getExtParams();
        ALPConfigDTO config = MerchantUtils.getALPConfig(params.get("app_id"));
        if (!verifySign(params, config.getPublicKey())) {
            LOG.error("周期扣款签订参数校验不通过，回调参数：{}", JsonUtils.seriazileAsString(params));
            return RenewSignResponse.buildFailure();
        }
        String status = params.get("status");
        if (!Objects.equals(status, "NORMAL")) {
            LOG.error("周期扣款签订状态错误，回调参数：{}", JsonUtils.seriazileAsString(params));
            return RenewSignResponse.buildFailure();
        }
        signInfo.setMchId(params.get("app_id"));
        signInfo.setExternalUserId(params.get("alipay_user_id"));
        signInfo.setExternalSignNo(params.get("agreement_no"));
        signInfo.setSignTime(DateUtils.parse(params.get("sign_time"), DatePattern.YMD_HMS));
        signInfo.setCallbackTime(DateUtils.parse(params.get("notify_time"), DatePattern.YMD_HMS));
        signInfo.setSignStatus(VipRenewSignStatus.Normal);
        return RenewSignResponse.buildSuccess(signInfo);
    }

    @Override
    public RenewDeductResponse submitRenewDeduct(String mchId, PackageType packageType, String orderNo, Integer money, String externalSignNo) {
        Double totalAmount = money / 100D;
        if (!UnifiedConfig.isProdEnv()) {
            totalAmount = 0.01;
        }
        AlipayTradePayRequest request = new AlipayTradePayRequest();
        request.setNotifyUrl(PAY_NOTIFY_URL);

        JSONObject bizContent = new JSONObject();
        bizContent.put("out_trade_no", orderNo);
        bizContent.put("total_amount", totalAmount);
        bizContent.put("subject", MsgUtils.format("{}签约代扣订单", packageType.getDesc()));
        bizContent.put("product_code", "CYCLE_PAY_AUTH");
        // 协议信息
        JSONObject agreementParams = new JSONObject();
        agreementParams.put("agreement_no", externalSignNo);
        bizContent.put("agreement_params", agreementParams);
        request.setBizContent(bizContent.toString());
        try {
            AlipayTradePayResponse response = getClient(mchId).execute(request);
            if (response != null && response.isSuccess()) {
                return RenewDeductResponse.buildSuccess();
            }
            LOG.error("支付宝周期扣款失败，orderNo: {}, code: {}, msg: {}, subCode: {}, subMsg: {}", orderNo, response.getCode(), response.getMsg(), response.getSubCode(), response.getSubMsg());
            return RenewDeductResponse.buildFailure(response.getSubMsg());
        } catch (AlipayApiException e) {
            LOG.error(MsgUtils.format("支付宝周期扣款失败，orderNo: {}, 原因：", orderNo), e);
            return RenewDeductResponse.buildFailure(e.getMessage());
        }
    }

    @Override
    public void unSignRenew(String mchId, String externalSignNo) {
        AlipayUserAgreementUnsignRequest request = new AlipayUserAgreementUnsignRequest();
        JSONObject object = new JSONObject();
        object.put("agreement_no", externalSignNo);
        request.setBizContent(object.toJSONString());
        try {
            getClient(mchId).execute(request);
        } catch (Exception e) {
            LOG.error(MsgUtils.format("支付宝周期扣款解约失败，externalSignNo: {}, 原因：", externalSignNo), e);
        }
    }

    @Override
    public boolean updateRenewPayDeductTime(String mchId, String externalSignNo, Date deductTime) {
        AlipayUserAgreementExecutionplanModifyRequest request = new AlipayUserAgreementExecutionplanModifyRequest();
        JSONObject object = new JSONObject();
        object.put("agreement_no", externalSignNo);
        object.put("deduct_time", DateUtils.toString(deductTime, DatePattern.YMD));
        object.put("memo", "周期扣款失败，调整下次扣款时间");
        request.setBizContent(object.toJSONString());
        try {
            AlipayUserAgreementExecutionplanModifyResponse response = getClient(mchId).execute(request);
            if (response != null && response.isSuccess()) {
                return true;
            }
            LOG.error("支付宝周期时间调整失败，externalSignNo: {}, code: {}, msg: {}, subCode: {}, subMsg: {}", externalSignNo, response.getCode(), response.getMsg(), response.getSubCode(), response.getSubMsg());
        } catch (AlipayApiException e) {
            LOG.error(MsgUtils.format("修改支付宝周期扣款计划失败，externalSignNo: {}, 原因：", externalSignNo), e);
        }
        return false;
    }

    @Override
    public boolean processSubscribeNotify(PackageType packageType, Map<String, String> params) {
        ALPConfigDTO config = MerchantUtils.getALPConfig(params.get("app_id"));
        // 校验参数签名
        if (!verifySign(params, config.getPublicKey())) {
            return false;
        }
        String status = params.get("status");
        // 续订解约
        if (Objects.equals(status, "UNSIGN")) {
            String signNo = params.get("external_agreement_no");
            if (StringUtils.isEmpty(signNo)) {
                LOG.error("支付宝续订解约签约号不存在，请求参数：{}", JsonUtils.seriazileAsString(params));
                return false;
            }
            Date unSignTime = DateUtils.parse(params.get("unsign_time"), DatePattern.YMD_HMS);
            vipComponent.unSignRenew(signNo, VipRenewUnSignType.Manual, unSignTime);
            return true;
        }
        LOG.warn("支付宝订阅通知消息，请求参数：{}", JsonUtils.seriazileAsString(params));
        return false;
    }
    /**
     * 订单退款
     *
     * @param uRecharge 订单信息
     * @param money 退款金额
     */
    @Override
    public RefundDTO refund(URecharge uRecharge, Integer money, Integer refundId) {
        //支付宝退款单位为元
        float refundMoney = money / 100f;
        if (!UnifiedConfig.isProdEnv()) {
            refundMoney = 0.01f;
        }
        AlipayTradeRefundRequest request = new AlipayTradeRefundRequest();
        JSONObject bizContent = new JSONObject();
        bizContent.put("out_trade_no", uRecharge.getOrderNo());
        bizContent.put("refund_amount", refundMoney);
        bizContent.put("out_request_no", refundId.toString());
        request.setBizContent(bizContent.toString());
        try {
            AlipayTradeRefundResponse response = getClient(uRecharge.getMchId()).execute(request);
            LOG.info("AlipayImpl-refund-response,orderNo:{},refundAmount:{},refundId:{},rep:{}", uRecharge.getOrderNo(), refundMoney, refundId, JsonUtils.seriazileAsString(response));
            if (response != null && response.isSuccess()) {
                RefundDTO result = new RefundDTO();
                result.setRefundStatus(true);
                return result;
            }
            LOG.warn("AlipayImpl-refund-fail,outTradeNo:{},refundAmount:{},outRequestNo:{},response:{}",uRecharge.getOrderNo(), refundMoney, refundId,JsonUtils.seriazileAsString(response));
            return RefundDTO.buildFailure(response.getSubMsg());
        } catch (AlipayApiException e) {
            LOG.error("AlipayImpl-refund-exception,outTradeNo:{},refundAmount:{},outRequestNo:{}",uRecharge.getOrderNo(), refundMoney, refundId,e);
            return RefundDTO.buildFailure(e.getMessage());
        }
    }

    private AlipayClient getClient(String mchId) {
        AlipayClient client = clientMap.get(mchId);
        if (client == null) {
            // 配置不存在
            ALPConfigDTO config = MerchantUtils.getALPConfig(mchId);
            if (config == null) {
                LOG.error("支付配置未定义，mchId: {}", mchId);
                throw new ComponentException("支付配置错误");
            }
            client = new DefaultAlipayClient(ALPConfigDTO.GATEWAY, config.getAppId(), config.getPrivateKey(), ALPConfigDTO.FORMAT, ALPConfigDTO.INPUT_CHARSET, config.getPublicKey(), ALPConfigDTO.SIGN_TYPE);
            clientMap.put(mchId, client);
        }
        if (client == null) {
            throw new ComponentException("支付配置错误");
        }
        return client;
    }

    @Override
    public CheckRefundStatusDTO checkRefundStatus(URecharge uRecharge, String refundId) {

        AlipayTradeFastpayRefundQueryRequest request = new AlipayTradeFastpayRefundQueryRequest();
        JSONObject bizContent = new JSONObject();
        bizContent.put("out_trade_no", uRecharge.getOrderNo());
        bizContent.put("out_request_no", refundId);
        request.setBizContent(bizContent.toString());
        CheckRefundStatusDTO checkRefundStatusDTO = new CheckRefundStatusDTO();
        try {
            AlipayTradeFastpayRefundQueryResponse response = getClient(uRecharge.getMchId()).execute(request);
            LOG.info("AlipayImpl-checkRefundStatus-response,orderNo:{},refundId:{},rep:{}", uRecharge.getOrderNo(), refundId, JsonUtils.seriazileAsString(response));
            //查询请求成功
            if(response.isSuccess()){
                //判断申请退款是否成功
                if(StringUtils.isNotEmpty(response.getRefundStatus()) && "REFUND_SUCCESS".equals(response.getRefundStatus()) ) {
                    //退款成功
                    checkRefundStatusDTO.setRefundStatus(CheckRefundStatus.SUCCESS);
                    //阿里退款单位为元，系统记录单位为分
                    float refundMoney = Float.valueOf(response.getRefundAmount()) * 100;
                    checkRefundStatusDTO.setRefundMoney((int)refundMoney );
                    return checkRefundStatusDTO;
                } else {
                    //退款失败
                    checkRefundStatusDTO.setFailReason(response.getSubMsg());
                    checkRefundStatusDTO.setRefundStatus(CheckRefundStatus.FAIL);
                    return checkRefundStatusDTO;
                }
            //查询失败，返回退款状态为退款中，等待下次查询
            } else {
                checkRefundStatusDTO.setRefundStatus(CheckRefundStatus.PROCESSING);
                return checkRefundStatusDTO;
            }
        } catch (Exception e) {
            //查询异常，返回退款状态为退款中，等待下次查询
            LOG.error("AlipayImpl-checkRefundStatus-exception,outTradeNo:{},outRequestNo:{}",uRecharge.getOrderNo(), refundId,e);
            checkRefundStatusDTO.setRefundStatus(CheckRefundStatus.PROCESSING);
            return checkRefundStatusDTO;
        }
    }

    /**
     * 构建App支付订单信息
     *
     * @param mchId
     * @param clientType
     * @param bizId
     * @param orderNo
     * @param totalAmount
     * @return
     */
    private RechargeOrderDTO buildAppOrderInfo(String mchId, PayType payType, ClientType clientType, Long bizId, String orderNo, Double totalAmount, PackageType packageType) {
        AlipayTradeAppPayRequest request = new AlipayTradeAppPayRequest();
        // SDK已经封装掉了公共参数，这里只需要传入业务参数。以下方法为sdk的model入参方式。
        AlipayTradeAppPayModel model = new AlipayTradeAppPayModel();
        model.setProductCode("QUICK_MSECURITY_PAY");
        model.setSubject(packageType.getPaySubject());
        model.setBody(packageType.getPayBody());
        model.setOutTradeNo(orderNo);
        model.setTotalAmount(String.valueOf(totalAmount));
        model.setPassbackParams(RechargeUtils.buildPassbackParams(bizId, payType));
        model.setTimeExpire(DateUtils.toString(DateUtils.plusSeconds(new Date(), PAY_TIMEOUT), DatePattern.YMD_HMS));
        request.setBizModel(model);
        request.setNotifyUrl(PAY_NOTIFY_URL);
        LOG.info("构建支付宝订单信息 PAY_NOTIFY_URL:{}", PAY_NOTIFY_URL);
        try {
            AlipayTradeAppPayResponse alipayTradeAppPayResponse = getClient(mchId).sdkExecute(request);
            // 解析支付宝返回值
            if(alipayTradeAppPayResponse.isSuccess()){
                String content = alipayTradeAppPayResponse.getBody();
                return RechargeOrderDTO.build(bizId, content, buildAppPayContentString(clientType, content));
            }
            LOG.error("构建支付宝订单信息出错 response:{}", JsonUtils.seriazileAsString(alipayTradeAppPayResponse));
        } catch (Exception e) {
            LOG.error("构建支付宝订单信息出错 reason:", e);
        }
        throw new ComponentException("构建支付宝订单信息出错,请稍后重试");
    }

    /**
     * 构建网页支付订单信息
     *
     * @param mchId
     * @param bizId
     * @param orderNo
     * @param totalAmount
     * @param formMap
     * @return
     */
    private RechargeOrderDTO buildWapOrderInfo(String mchId, PayType payType, Long bizId, String orderNo, Double totalAmount, SimpleMap formMap, PackageType packageType) {
        AlipayTradeWapPayRequest request = new AlipayTradeWapPayRequest();
        // SDK已经封装掉了公共参数，这里只需要传入业务参数。以下方法为sdk的model入参方式。
        AlipayTradeWapPayModel model = new AlipayTradeWapPayModel();
        model.setProductCode("QUICK_WAP_WAY");
        model.setSubject(packageType.getPaySubject());
        model.setBody(packageType.getPayBody());
        model.setOutTradeNo(orderNo);
        model.setTotalAmount(String.valueOf(totalAmount));
        model.setPassbackParams(RechargeUtils.buildPassbackParams(bizId, payType));
        model.setTimeExpire(DateUtils.toString(DateUtils.plusSeconds(new Date(), PAY_TIMEOUT), DatePattern.YMD_HMS));
        model.setQuitUrl(formMap.getString("quitUrl"));
        request.setBizModel(model);
        request.setReturnUrl(formMap.getString("returnUrl"));
        request.setNotifyUrl(PAY_NOTIFY_URL);
        try {
            String content = getClient(mchId).sdkExecute(request).getBody();
            return RechargeOrderDTO.build(bizId, ALPConfigDTO.GATEWAY + "?" + content, null);
        } catch (AlipayApiException e) {
            throw new ComponentException("构建支付宝订单信息出错", e);
        }
    }

    /**
     * 生成App支付拉起支付字符串
     *
     * @param clientType
     * @param content
     * @return
     */
    private String buildAppPayContentString(ClientType clientType, String content) {
        if (ClientType.iOS == clientType) {
            Map<String, String> result = new HashMap<>();
            result.put("requestType", "SafePay");
            result.put("fromAppUrlScheme", ALPConfigDTO.APP_URL_SCHEME);
            result.put("dataString", content);
            try {
                String rawContent = URLEncoder.encode(JsonUtils.seriazileAsString(result), "utf-8");
                return "alipay://alipayclient/?" + rawContent;
            } catch (UnsupportedEncodingException e) {
                LOG.error("生成支付宝支付字符串出错，原因：", e);
            }
        }
        return null;
    }

    /**
     * 验证签名
     *
     * @param params
     * @param publicKey
     * @return
     */
    private boolean verifySign(Map<String, String> params, String publicKey) {
        try {
            String signType = params.get("sign_type");
            return AlipaySignature.rsaCheckV1(params, publicKey, ALPConfigDTO.INPUT_CHARSET, signType);
        } catch (AlipayApiException e) {
            LOG.error("支付宝订单校验失败", e);
            return false;
        }
    }

    /**
     * 验证参数
     *
     * @param params
     * @param appId
     * @param sellerId
     * @return
     */
    private boolean verifyParams(Map<String, String> params, String appId, String sellerId) {
        return Objects.equals(appId, params.get("app_id")) && Objects.equals(sellerId, params.get("seller_id"));
    }
}
