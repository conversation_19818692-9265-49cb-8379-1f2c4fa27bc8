package com.tuowan.yeliao.acct.web.controller.cash;


import com.tuowan.yeliao.acct.service.cash.CashTransferService;
import com.tuowan.yeliao.acct.web.form.cash.transfer.TransferBIndForm;
import com.tuowan.yeliao.acct.web.form.cash.transfer.TransferCashForm;
import com.tuowan.yeliao.acct.web.form.cash.transfer.TransferVerifyForm;
import com.tuowan.yeliao.commons.context.config.Request;
import com.tuowan.yeliao.commons.core.http.ReturnUtils;
import com.tuowan.yeliao.commons.core.http.Root;
import com.tuowan.yeliao.commons.web.common.vo.ListVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @title 零钱转账管理
 * @date 2021/11/25 16:55
 */
@RestController
@RequestMapping("/acc/cash/transfer")
public class CashTransferController {

    @Autowired
    private CashTransferService cashTransferService;

    /**
     * @title 转账验证码验证
     */
    @Request
    @RequestMapping("/codeVerify")
    public Root<Void> codeVerify(@RequestBody TransferVerifyForm form) {
        cashTransferService.saveTransferVerify(form);
        return ReturnUtils.empty();
    }

    /**
     * @title 绑定转账账号
     */
    @Request
    @RequestMapping("/bind")
    public Root<Void> bind(@RequestBody TransferBIndForm form) {
        cashTransferService.saveBindUser(form);
        return ReturnUtils.empty();
    }

    /**
     * @title 零钱转账
     * 由于用户锁一个service 只能开一个，这里扣除和增加做先后分步处理
     */
    @Request
    @RequestMapping("/cash")
    public Root<Void> cash(@RequestBody TransferCashForm form) {
        // 先处理扣钱逻辑,即使报错不至于再增加零钱
        cashTransferService.saveTransferDeductCash(form);
        cashTransferService.saveTransferAddCash(form);
        return ReturnUtils.empty();
    }

    /**
     * 转账记录
     * 备注：最近50条
     */
    @Request
    @RequestMapping("/records")
    public Root<ListVO> transferRecord() {
        return ReturnUtils.root(cashTransferService.queryTransferLog());
    }

}
