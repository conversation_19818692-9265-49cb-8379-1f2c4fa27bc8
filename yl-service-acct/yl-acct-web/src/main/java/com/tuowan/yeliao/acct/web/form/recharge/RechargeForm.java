package com.tuowan.yeliao.acct.web.form.recharge;


import com.tuowan.yeliao.acct.comp.recharge.request.RechargeRequest;
import com.tuowan.yeliao.commons.web.common.form.Form;

import java.util.Map;

/**
 * 支付表单
 *
 * <AUTHOR>
 * @date 2021/6/23 21:30
 */
public interface RechargeForm extends Form {

    /**
     * 将表单转成充值请求参数对象
     *
     * @return
     */
    RechargeRequest toRechargeRequest();

    /**
     * 回调通知参数
     *
     * @return
     */
    Map<String, String> getNotifyParams();
}
