package com.tuowan.yeliao.acct.web.form.recharge;


import com.easyooo.framework.validate.config.LMNotEmpty;
import com.easyooo.framework.validate.config.LMNotNull;
import com.tuowan.yeliao.acct.comp.recharge.request.RechargeRequest;
import com.tuowan.yeliao.commons.core.enums.general.PayType;

import java.util.HashMap;
import java.util.Map;

/**
 * 苹果充值表单
 *
 * <AUTHOR>
 * @date 2021/7/3 11:39
 */
public class AppleNotifyForm implements RechargeForm {

    /**
     * 平台订单号
     */
    @LMNotNull
    private Long orderNo;
    /**
     * 商品ID
     */
    @LMNotEmpty
    private String productId;
    /**
     * 三方订单号
     */
    @LMNotEmpty
    private String transactionId;
    /**
     * 收据信息
     */
    @LMNotEmpty
    private String receipt;

    public Long getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(Long orderNo) {
        this.orderNo = orderNo;
    }

    public String getProductId() {
        return productId;
    }

    public void setProductId(String productId) {
        this.productId = productId;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public String getReceipt() {
        return receipt;
    }

    public void setReceipt(String receipt) {
        this.receipt = receipt;
    }

    @Override
    public RechargeRequest toRechargeRequest() {
        Map<String, String> extParams = new HashMap<>();
        extParams.put("receipt", this.receipt);
        RechargeRequest request = RechargeRequest.build(PayType.ApplePay, this.orderNo, this.transactionId);
        request.setProductId(this.productId);
        request.setNotifyParams(extParams);
        return request;
    }

    @Override
    public Map<String, String> getNotifyParams() {
        return null;
    }
}
