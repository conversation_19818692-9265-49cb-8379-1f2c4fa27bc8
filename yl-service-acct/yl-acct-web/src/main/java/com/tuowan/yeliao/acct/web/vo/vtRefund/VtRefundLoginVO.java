package com.tuowan.yeliao.acct.web.vo.vtRefund;

import com.tuowan.yeliao.commons.data.entity.user.UUserBasic;

public class VtRefundLoginVO {
    /**
     * 用户唯一标识
     */
    private Long userId;
    /**
     * 用户昵称
     */
    private String nickname;
    /**
     * 用户头像
     */
    private String headPic;
    /**
     * H5 session
     */
    private String sessionId;

    public static VtRefundLoginVO build1(UUserBasic userBasic, String sessionId) {
        VtRefundLoginVO vo = new VtRefundLoginVO();
        vo.setUserId(userBasic.getUserId());
        vo.setNickname(userBasic.getNickname());
        vo.setHeadPic(userBasic.getHeadPic());
        vo.setSessionId(sessionId);
        return vo;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getHeadPic() {
        return headPic;
    }

    public void setHeadPic(String headPic) {
        this.headPic = headPic;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }
}
