package com.tuowan.yeliao.acct.listener;

import com.tuowan.yeliao.acct.service.cash.WithdrawService;
import com.tuowan.yeliao.acct.service.wallet.RechargeService;
import com.tuowan.yeliao.acct.service.wallet.ViolationsRefundService;
import com.tuowan.yeliao.acct.service.wallet.VipService;
import com.tuowan.yeliao.commons.comp.user.dto.OpenVipResultDTO;
import com.tuowan.yeliao.commons.context.GlobalContext;
import com.tuowan.yeliao.commons.context.GlobalUtils;
import com.tuowan.yeliao.commons.core.enums.busicode.BusiCodeDefine;
import com.tuowan.yeliao.commons.core.enums.business.BusinessDataKey;
import com.tuowan.yeliao.commons.mq.annotation.Consumer;
import com.tuowan.yeliao.commons.mq.enums.MessageTag;
import com.tuowan.yeliao.commons.mq.listener.ContextDispatchMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 账户模块通用消费者
 *
 * <AUTHOR>
 * @date 2022/7/6 13:37
 */
@Component
@Consumer(MessageTag.AcctBusi)
public class AcctBusiListener extends ContextDispatchMessageListener {

    @Autowired
    private VipService vipService;
    @Autowired
    private RechargeService rechargeService;
    @Autowired
    private ViolationsRefundService violationsRefundService;
    @Autowired
    private WithdrawService withdrawService;

    @Override
    public void doProcessMessage(GlobalContext globalContext) {
        BusiCodeDefine busiCode = GlobalUtils.busiCodeName();
        if (BusiCodeDefine.UpdateRechargeResult == busiCode) {
            doProcess("处理充值邀请人提成", () -> {
                rechargeService.saveRechargeSucInvitorPresent();
            });
            doProcess("处理VIP开通", () -> {
                OpenVipResultDTO result = vipService.saveOpenVip();
                // VIP开通成功 通知弹窗
                if (result != null) {
                    vipService.saveSendOpenVipMessage(result);
                }
            });
            doProcess("壕气通知", () -> {
                rechargeService.sendHqNotice();
            });
            doProcess("男用户充值信息同步到ES", () -> {
                rechargeService.syncMaleRechargeInfoToEs();
            });
            doProcess("男用户首充转化奖励", () -> {
                rechargeService.saveMaleFirstRechargeConversationAward();
            });
            /*doProcess("处理充值赠送物品", () -> {
                rechargeService.saveRechargeSucGiveGoods();
            });*/
            /*doProcess("处理用户违规返款", () -> {
                violationsRefundService.saveViolationsRefundOrderFinish();
            });
            doProcess("首充相关业务处理", () -> {
                rechargeService.saveUserFirstRechargeDeal();
            });
            doProcess("新人礼包发放", () -> {
                rechargeService.saveNewUserBag(GlobalUtils.busiValue(BusinessDataKey.TplId));
            });*/
            return;
        }
        if (BusiCodeDefine.Online == busiCode) {
            /*doProcess("发放VIP每日奖励", () -> {
                vipService.saveVipGoodsDayAward();
            });*/
            doProcess("VIP消息提醒弹窗", () -> {
                vipService.saveVipUserMsgRemindPop();
            });
            return;
        }
        if (BusiCodeDefine.UserCashOut == busiCode) {
            doProcess("提现成功邀请人分成处理", () -> {
                withdrawService.saveWithdrawSucPresent();
            });
            return;
        }
        /*if (BusiCodeDefine.BindMobile == busiCode) {
            doProcess("绑定手机号完成尝试自动工猫电签", () -> {
                withdrawService.saveGmAutoInterview();
            });
            return;
        }*/
        if (BusiCodeDefine.RealNameCertification == busiCode) {
            doProcess("实名认证完成尝试自动工猫电签", () -> {
                withdrawService.saveGmAutoInterview();
            });
            return;
        }
    }
}
