package com.tuowan.yeliao.acct.web.form.recharge;

import com.easyooo.framework.validate.config.LMNotEmpty;
import com.easyooo.framework.validate.config.LMNotNull;
import com.tuowan.yeliao.commons.web.common.form.Form;

/**
 * 更新续订表单
 *
 * <AUTHOR>
 * @date 2022/7/21 16:44
 */
public class UpdateRenewForm implements Form {

    /**
     * 用户ID
     */
    @LMNotNull
    private Long userId;
    /**
     * 时间
     */
    @LMNotEmpty
    private String time;

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getTime() {
        return time;
    }

    public void setTime(String time) {
        this.time = time;
    }
}
