package com.tuowan.yeliao.acct.job;

import com.easyooo.framework.common.util.ListUtils;
import com.tuowan.yeliao.acct.comp.cash.WithdrawComponent;
import com.tuowan.yeliao.acct.data.entity.UCashWithdrawOrder;
import com.tuowan.yeliao.acct.service.cash.WithdrawService;
import com.tuowan.yeliao.acct.web.form.cash.withdraw.WithdrawSubmitForm;
import com.tuowan.yeliao.commons.context.proxy.ProxyExecutors;
import com.tuowan.yeliao.commons.core.enums.busicode.BackCodeDefine;
import com.tuowan.yeliao.commons.job.DefaultJob;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 动态相关定时任务
 *
 * <AUTHOR>
 * @date 2022/6/21 20:28
 */
@Component
public class WithDrawJob extends DefaultJob {

    @Autowired
    private WithdrawService withdrawService;
    @Autowired
    private WithdrawComponent withdrawComponent;

    /**
     * 向三方《微信、支付宝》提交提现申请
     * <p>
     * Cron: 0 0 * * * ?
     * 每分钟执行
     * @param param
     * @return
     */
    @XxlJob(JobKeyDefine.SubmitWithDrawApply)
    public ReturnT<String> submitWithDrawApply(String param) {
        long startTime = System.currentTimeMillis();
        List<UCashWithdrawOrder> values = withdrawComponent.queryWaitSubmitWithDrawDatas();
        if (ListUtils.isEmpty(values)) {
            return ReturnT.SUCCESS;
        }
        int successNum = 0;
        int failNum = 0;
        for (UCashWithdrawOrder item : values) {
            try {
                ProxyExecutors.doProxy(BackCodeDefine.SubmitWithdrawApply, context -> {
                    context.setReqTime(System.currentTimeMillis());
                    withdrawService.saveWithdrawSubmitForGm(item.getUserId(), item.getOrderNo());
                    return null;
                });
                successNum++;
            } catch (Exception e) {
                failNum++;
                LOG.error("此次提交提现申请失败，订单号：{}，原因：", item.getOrderNo(), e);
            }
        }
        long execTime = System.currentTimeMillis() - startTime;
        LOG.info("本次提交提现申请 成功：{}条，失败：{}条，耗时：{}ms", successNum, failNum, execTime);
        return ReturnT.SUCCESS;
    }
}
