package com.tuowan.yeliao.acct.web.vo.cash.cashinfo;


import com.tuowan.yeliao.acct.data.entity.UCash;
import com.tuowan.yeliao.acct.data.entity.UCashIncomeDayStat;
import com.tuowan.yeliao.commons.data.enums.user.SexType;
import com.tuowan.yeliao.commons.data.utils.BusiUtils;

import java.util.List;

/**
 * 零钱首页信息
 *
 * <AUTHOR>
 * @date 2020/7/22 10:59
 */
public class CashHomeVO {

    /**
     * 可提现收益
     */
    private String cash;
    /**
     * 今日收益
     */
    private String todayCash;
    /**
     * 累计收益
     */
    private String totalCash;
    /**
     * 收益统计日期
     */
    private String statDate;
    /**
     * 收益统计列表
     */
    private List<CashStatVO> statList;
    /**
     * 提现页面地址
     */
    private String withdrawUrl;
    /**
     * 用户性别
     */
    private SexType sexType;

    public static CashHomeVO create(UCash uCash, UCashIncomeDayStat todayStat, String statDate, List<CashStatVO> statList,
                                    String withdrawUrl, SexType sexType) {
        CashHomeVO vo = new CashHomeVO();
        if (uCash == null) {
            vo.setCash("0");
            vo.setTodayCash("0");
            vo.setTotalCash("0");
        } else {
            vo.setCash(BusiUtils.cashToYuanStr(uCash.getCash()));
            if (todayStat == null) {
                vo.setTodayCash("0");
            } else {
                vo.setTodayCash(BusiUtils.cashToYuanStr(todayStat.getTotalCash()));
            }
            vo.setTotalCash(BusiUtils.cashToYuanStr(uCash.getTotalCash()));
        }
        vo.setStatDate(statDate);
        vo.setStatList(statList);
        vo.setWithdrawUrl(withdrawUrl);
        vo.setSexType(sexType);
        return vo;
    }

    public String getCash() {
        return cash;
    }

    public void setCash(String cash) {
        this.cash = cash;
    }

    public String getTodayCash() {
        return todayCash;
    }

    public void setTodayCash(String todayCash) {
        this.todayCash = todayCash;
    }

    public String getTotalCash() {
        return totalCash;
    }

    public void setTotalCash(String totalCash) {
        this.totalCash = totalCash;
    }

    public String getStatDate() {
        return statDate;
    }

    public void setStatDate(String statDate) {
        this.statDate = statDate;
    }

    public List<CashStatVO> getStatList() {
        return statList;
    }

    public void setStatList(List<CashStatVO> statList) {
        this.statList = statList;
    }

    public String getWithdrawUrl() {
        return withdrawUrl;
    }

    public void setWithdrawUrl(String withdrawUrl) {
        this.withdrawUrl = withdrawUrl;
    }

    public SexType getSexType() {
        return sexType;
    }

    public void setSexType(SexType sexType) {
        this.sexType = sexType;
    }
}
