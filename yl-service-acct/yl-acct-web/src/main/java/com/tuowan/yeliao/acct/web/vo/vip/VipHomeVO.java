package com.tuowan.yeliao.acct.web.vo.vip;

import com.tuowan.yeliao.acct.data.dto.vip.PrivilegeDTO;
import com.tuowan.yeliao.acct.data.dto.vip.VipRuleDTO;
import com.tuowan.yeliao.acct.data.dto.vip.VipTplDTO;
import com.tuowan.yeliao.commons.data.dto.user.VipPrivilegeDTO;

import java.util.List;

public class VipHomeVO {
    // 用户ID
    private Long userId;
    // 昵称
    private String nickname;
    // 头像
    private String headPic;
    // 会员过期时间（返回为null 代表不是会员）
    private String vipExpireTime;

    // 会员模板
    private List<VipTplDTO> tplList;
    // 会员特权
    private List<VipPrivilegeDTO> privilegeList;
    // VIP服务协议
    private String vipProtocol;

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getHeadPic() {
        return headPic;
    }

    public void setHeadPic(String headPic) {
        this.headPic = headPic;
    }

    public String getVipExpireTime() {
        return vipExpireTime;
    }

    public void setVipExpireTime(String vipExpireTime) {
        this.vipExpireTime = vipExpireTime;
    }

    public List<VipTplDTO> getTplList() {
        return tplList;
    }

    public void setTplList(List<VipTplDTO> tplList) {
        this.tplList = tplList;
    }

    public List<VipPrivilegeDTO> getPrivilegeList() {
        return privilegeList;
    }

    public void setPrivilegeList(List<VipPrivilegeDTO> privilegeList) {
        this.privilegeList = privilegeList;
    }

    public String getVipProtocol() {
        return vipProtocol;
    }

    public void setVipProtocol(String vipProtocol) {
        this.vipProtocol = vipProtocol;
    }
}
