package com.tuowan.yeliao.acct.web.form.cash.withdraw;

import com.tuowan.yeliao.commons.web.common.form.Form;

public class GmCallBackForm implements Form {
     private String bankAccount; //"o-Xjs6Y1BgPXYwpnDuGQAx85nRX0"
     private String dateTime; //"**************"
     // 工猫平台订单号
     private String innerTradeNo; //"760Af56D09oB0008"
     // 提现金额
     private Double amount; //0.3
     // 当次缴纳个税 （算税方式为标准时个税）
     private Double currentTax; //0
     // 实际付款时间(yyyyMMddHHmmss)
     private String payTime; //"**************"
     private String mobile; //***********"
     private String sign; //"7A6C3BBACC9CDE47E8BA7A20E58F1A51"
     private String bankName; //"微信"
     // 当次提现管理费 （算税方式为标准时管理费）
     private Double currentManageFee; //0.01
     private String nonce; //"46e485dc1daa4af4a110fc2c4d61de2e"
     // 当次提现增值税（算税方式为标准时增值税）
     private Double currentAddValueTax; //0
     // 实发金额
     private Double currentRealWage; //0.3
     // 当次提现附加税（算税方式为标准时附加税）
     private Double currentAddTax; //0
     private String createTime; //"**************"
     // 客户请求标识，最长32位（用户提现时商户系统自动生成，每个requestId代表一个提现请求，工猫作为每笔提现记录的唯一标识）
     // 《也是我方订单号》
     private String requestId; //"AY10109985"
     private String identity; // "500101189910209016"
     private String name; //"陈鹏"
     private String appKey; //"68281c4da75c4edc9ee1a8821ccbbae0"
     // 状态(20:成功 30:失败)
     // 《根据这个状态判断是否提现成功》
     private Integer status; //20
     // 如果失败，返回的失败原因
     private String failReason; //哔哩哔哩

     public String getBankAccount() {
          return bankAccount;
     }

     public void setBankAccount(String bankAccount) {
          this.bankAccount = bankAccount;
     }

     public String getDateTime() {
          return dateTime;
     }

     public void setDateTime(String dateTime) {
          this.dateTime = dateTime;
     }

     public String getInnerTradeNo() {
          return innerTradeNo;
     }

     public void setInnerTradeNo(String innerTradeNo) {
          this.innerTradeNo = innerTradeNo;
     }

     public Double getAmount() {
          return amount;
     }

     public void setAmount(Double amount) {
          this.amount = amount;
     }

     public Double getCurrentTax() {
          return currentTax;
     }

     public void setCurrentTax(Double currentTax) {
          this.currentTax = currentTax;
     }

     public String getPayTime() {
          return payTime;
     }

     public void setPayTime(String payTime) {
          this.payTime = payTime;
     }

     public String getMobile() {
          return mobile;
     }

     public void setMobile(String mobile) {
          this.mobile = mobile;
     }

     public String getSign() {
          return sign;
     }

     public void setSign(String sign) {
          this.sign = sign;
     }

     public String getBankName() {
          return bankName;
     }

     public void setBankName(String bankName) {
          this.bankName = bankName;
     }

     public Double getCurrentManageFee() {
          return currentManageFee;
     }

     public void setCurrentManageFee(Double currentManageFee) {
          this.currentManageFee = currentManageFee;
     }

     public String getNonce() {
          return nonce;
     }

     public void setNonce(String nonce) {
          this.nonce = nonce;
     }

     public Double getCurrentAddValueTax() {
          return currentAddValueTax;
     }

     public void setCurrentAddValueTax(Double currentAddValueTax) {
          this.currentAddValueTax = currentAddValueTax;
     }

     public Double getCurrentRealWage() {
          return currentRealWage;
     }

     public void setCurrentRealWage(Double currentRealWage) {
          this.currentRealWage = currentRealWage;
     }

     public Double getCurrentAddTax() {
          return currentAddTax;
     }

     public void setCurrentAddTax(Double currentAddTax) {
          this.currentAddTax = currentAddTax;
     }

     public String getCreateTime() {
          return createTime;
     }

     public void setCreateTime(String createTime) {
          this.createTime = createTime;
     }

     public String getRequestId() {
          return requestId;
     }

     public void setRequestId(String requestId) {
          this.requestId = requestId;
     }

     public String getIdentity() {
          return identity;
     }

     public void setIdentity(String identity) {
          this.identity = identity;
     }

     public String getName() {
          return name;
     }

     public void setName(String name) {
          this.name = name;
     }

     public String getAppKey() {
          return appKey;
     }

     public void setAppKey(String appKey) {
          this.appKey = appKey;
     }

     public Integer getStatus() {
          return status;
     }

     public void setStatus(Integer status) {
          this.status = status;
     }

     public String getFailReason() {
          return failReason;
     }

     public void setFailReason(String failReason) {
          this.failReason = failReason;
     }
}
