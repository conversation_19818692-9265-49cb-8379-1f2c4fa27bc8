package com.tuowan.yeliao.acct.web.form.recharge;


import com.easyooo.framework.validate.config.LMNotNull;
import com.tuowan.yeliao.commons.core.enums.general.PayType;
import com.tuowan.yeliao.commons.data.enums.user.RechargeEntryType;
import com.tuowan.yeliao.commons.data.enums.user.VipTriggerType;
import com.tuowan.yeliao.commons.web.common.form.Form;

/**
 * 创建订单表单
 *
 * <AUTHOR>
 * @date 2021/7/3 14:36
 */
public class RechargeOrderForm implements Form {

    /** 支付类型 */
    @LMNotNull
    private PayType payType = PayType.ApplePay;
    /** 充值入口 */
    @LMNotNull
    private RechargeEntryType rechargeEntry;
    /** 到账用户ID(如果给自己充值，不需要填) */
    private Long targetUserId;
    /**
     * 充值模板ID
     */
    private Integer tplId;
    /**
     * 商品ID
     */
    private String productId;
    /**
     * 成功重定向地址(支付宝wap支付必填)
     */
    private String returnUrl;
    /**
     * 取消支跳转地址(支付宝wap支付必填)
     */
    private String quitUrl;
    /**
     * 开放平台唯一标识(微信H5支付必填)
     */
    private String openId;
    /**
     * Vip开通触发类型
     */
    private VipTriggerType triggerType;
    /**
     * 标识ID
     */
    private String signId;

    public PayType getPayType() {
        return payType;
    }

    public void setPayType(PayType payType) {
        this.payType = payType;
    }

    public RechargeEntryType getRechargeEntry() {
        return rechargeEntry;
    }

    public void setRechargeEntry(RechargeEntryType rechargeEntry) {
        this.rechargeEntry = rechargeEntry;
    }

    public Long getTargetUserId() {
        return targetUserId;
    }

    public void setTargetUserId(Long targetUserId) {
        this.targetUserId = targetUserId;
    }

    public Integer getTplId() {
        return tplId;
    }

    public void setTplId(Integer tplId) {
        this.tplId = tplId;
    }

    public String getProductId() {
        return productId;
    }

    public void setProductId(String productId) {
        this.productId = productId;
    }

    public String getReturnUrl() {
        return returnUrl;
    }

    public void setReturnUrl(String returnUrl) {
        this.returnUrl = returnUrl;
    }

    public String getQuitUrl() {
        return quitUrl;
    }

    public void setQuitUrl(String quitUrl) {
        this.quitUrl = quitUrl;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public VipTriggerType getTriggerType() {
        return triggerType;
    }

    public void setTriggerType(VipTriggerType triggerType) {
        this.triggerType = triggerType;
    }

    public String getSignId() {
        return signId;
    }

    public void setSignId(String signId) {
        this.signId = signId;
    }
}
