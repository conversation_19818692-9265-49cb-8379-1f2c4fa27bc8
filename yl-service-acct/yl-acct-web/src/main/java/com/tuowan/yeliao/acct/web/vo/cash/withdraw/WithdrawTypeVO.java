package com.tuowan.yeliao.acct.web.vo.cash.withdraw;

import com.easyooo.framework.common.util.StringUtils;
import com.tuowan.yeliao.acct.data.enums.WithdrawType;
import com.tuowan.yeliao.commons.core.enums.general.BoolType;
import com.tuowan.yeliao.commons.core.enums.general.ClientType;
import com.tuowan.yeliao.commons.data.entity.user.UIdentityAuth;

import java.util.ArrayList;
import java.util.List;

/**
 * 提现类型信息
 *
 * <AUTHOR>
 * @date 2020/7/17 13:48
 */
public class WithdrawTypeVO {

    /** 提现类型 */
    private WithdrawType type;
    /** 到账昵称 */
    private String nickname;
    /** 是否已授权 */
    private BoolType authorized;
    /** 错误弹窗title authorized=False才有值*/
    private String errorTitle;
    /** 错误弹窗content authorized=False才有值*/
    private String errorContent;

    public static List<WithdrawTypeVO> create(UIdentityAuth identityAuth, ClientType curClientType, BoolType realName, ClientType sourceClientType) {
        List<WithdrawTypeVO> resultList = new ArrayList<>();
        for (WithdrawType type : WithdrawType.values()) {
            WithdrawTypeVO vo = new WithdrawTypeVO();
            vo.setType(type);
            vo.setAuthorized(BoolType.False);
            if (WithdrawType.Weixin == type) {
                if (identityAuth == null || StringUtils.isEmpty(identityAuth.getWeixinUserId())) {
                    // 1. 先判断是否有绑定微信
                    vo.setErrorTitle("微信绑定提醒");
                    vo.setErrorContent("请在\"设置·账号与安全\"绑定本人实名的微信，才可提现哦");
                }else if(BoolType.True != realName){
                    // 2. 再判断用户是否有实名认证
                    vo.setErrorTitle("实名认证提醒");
                    vo.setErrorContent("为保证你的账号安全，提现前请先进行实名认证");
                }else{
                    vo.setAuthorized(BoolType.True);
                    vo.setNickname(identityAuth.getWeixinNickname());
                }
            } else if(WithdrawType.Alipay == type){
                // ios 排除掉支付宝提现
                if(ClientType.iOS == sourceClientType){
                    continue;
                }
                if (identityAuth == null || StringUtils.isEmpty(identityAuth.getAlipayUserId())) {
                    // 1. 先判断是否有绑定支付宝
                    vo.setErrorTitle("支付宝绑定提醒");
                    vo.setErrorContent("请在\"设置·账号与安全\"绑定本人实名的支付宝，才可提现哦");
                }else if(BoolType.True != realName){
                    // 2. 再判断用户是否有实名认证
                    vo.setErrorTitle("实名认证提醒");
                    vo.setErrorContent("为保证你的账号安全，提现前请先进行实名认证");
                }else{
                    vo.setAuthorized(BoolType.True);
                    vo.setNickname(identityAuth.getAlipayNickname());
                }
            } else{
                // 目前仅对 微信、支付宝做处理
                continue;
            }
            resultList.add(vo);
        }
        return resultList;
    }

    public WithdrawType getType() {
        return type;
    }

    public void setType(WithdrawType type) {
        this.type = type;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public BoolType getAuthorized() {
        return authorized;
    }

    public void setAuthorized(BoolType authorized) {
        this.authorized = authorized;
    }

    public String getErrorTitle() {
        return errorTitle;
    }

    public void setErrorTitle(String errorTitle) {
        this.errorTitle = errorTitle;
    }

    public String getErrorContent() {
        return errorContent;
    }

    public void setErrorContent(String errorContent) {
        this.errorContent = errorContent;
    }
}
