package com.tuowan.yeliao.acct.data.manager.cashstat;

import com.easyooo.framework.common.util.MsgUtils;
import com.tuowan.yeliao.acct.data.dto.cash.UserCashDTO;
import com.tuowan.yeliao.acct.data.dto.invite.InviteStatDTO;
import com.tuowan.yeliao.commons.data.utils.BusiUtils;


/**
 * 零钱统计通用业务实现
 *
 * <AUTHOR>
 * @date 2020/9/16 10:52
 */
public abstract class AbstractCashStatFiller implements ICashStatFiller {


    @Override
    public String getInviteStatDiffInfo(UserCashDTO topUser, InviteStatDTO topStat, InviteStatDTO myStat) {
        if (topStat == null) {
            return "昨日还未产生冠军，快去争取今日的冠军吧~";
        }
        if (myStat != null && topStat.getUserId().equals(myStat.getUserId())) {
            return MsgUtils.format("恭喜你夺得昨日{}冠军，再接再厉哦~", getType().getDesc());
        }
        String diffInfo = getInviteStatDiffInfo(topStat, myStat);
        if (diffInfo == null) {
            return "昨日还未产生冠军，快去争取今日的冠军吧~";
        }
        return MsgUtils.format("Ta昨日收益{}元，{}", BusiUtils.cashToYuanStr(topUser.getCash()), diffInfo);
    }

    protected String getInviteStatDiffInfo(InviteStatDTO topStat, InviteStatDTO myStat) {
        return null;
    }
}
