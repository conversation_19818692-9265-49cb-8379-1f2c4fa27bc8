/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.acct.data.persistence;

import com.easyooo.framework.sharding.annotation.Table;
import com.tuowan.yeliao.acct.data.entity.UCashChange;
import org.springframework.stereotype.Repository;

@Repository
@Table(value = "U_CASH_CHANGE", schema = "YL_ACCT")
public interface UCashChangeMapper {
    int deleteByPrimaryKey(UCashChange record);

    int insert(UCashChange record);

    UCashChange selectByPrimaryKey(UCashChange record);

    int updateByPrimaryKeySelective(UCashChange record);

    int updateByPrimaryKey(UCashChange record);
}