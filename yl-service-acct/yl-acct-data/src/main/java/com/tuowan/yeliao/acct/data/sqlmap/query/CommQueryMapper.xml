<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tuowan.yeliao.acct.data.persistence.query.CommQueryMapper">
  <select id="queryInvitorHasIncomeFemales" resultType="java.lang.Long">
    select uif.friend_id
    from yl_social.u_invite_friends as uif
    left join yl_acct.u_cash as uc on uif.friend_id = uc.user_id
    where uif.user_id = #{invitor} and uc.cash >= 500000
  </select>

  <select id="queryNetCallInfo" resultType="com.tuowan.yeliao.acct.data.dto.common.NetCallDTO">
    select
    call_id, user_id, friend_id
    from yl_social.f_chat_net_call
    where call_id = #{callId}
  </select>

</mapper>