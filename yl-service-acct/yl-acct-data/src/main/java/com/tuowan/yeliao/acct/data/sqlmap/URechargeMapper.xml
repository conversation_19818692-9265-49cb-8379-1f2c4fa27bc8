<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tuowan.yeliao.acct.data.persistence.URechargeMapper">
  <sql id="Base_Column_List">
    biz_id, order_no, target_user_id, pay_type, channel_id, recharge_entry, tpl_id, pay_amount,
    pay_status, pay_order_no, pay_callback_time,
    package_type, client_type, client_version, is_new_pay, mch_id, sign_id, remark, creator, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="URecharge" resultType="URecharge">
    select 
    <include refid="Base_Column_List" />
    from u_recharge
    where biz_id = #{bizId}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="URecharge">
    delete from u_recharge
    where biz_id = #{bizId}
  </delete>
  <insert id="insert" parameterType="URecharge" useGeneratedKeys="true" keyProperty="bizId">
      insert into u_recharge (biz_id, order_no, target_user_id, pay_type, channel_id, recharge_entry,
                              tpl_id, pay_amount, pay_status,
                              pay_order_no, pay_callback_time, package_type, client_type, client_version,
                              is_new_pay, mch_id, sign_id, remark, creator, create_time)
      values (#{bizId}, #{orderNo}, #{targetUserId}, #{payType}, #{channelId}, #{rechargeEntry},
              #{tplId}, #{payAmount}, #{payStatus},
              #{payOrderNo}, #{payCallbackTime}, #{packageType}, #{clientType}, #{clientVersion},
              #{isNewPay}, #{mchId}, #{signId}, #{remark}, #{creator}, #{createTime})
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="URecharge">
    update u_recharge
    <set>
      <if test="orderNo != null">
        order_no = #{orderNo},
      </if>
      <if test="targetUserId != null">
        target_user_id = #{targetUserId},
      </if>
      <if test="payType != null">
        pay_type = #{payType},
      </if>
      <if test="channelId != null">
        channel_id = #{channelId},
      </if>
      <if test="rechargeEntry != null">
        recharge_entry = #{rechargeEntry},
      </if>
      <if test="tplId != null">
        tpl_id = #{tplId},
      </if>
      <if test="payAmount != null">
        pay_amount = #{payAmount},
      </if>
      <if test="payStatus != null">
        pay_status = #{payStatus},
      </if>
      <if test="payOrderNo != null">
        pay_order_no = #{payOrderNo},
      </if>
      <if test="payCallbackTime != null">
        pay_callback_time = #{payCallbackTime},
      </if>
      <if test="packageType != null">
        package_type = #{packageType},
      </if>
      <if test="clientType != null">
        client_type = #{clientType},
      </if>
        <if test="clientVersion != null">
            client_version = #{clientVersion},
        </if>
        <if test="isNewPay != null">
            is_new_pay = #{isNewPay},
        </if>
        <if test="mchId != null">
            mch_id = #{mchId},
        </if>
        <if test="signId != null">
            sign_id = #{signId},
        </if>
        <if test="remark != null">
            remark = #{remark},
        </if>
        <if test="creator != null">
            creator = #{creator},
        </if>
        <if test="createTime != null">
            create_time = #{createTime},
        </if>
    </set>
    where biz_id = #{bizId}
  </update>
  <update id="updateByPrimaryKey" parameterType="URecharge">
    update u_recharge
    set order_no          = #{orderNo},
        target_user_id    = #{targetUserId},
        pay_type          = #{payType},
        channel_id        = #{channelId},
        recharge_entry    = #{rechargeEntry},
        tpl_id            = #{tplId},
        pay_amount        = #{payAmount},
        pay_status        = #{payStatus},
        pay_order_no      = #{payOrderNo},
        pay_callback_time = #{payCallbackTime},
        package_type      = #{packageType},
        client_type       = #{clientType},
        client_version    = #{clientVersion},
        is_new_pay        = #{isNewPay},
        mch_id            = #{mchId},
        sign_id           = #{signId},
        remark            = #{remark},
        creator           = #{creator},
        create_time       = #{createTime}
    where biz_id = #{bizId}
  </update>
    <select id="selectByKeyForUpdate" parameterType="URecharge" resultType="URecharge">
        select
        <include refid="Base_Column_List" />
        from u_recharge
        where biz_id = #{bizId}
        for update
    </select>

    <select id="selectByOrderNo" resultType="URecharge">
        select
        <include refid="Base_Column_List" />
        from u_recharge
        where order_no = #{orderNo}
    </select>
</mapper>