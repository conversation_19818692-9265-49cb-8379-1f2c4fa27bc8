/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.acct.data.persistence;

import com.easyooo.framework.sharding.annotation.Table;
import com.tuowan.yeliao.acct.data.entity.UExchangeOrder;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@Table(value = "U_EXCHANGE_ORDER", schema = "YL_ACCT")
public interface UExchangeOrderMapper {
    int deleteByPrimaryKey(UExchangeOrder record);

    int insert(UExchangeOrder record);

    UExchangeOrder selectByPrimaryKey(UExchangeOrder record);

    int updateByPrimaryKeySelective(UExchangeOrder record);

    int updateByPrimaryKey(UExchangeOrder record);

    List<UExchangeOrder> selectByUserId(@Param("userId") Long userId);
}