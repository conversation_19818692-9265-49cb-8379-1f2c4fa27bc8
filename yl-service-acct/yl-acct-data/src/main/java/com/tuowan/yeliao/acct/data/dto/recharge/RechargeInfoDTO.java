package com.tuowan.yeliao.acct.data.dto.recharge;


import com.tuowan.yeliao.commons.data.entity.config.TRechargeTpl;

import java.util.ArrayList;
import java.util.List;

/**
 * 充值信息DTO
 *
 * <AUTHOR>
 * @date 2018/8/9 18:10
 */
public class RechargeInfoDTO {

    /**
     * 支持的充值类型
     */
    private String payTypes;

    /**
     * 充值模板列表
     */
    private List<TRechargeTpl> tplList;

    /**
     * 已使用过的模版ID
     */
    private List<Integer> usedTplIds = new ArrayList<>();

    public String getPayTypes() {
        return payTypes;
    }

    public void setPayTypes(String payTypes) {
        this.payTypes = payTypes;
    }

    public List<TRechargeTpl> getTplList() {
        return tplList;
    }

    public void setTplList(List<TRechargeTpl> tplList) {
        this.tplList = tplList;
    }

    public List<Integer> getUsedTplIds() {
        return usedTplIds;
    }

    public void setUsedTplIds(List<Integer> usedTplIds) {
        this.usedTplIds = usedTplIds;
    }
}
