<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tuowan.yeliao.acct.data.persistence.UVipOpenMapper">
  <sql id="Base_Column_List">
    user_id, vip_type, vip_exp_time, surplus_times, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="UVipOpen" resultType="UVipOpen">
    select 
    <include refid="Base_Column_List" />
    from u_vip_open
    where user_id = #{userId}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="UVipOpen">
    delete from u_vip_open
    where user_id = #{userId}
  </delete>
  <insert id="insert" parameterType="UVipOpen">
    insert into u_vip_open (user_id, vip_type, vip_exp_time, surplus_times, create_time)
    values (#{userId}, #{vipType}, #{vipExpTime}, #{surplusTimes}, #{createTime})
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="UVipOpen">
    update u_vip_open
    <set>
      <if test="vipType != null">
        vip_type = #{vipType},
      </if>
      <if test="vipExpTime != null">
        vip_exp_time = #{vipExpTime},
      </if>
      <if test="surplusTimes != null">
        surplus_times = #{surplusTimes},
      </if>
      <if test="createTime != null">
        create_time = #{createTime},
      </if>
    </set>
    where user_id = #{userId}
  </update>
  <update id="updateByPrimaryKey" parameterType="UVipOpen">
    update u_vip_open
    set vip_type = #{vipType},
      vip_exp_time = #{vipExpTime},
      surplus_times = #{surplusTimes},
      create_time = #{createTime}
    where user_id = #{userId}
  </update>

  <select id="selectVipExpireUserIds" resultType="long">
    select user_id
    from u_vip_open
    where vip_exp_time &gt;= #{beginTime} and vip_exp_time &lt; #{endTime}
    and user_id % #{shardTotal} = #{shardIndex}
  </select>
</mapper>