package com.tuowan.yeliao.acct.data.persistence;

import com.easyooo.framework.sharding.annotation.Table;
import com.tuowan.yeliao.acct.data.dto.common.EcuDTO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 用户相关查询
 *
 * <AUTHOR>
 * @date 2020/7/28 17:22
 */
@Repository
@Table(schema = "YL_QUERY")
public interface UserQueryMapper {

    /**
     * 获取用户当前周收益(聊天、音视频、礼物)统计
     *
     * @return
     */
    Long countWeekChatCash(@Param("userId") Long userId);

    int countReportRecord(@Param("userId") Long userId);

    /**
     * 获取待电签女用户
     */
    List<Long> queryWaitInterviewUsers();

    /**
     * 获取女用户专属回复设置
     */
    List<EcuDTO> queryFemaleEcus(@Param("userId") Long userId);
}
