/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.acct.data.persistence;

import com.easyooo.framework.sharding.annotation.Table;
import com.tuowan.yeliao.acct.data.entity.UCashIncomeLogDetail;
import org.springframework.stereotype.Repository;

@Repository
@Table(value = "U_CASH_INCOME_LOG_DETAIL", schema = "YL_ACCT")
public interface UCashIncomeLogDetailMapper {
    int deleteByPrimaryKey(UCashIncomeLogDetail record);

    int insert(UCashIncomeLogDetail record);

    UCashIncomeLogDetail selectByPrimaryKey(UCashIncomeLogDetail record);

    int updateByPrimaryKeySelective(UCashIncomeLogDetail record);

    int updateByPrimaryKey(UCashIncomeLogDetail record);
}