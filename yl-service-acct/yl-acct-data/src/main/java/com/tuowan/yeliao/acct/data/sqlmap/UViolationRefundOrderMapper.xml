<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tuowan.yeliao.acct.data.persistence.UViolationRefundOrderMapper">
    <sql id="Base_Column_List">
        biz_id
        , user_id, user_mobile, refund_money, refund_status, creator, pay_expire_time, create_time,
    recharge_biz_id, pay_time, contact_user, contact_time, violation_reason, refund_remark, contact_remark
    </sql>
    <select id="selectByPrimaryKey" parameterType="UViolationRefundOrder" resultType="UViolationRefundOrder">
        select
        <include refid="Base_Column_List"/>
        from u_violation_refund_order
        where biz_id = #{bizId}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="UViolationRefundOrder">
        delete
        from u_violation_refund_order
        where biz_id = #{bizId}
    </delete>
    <insert id="insert" parameterType="UViolationRefundOrder" keyProperty="bizId" useGeneratedKeys="true">
        insert into u_violation_refund_order (biz_id, user_id, user_mobile, refund_money, refund_status, creator,
                                              pay_expire_time,
                                              create_time, recharge_biz_id, pay_time, contact_user, contact_time,
                                              violation_reason,
                                              refund_remark, contact_remark)
        values (#{bizId}, #{userId}, #{userMobile}, #{refundMoney}, #{refundStatus}, #{creator}, #{payExpireTime},
                #{createTime}, #{rechargeBizId}, #{payTime}, #{contactUser}, #{contactTime}, #{violationReason},
                #{refundRemark}, #{contactRemark})
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="UViolationRefundOrder">
        update u_violation_refund_order
        <set>
            <if test="userId != null">
                user_id = #{userId},
            </if>
            <if test="userMobile != null">
                user_mobile = #{userMobile},
            </if>
            <if test="refundMoney != null">
                refund_money = #{refundMoney},
            </if>
            <if test="refundStatus != null">
                refund_status = #{refundStatus},
            </if>
            <if test="creator != null">
                creator = #{creator},
            </if>
            <if test="payExpireTime != null">
                pay_expire_time = #{payExpireTime},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="rechargeBizId != null">
                recharge_biz_id = #{rechargeBizId},
            </if>
            <if test="payTime != null">
                pay_time = #{payTime},
            </if>
            <if test="contactUser != null">
                contact_user = #{contactUser},
            </if>
            <if test="contactTime != null">
                contact_time = #{contactTime},
            </if>
            <if test="violationReason != null">
                violation_reason = #{violationReason},
            </if>
            <if test="refundRemark != null">
                refund_remark = #{refundRemark},
            </if>
            <if test="contactRemark != null">
                contact_remark = #{contactRemark},
            </if>
        </set>
        where biz_id = #{bizId}
    </update>
    <update id="updateByPrimaryKey" parameterType="UViolationRefundOrder">
        update u_violation_refund_order
        set user_id          = #{userId},
            user_mobile      = #{userMobile},
            refund_money     = #{refundMoney},
            refund_status    = #{refundStatus},
            creator          = #{creator},
            pay_expire_time  = #{payExpireTime},
            create_time      = #{createTime},
            recharge_biz_id  = #{rechargeBizId},
            pay_time         = #{payTime},
            contact_user     = #{contactUser},
            contact_time     = #{contactTime},
            violation_reason = #{violationReason},
            refund_remark    = #{refundRemark},
            contact_remark   = #{contactRemark}
        where biz_id = #{bizId}
    </update>

    <select id="listByUserId" resultType="UViolationRefundOrder">
        select
        <include refid="Base_Column_List"/>
        from u_violation_refund_order
        where user_id = #{userId}
        order by biz_id desc
        limit #{offset},#{limit}
    </select>
</mapper>