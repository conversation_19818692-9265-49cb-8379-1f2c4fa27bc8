<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tuowan.yeliao.acct.data.persistence.URechargeTokenMapper">
  <sql id="Base_Column_List">
    purchase_token, pay_type, biz_id, pay_order_no, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="URechargeToken" resultType="URechargeToken">
    select 
    <include refid="Base_Column_List" />
    from u_recharge_token
    where purchase_token = #{purchaseToken}
      and pay_type = #{payType}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="URechargeToken">
    delete from u_recharge_token
    where purchase_token = #{purchaseToken}
      and pay_type = #{payType}
  </delete>
  <insert id="insert" parameterType="URechargeToken">
    insert into u_recharge_token (purchase_token, pay_type, biz_id, pay_order_no, create_time)
    values (#{purchaseToken}, #{payType}, #{bizId}, #{payOrderNo}, #{createTime})
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="URechargeToken">
    update u_recharge_token
    <set>
      <if test="bizId != null">
        biz_id = #{bizId},
      </if>
      <if test="payOrderNo != null">
        pay_order_no = #{payOrderNo},
      </if>
      <if test="createTime != null">
        create_time = #{createTime},
      </if>
    </set>
    where purchase_token = #{purchaseToken}
      and pay_type = #{payType}
  </update>
  <update id="updateByPrimaryKey" parameterType="URechargeToken">
    update u_recharge_token
    set biz_id = #{bizId},
      pay_order_no = #{payOrderNo},
      create_time = #{createTime}
    where purchase_token = #{purchaseToken}
      and pay_type = #{payType}
  </update>
</mapper>