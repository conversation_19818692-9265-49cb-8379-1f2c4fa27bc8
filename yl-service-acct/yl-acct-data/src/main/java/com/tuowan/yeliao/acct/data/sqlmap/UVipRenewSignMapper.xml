<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tuowan.yeliao.acct.data.persistence.UVipRenewSignMapper">
    <sql id="Base_Column_List">
        user_id, sign_no, pay_type, client_type, client_version, tpl_id, period_type, period,
        single_amount, execute_time, package_type, sign_status, callback_time, external_sign_no,
        remark, mch_id, external_user_id, sign_time, pay_fail_count, beans, give_beans, ext_json_cfg,
        valid_time, invalid_time, create_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="UVipRenewSign" resultType="UVipRenewSign">
        select
        <include refid="Base_Column_List"/>
        from u_vip_renew_sign
        where user_id = #{userId}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="UVipRenewSign">
        delete from u_vip_renew_sign
        where user_id = #{userId}
    </delete>
    <insert id="insert" parameterType="UVipRenewSign">
        insert into u_vip_renew_sign (user_id, sign_no, pay_type, client_type, client_version, tpl_id,
        period_type, period, single_amount, execute_time, package_type, sign_status,
        callback_time, external_sign_no, remark, mch_id, external_user_id, sign_time,
        pay_fail_count, beans, give_beans, ext_json_cfg, valid_time, invalid_time,
        create_time)
        values (#{userId}, #{signNo}, #{payType}, #{clientType}, #{clientVersion}, #{tplId},
        #{periodType}, #{period}, #{singleAmount}, #{executeTime}, #{packageType}, #{signStatus},
        #{callbackTime}, #{externalSignNo}, #{remark}, #{mchId}, #{externalUserId}, #{signTime},
        #{payFailCount}, #{beans}, #{giveBeans}, #{extJsonCfg}, #{validTime}, #{invalidTime},
        #{createTime})
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="UVipRenewSign">
        update u_vip_renew_sign
        <set>
            <if test="signNo != null">
                sign_no = #{signNo},
            </if>
            <if test="payType != null">
                pay_type = #{payType},
            </if>
            <if test="clientType != null">
                client_type = #{clientType},
            </if>
            <if test="clientVersion != null">
                client_version = #{clientVersion},
            </if>
            <if test="tplId != null">
                tpl_id = #{tplId},
            </if>
            <if test="periodType != null">
                period_type = #{periodType},
            </if>
            <if test="period != null">
                period = #{period},
            </if>
            <if test="singleAmount != null">
                single_amount = #{singleAmount},
            </if>
            <if test="executeTime != null">
                execute_time = #{executeTime},
            </if>
            <if test="packageType != null">
                package_type = #{packageType},
            </if>
            <if test="signStatus != null">
                sign_status = #{signStatus},
            </if>
            <if test="callbackTime != null">
                callback_time = #{callbackTime},
            </if>
            <if test="externalSignNo != null">
                external_sign_no = #{externalSignNo},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
            <if test="mchId != null">
                mch_id = #{mchId},
            </if>
            <if test="externalUserId != null">
                external_user_id = #{externalUserId},
            </if>
            <if test="signTime != null">
                sign_time = #{signTime},
            </if>
            <if test="payFailCount != null">
                <if test="payFailCount == 0">
                    pay_fail_count = #{payFailCount},
                </if>
                <if test="payFailCount > 0">
                    pay_fail_count = pay_fail_count + #{payFailCount},
                </if>
            </if>
            <if test="beans != null">
                beans = #{beans},
            </if>
            <if test="giveBeans != null">
                give_beans = #{giveBeans},
            </if>
            <if test="extJsonCfg != null">
                ext_json_cfg = #{extJsonCfg},
            </if>
            <if test="validTime != null">
                valid_time = #{validTime},
            </if>
            <if test="invalidTime != null">
                invalid_time = #{invalidTime},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
        </set>
        where user_id = #{userId}
    </update>
    <update id="updateByPrimaryKey" parameterType="UVipRenewSign">
        update u_vip_renew_sign
        set sign_no = #{signNo},
        pay_type = #{payType},
        client_type = #{clientType},
        client_version = #{clientVersion},
        tpl_id = #{tplId},
        period_type = #{periodType},
        period = #{period},
        single_amount = #{singleAmount},
        execute_time = #{executeTime},
        package_type = #{packageType},
        sign_status = #{signStatus},
        callback_time = #{callbackTime},
        external_sign_no = #{externalSignNo},
        remark = #{remark},
        mch_id = #{mchId},
        external_user_id = #{externalUserId},
        sign_time = #{signTime},
        pay_fail_count = #{payFailCount},
        beans = #{beans},
        give_beans = #{giveBeans},
        ext_json_cfg = #{extJsonCfg},
        valid_time = #{validTime},
        invalid_time = #{invalidTime},
        create_time = #{createTime}
        where user_id = #{userId}
    </update>

    <select id="selectBySignNo" resultType="UVipRenewSign">
        select
        <include refid="Base_Column_List"/>
        from u_vip_renew_sign
        where sign_no = #{signNo}
        limit 1
    </select>

    <select id="getWaitRenewDeductList" resultType="UVipRenewSign">
        select
        <include refid="Base_Column_List"/>
        from u_vip_renew_sign
        where user_id % #{shardTotal} = #{shardIndex}
        and execute_time &lt;= #{targetDate} and sign_status = 'N' and pay_type = 'ALA'
    </select>

    <select id="queryRenewRemindList" resultType="long">
        select user_id
        from u_vip_renew_sign
        where user_id % #{shardTotal} = #{shardIndex}
        and execute_time = date_add(curdate(), interval 5 day) and sign_status = 'N'
    </select>
</mapper>