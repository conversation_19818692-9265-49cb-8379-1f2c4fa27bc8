<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tuowan.yeliao.acct.data.persistence.UVipRenewSignHisMapper">
    <sql id="Base_Column_List">
        log_id, user_id, sign_no, pay_type, client_type, client_version, tpl_id, period_type,
        period, single_amount, execute_time, package_type, callback_time, external_sign_no,
        remark, mch_id, external_user_id, sign_time, create_time, un_sign_type, un_sign_time, beans, give_beans,
        ext_json_cfg, valid_time, invalid_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="UVipRenewSignHis" resultType="UVipRenewSignHis">
        select
        <include refid="Base_Column_List"/>
        from u_vip_renew_sign_his
        where log_id = #{logId}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="UVipRenewSignHis">
        delete from u_vip_renew_sign_his
        where log_id = #{logId}
    </delete>
    <insert id="insert" parameterType="UVipRenewSignHis">
        insert into u_vip_renew_sign_his (log_id, user_id, sign_no, pay_type, client_type, client_version,
        tpl_id, period_type, period, single_amount, execute_time, package_type,
        callback_time, external_sign_no, remark, mch_id, external_user_id, sign_time,
        create_time, un_sign_type, un_sign_time, beans, give_beans, ext_json_cfg, valid_time,
        invalid_time)
        values (#{logId}, #{userId}, #{signNo}, #{payType}, #{clientType}, #{clientVersion},
        #{tplId}, #{periodType}, #{period}, #{singleAmount}, #{executeTime}, #{packageType},
        #{callbackTime}, #{externalSignNo}, #{remark}, #{mchId}, #{externalUserId}, #{signTime},
        #{createTime}, #{unSignType}, #{unSignTime}, #{beans}, #{giveBeans}, #{extJsonCfg}, #{validTime},
        #{invalidTime})
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="UVipRenewSignHis">
        update u_vip_renew_sign_his
        <set>
            <if test="userId != null">
                user_id = #{userId},
            </if>
            <if test="signNo != null">
                sign_no = #{signNo},
            </if>
            <if test="payType != null">
                pay_type = #{payType},
            </if>
            <if test="clientType != null">
                client_type = #{clientType},
            </if>
            <if test="clientVersion != null">
                client_version = #{clientVersion},
            </if>
            <if test="tplId != null">
                tpl_id = #{tplId},
            </if>
            <if test="periodType != null">
                period_type = #{periodType},
            </if>
            <if test="period != null">
                period = #{period},
            </if>
            <if test="singleAmount != null">
                single_amount = #{singleAmount},
            </if>
            <if test="executeTime != null">
                execute_time = #{executeTime},
            </if>
            <if test="packageType != null">
                package_type = #{packageType},
            </if>
            <if test="callbackTime != null">
                callback_time = #{callbackTime},
            </if>
            <if test="externalSignNo != null">
                external_sign_no = #{externalSignNo},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
            <if test="mchId != null">
                mch_id = #{mchId},
            </if>
            <if test="externalUserId != null">
                external_user_id = #{externalUserId},
            </if>
            <if test="signTime != null">
                sign_time = #{signTime},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="unSignType != null">
                un_sign_type = #{unSignType},
            </if>
            <if test="unSignTime != null">
                un_sign_time = #{unSignTime},
            </if>
            <if test="beans != null">
                beans = #{beans},
            </if>
            <if test="giveBeans != null">
                give_beans = #{giveBeans},
            </if>
            <if test="extJsonCfg != null">
                ext_json_cfg = #{extJsonCfg},
            </if>
            <if test="validTime != null">
                valid_time = #{validTime},
            </if>
            <if test="invalidTime != null">
                invalid_time = #{invalidTime},
            </if>
        </set>
        where log_id = #{logId}
    </update>
    <update id="updateByPrimaryKey" parameterType="UVipRenewSignHis">
        update u_vip_renew_sign_his
        set user_id = #{userId},
        sign_no = #{signNo},
        pay_type = #{payType},
        client_type = #{clientType},
        client_version = #{clientVersion},
        tpl_id = #{tplId},
        period_type = #{periodType},
        period = #{period},
        single_amount = #{singleAmount},
        execute_time = #{executeTime},
        package_type = #{packageType},
        callback_time = #{callbackTime},
        external_sign_no = #{externalSignNo},
        remark = #{remark},
        mch_id = #{mchId},
        external_user_id = #{externalUserId},
        sign_time = #{signTime},
        create_time = #{createTime},
        un_sign_type = #{unSignType},
        un_sign_time = #{unSignTime},
        beans = #{beans},
        give_beans = #{giveBeans},
        ext_json_cfg = #{extJsonCfg},
        valid_time = #{validTime},
        invalid_time = #{invalidTime}
        where log_id = #{logId}
    </update>
</mapper>