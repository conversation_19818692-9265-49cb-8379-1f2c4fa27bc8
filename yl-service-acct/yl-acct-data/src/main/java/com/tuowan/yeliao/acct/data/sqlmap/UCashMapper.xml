<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tuowan.yeliao.acct.data.persistence.UCashMapper">
    <sql id="Base_Column_List">
        user_id, cash, total_cash, withdraw, withdraw_total,
        last_withdraw_time, friend_total_cash, task_total_cash, active_total_cash, invite_total_cash,
        it_charge_total_cash, other_total_cash
    </sql>
    <select id="selectByPrimaryKey" parameterType="UCash" resultType="UCash">
        select
        <include refid="Base_Column_List"/>
        from u_cash
        where user_id = #{userId}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="UCash">
        delete from u_cash
        where user_id = #{userId}
    </delete>
    <insert id="insert" parameterType="UCash">
        insert into u_cash (user_id, cash, total_cash,
        withdraw, withdraw_total, last_withdraw_time, friend_total_cash, task_total_cash,
        active_total_cash, invite_total_cash, it_charge_total_cash, other_total_cash)
        values (#{userId}, #{cash}, #{totalCash},
        #{withdraw}, #{withdrawTotal}, #{lastWithdrawTime}, #{friendTotalCash}, #{taskTotalCash},
        #{activeTotalCash}, #{inviteTotalCash}, #{itChargeTotalCash}, #{otherTotalCash})
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="UCash">
        update u_cash
        <set>
            <if test="cash != null">
                cash = #{cash},
            </if>
            <if test="totalCash != null">
                total_cash = #{totalCash},
            </if>
            <if test="withdraw != null">
                withdraw = #{withdraw},
            </if>
            <if test="withdrawTotal != null">
                withdraw_total = #{withdrawTotal},
            </if>
            <if test="lastWithdrawTime != null">
                last_withdraw_time = #{lastWithdrawTime},
            </if>
            <if test="friendTotalCash != null">
                friend_total_cash = #{friendTotalCash},
            </if>
            <if test="taskTotalCash != null">
                task_total_cash = #{taskTotalCash},
            </if>
            <if test="activeTotalCash != null">
                active_total_cash = #{activeTotalCash},
            </if>
            <if test="inviteTotalCash != null">
                invite_total_cash = #{inviteTotalCash},
            </if>
            <if test="itChargeTotalCash != null">
                it_charge_total_cash = #{itChargeTotalCash},
            </if>
            <if test="otherTotalCash != null">
                other_total_cash = #{otherTotalCash},
            </if>
        </set>
        where user_id = #{userId}
    </update>
    <update id="updateByPrimaryKey" parameterType="UCash">
        update u_cash
        set cash = #{cash},
        platform_cash = #{platformCash},
        total_cash = #{totalCash},
        total_platform_cash = #{totalPlatformCash},
        withdraw = #{withdraw},
        withdraw_total = #{withdrawTotal},
        last_withdraw_time = #{lastWithdrawTime},
        friend_total_cash = #{friendTotalCash},
        task_total_cash = #{taskTotalCash},
        active_total_cash = #{activeTotalCash},
        invite_total_cash = #{inviteTotalCash},
        it_charge_total_cash = #{itChargeTotalCash},
        other_total_cash = #{otherTotalCash}
        where user_id = #{userId}
    </update>

    <update id="updateCash" parameterType="UCash">
        update u_cash
        set cash = cash + #{cash}
        where user_id = #{userId}
    </update>
    <update id="updateTotalCash" parameterType="UCash">
        update u_cash
        set
        <if test="friendTotalCash != null">
            friend_total_cash = friend_total_cash + #{friendTotalCash},
        </if>
        <if test="taskTotalCash != null">
            task_total_cash = task_total_cash + #{taskTotalCash},
        </if>
        <if test="activeTotalCash != null">
            active_total_cash = active_total_cash + #{activeTotalCash},
        </if>
        <if test="inviteTotalCash != null">
            invite_total_cash = invite_total_cash + #{inviteTotalCash},
        </if>
        <if test="itChargeTotalCash != null">
            it_charge_total_cash = it_charge_total_cash + #{itChargeTotalCash},
        </if>
        <if test="otherTotalCash != null">
            other_total_cash = other_total_cash + #{otherTotalCash},
        </if>
        cash = cash + #{cash},
        total_cash = total_cash + #{cash}
        where user_id = #{userId}
    </update>
    <update id="updateWithdrawApply" parameterType="UCash">
        update u_cash
        set withdraw = withdraw + #{withdraw},
        last_withdraw_time = #{lastWithdrawTime}
        where user_id = #{userId}
    </update>
    <update id="updateWithdrawSuccess" parameterType="UCash">
        update u_cash
        set withdraw = withdraw - #{withdraw},
        withdraw_total = withdraw_total + #{withdraw}
        where user_id = #{userId}
    </update>
    <update id="updateWithdrawFailure" parameterType="UCash">
        update u_cash
        set cash = cash + #{cash},
        withdraw = withdraw - #{withdraw}
        where user_id = #{userId}
    </update>
</mapper>