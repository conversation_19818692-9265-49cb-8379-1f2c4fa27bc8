/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.acct.data.entity;

import com.easyooo.framework.cache.annotations.KeyProperty;
import java.util.Date;
import org.apache.ibatis.type.Alias;

@Alias("UCashIncomeInner")
public class UCashIncomeInner {
    /** 自增主键 */
    @KeyProperty
    private Long logId;

    /** 目标用户 */
    private Long targetUserId;

    /** 积分 */
    private Long cash;

    /** 创建时间 */
    private Date createTime;

    /** 操作人 */
    private Long operator;

    /** 操作备注 */
    private String operatorRemark;

    public UCashIncomeInner() {
        
    }

    /** 根据主键初始化实例 **/
    public UCashIncomeInner(Long logId) {
        this.logId = logId;
    }

    public Long getLogId() {
        return logId;
    }

    public void setLogId(Long logId) {
        this.logId = logId;
    }

    public Long getTargetUserId() {
        return targetUserId;
    }

    public void setTargetUserId(Long targetUserId) {
        this.targetUserId = targetUserId;
    }

    public Long getCash() {
        return cash;
    }

    public void setCash(Long cash) {
        this.cash = cash;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getOperator() {
        return operator;
    }

    public void setOperator(Long operator) {
        this.operator = operator;
    }

    public String getOperatorRemark() {
        return operatorRemark;
    }

    public void setOperatorRemark(String operatorRemark) {
        this.operatorRemark = operatorRemark == null ? null : operatorRemark.trim();
    }
}