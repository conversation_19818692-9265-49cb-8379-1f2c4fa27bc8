/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.acct.data.entity;

import com.easyooo.framework.cache.annotations.KeyProperty;
import org.apache.ibatis.type.Alias;

import java.util.Date;

@Alias("UTransferBind")
public class UTransferBind {
    /**
     * 被绑用户ID
     */
    @KeyProperty
    private Long userId;

    /**
     * 绑定的用户ID
     */
    private Long bindUserId;

    /**
     * 绑定时间
     */
    private Date createTime;

    public UTransferBind() {

    }

    /**
     * 根据主键初始化实例
     **/
    public UTransferBind(Long userId) {
        this.userId = userId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getBindUserId() {
        return bindUserId;
    }

    public void setBindUserId(Long bindUserId) {
        this.bindUserId = bindUserId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}