package com.tuowan.yeliao.acct.data.enums;

import com.easyooo.framework.common.util.EnumUtils;

/**
 * 提现类型枚举定义
 *
 * <AUTHOR>
 * @date 2020/7/16 15:56
 */
public enum WithdrawType implements EnumUtils.IDEnum {

    Wei<PERSON>("W", "微信", "WECHAT", "14775"),
    Alipay("A", "支付宝", "ALIPAY", "14775"),
    ;

    private String id;
    private String desc;
    private String salaryType;
    // 税源地服务商
    private String serviceId;

    WithdrawType(String id, String desc, String salaryType, String serviceId) {
        this.id = id;
        this.desc = desc;
        this.salaryType = salaryType;
        this.serviceId = serviceId;
    }

    @Override
    public String getId() {
        return id;
    }

    @Override
    public String getDesc() {
        return desc;
    }

    public String getSalaryType() {
        return salaryType;
    }

    public String getServiceId() {
        return serviceId;
    }
}
