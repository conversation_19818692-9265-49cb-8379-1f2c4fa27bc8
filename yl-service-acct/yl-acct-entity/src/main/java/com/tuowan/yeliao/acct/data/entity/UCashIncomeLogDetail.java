/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.acct.data.entity;

import com.easyooo.framework.cache.annotations.KeyProperty;
import org.apache.ibatis.type.Alias;

import java.util.Date;

@Alias("UCashIncomeLogDetail")
public class UCashIncomeLogDetail {
    /**
     * 创建时间
     */
    @KeyProperty
    private Date createTime;

    /**
     * 日志ID
     */
    @KeyProperty
    private Long logId;

    /**
     * 家族ID
     */
    private Integer familyId;

    public UCashIncomeLogDetail() {

    }

    /**
     * 根据主键初始化实例
     **/
    public UCashIncomeLogDetail(Date createTime, Long logId) {
        this.createTime = createTime;
        this.logId = logId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getLogId() {
        return logId;
    }

    public void setLogId(Long logId) {
        this.logId = logId;
    }

    public Integer getFamilyId() {
        return familyId;
    }

    public void setFamilyId(Integer familyId) {
        this.familyId = familyId;
    }
}