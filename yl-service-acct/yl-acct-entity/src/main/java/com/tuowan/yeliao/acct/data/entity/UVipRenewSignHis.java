/*
 * Copyright © 2018 Julun Corporation, All Rights Reserved.
 */
package com.tuowan.yeliao.acct.data.entity;

import com.easyooo.framework.cache.annotations.KeyProperty;
import com.tuowan.yeliao.acct.data.enums.VipRenewUnSignType;
import com.tuowan.yeliao.commons.core.enums.general.ClientType;
import com.tuowan.yeliao.commons.core.enums.general.PackageType;
import com.tuowan.yeliao.commons.core.enums.general.PayType;
import org.apache.ibatis.type.Alias;

import java.util.Date;

@Alias("UVipRenewSignHis")
public class UVipRenewSignHis {
    /**
     * 记录ID
     */
    @KeyProperty
    private Long logId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 签约号
     */
    private String signNo;

    /**
     * 签约方式：
     * 支付宝APP支付：ALA
     * 苹果支付：IAP
     */
    private PayType payType;

    /**
     * 应用ID
     */
    private String channelId;

    /**
     * 支持的客户端类型(ClientType)
     * 微信公众号：H
     * Android客户端：A
     * Ios客户端：I
     * 直播联盟：U
     * Cms：C
     * Site网站：S
     * Assistent助手：T
     * All: F
     */
    private ClientType clientType;

    /**
     * 客户端版本号
     */
    private String clientVersion;

    /**
     * 充值模板ID
     */
    private Integer tplId;

    /**
     * 周期类型period_type是周期扣款产品必填，枚举值为DAY和MONTH
     */
    private String periodType;

    /**
     * 周期数period是周期扣款产品必填
     */
    private Integer period;

    /**
     * 单次扣款最大金额
     */
    private Integer singleAmount;

    /**
     * 下次扣款日期
     */
    private Date executeTime;

    /**
     * 包体类型
     */
    private PackageType packageType;

    /**
     * 签约回调时间，通知后更新
     */
    private Date callbackTime;

    /**
     * 第三方签约号，通知后更新
     */
    private String externalSignNo;

    /**
     * 备注，通知后更新
     */
    private String remark;

    /**
     * 商户号
     */
    private String mchId;

    /**
     * 签约方用户号（如支付宝唯一用户号，appleID），通知后更新
     */
    private String externalUserId;

    /**
     * 签约时间，通知后更新
     */
    private Date signTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 解约类型
     */
    private VipRenewUnSignType unSignType;

    /**
     * 解约时间
     */
    private Date unSignTime;

    /**
     * 总金币
     */
    private Integer beans;

    /**
     * 平台赠送金币
     */
    private Integer giveBeans;

    /**
     * tpl扩展字段
     */
    private String extJsonCfg;

    /**
     * 协议生效时间，null：立即生效 (苹果)
     */
    private Date validTime;

    /**
     * 协议失效时间，null：永不失效 (苹果)
     */
    private Date invalidTime;

    public UVipRenewSignHis() {

    }

    /**
     * 根据主键初始化实例
     **/
    public UVipRenewSignHis(Long logId) {
        this.logId = logId;
    }

    public Long getLogId() {
        return logId;
    }

    public void setLogId(Long logId) {
        this.logId = logId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getSignNo() {
        return signNo;
    }

    public void setSignNo(String signNo) {
        this.signNo = signNo == null ? null : signNo.trim();
    }

    public PayType getPayType() {
        return payType;
    }

    public void setPayType(PayType payType) {
        this.payType = payType;
    }

    public String getChannelId() {
        return channelId;
    }

    public void setChannelId(String channelId) {
        this.channelId = channelId;
    }

    public ClientType getClientType() {
        return clientType;
    }

    public void setClientType(ClientType clientType) {
        this.clientType = clientType;
    }

    public String getClientVersion() {
        return clientVersion;
    }

    public void setClientVersion(String clientVersion) {
        this.clientVersion = clientVersion == null ? null : clientVersion.trim();
    }

    public Integer getTplId() {
        return tplId;
    }

    public void setTplId(Integer tplId) {
        this.tplId = tplId;
    }

    public String getPeriodType() {
        return periodType;
    }

    public void setPeriodType(String periodType) {
        this.periodType = periodType == null ? null : periodType.trim();
    }

    public Integer getPeriod() {
        return period;
    }

    public void setPeriod(Integer period) {
        this.period = period;
    }

    public Integer getSingleAmount() {
        return singleAmount;
    }

    public void setSingleAmount(Integer singleAmount) {
        this.singleAmount = singleAmount;
    }

    public Date getExecuteTime() {
        return executeTime;
    }

    public void setExecuteTime(Date executeTime) {
        this.executeTime = executeTime;
    }

    public PackageType getPackageType() {
        return packageType;
    }

    public void setPackageType(PackageType packageType) {
        this.packageType = packageType;
    }

    public Date getCallbackTime() {
        return callbackTime;
    }

    public void setCallbackTime(Date callbackTime) {
        this.callbackTime = callbackTime;
    }

    public String getExternalSignNo() {
        return externalSignNo;
    }

    public void setExternalSignNo(String externalSignNo) {
        this.externalSignNo = externalSignNo == null ? null : externalSignNo.trim();
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    public String getMchId() {
        return mchId;
    }

    public void setMchId(String mchId) {
        this.mchId = mchId;
    }

    public String getExternalUserId() {
        return externalUserId;
    }

    public void setExternalUserId(String externalUserId) {
        this.externalUserId = externalUserId == null ? null : externalUserId.trim();
    }

    public Date getSignTime() {
        return signTime;
    }

    public void setSignTime(Date signTime) {
        this.signTime = signTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public VipRenewUnSignType getUnSignType() {
        return unSignType;
    }

    public void setUnSignType(VipRenewUnSignType unSignType) {
        this.unSignType = unSignType;
    }

    public Date getUnSignTime() {
        return unSignTime;
    }

    public void setUnSignTime(Date unSignTime) {
        this.unSignTime = unSignTime;
    }

    public Integer getBeans() {
        return beans;
    }

    public void setBeans(Integer beans) {
        this.beans = beans;
    }

    public Integer getGiveBeans() {
        return giveBeans;
    }

    public void setGiveBeans(Integer giveBeans) {
        this.giveBeans = giveBeans;
    }

    public String getExtJsonCfg() {
        return extJsonCfg;
    }

    public void setExtJsonCfg(String extJsonCfg) {
        this.extJsonCfg = extJsonCfg == null ? null : extJsonCfg.trim();
    }

    public Date getValidTime() {
        return validTime;
    }

    public void setValidTime(Date validTime) {
        this.validTime = validTime;
    }

    public Date getInvalidTime() {
        return invalidTime;
    }

    public void setInvalidTime(Date invalidTime) {
        this.invalidTime = invalidTime;
    }
}