# P6Spy SQL监控集成说明

## 概述

本项目已集成P6Spy 3.9.1用于SQL语句监控和执行时间统计。P6Spy可以拦截并记录应用程序执行的所有SQL语句，帮助开发人员进行性能调优和问题排查。

## 特性

- ✅ **简单配置**: 只需在application.yml中配置`p6spy.enabled=true/false`即可开启/关闭SQL打印
- ✅ **环境隔离**: 仅在开发(dev)和测试(test)环境包含p6spy依赖，生产环境自动排除
- ✅ **性能友好**: 生产环境完全不包含p6spy代码，零性能影响
- ✅ **详细信息**: 显示完整SQL语句、执行时间、连接信息等
- ✅ **兼容现有架构**: 与项目现有的多数据源配置完全兼容

## 配置方式

### 1. 启用/禁用P6Spy

在Nacos配置中心或application.yml中添加以下配置：

```yaml
# 启用P6Spy SQL监控
p6spy:
  enabled: true  # true=启用, false=禁用
```

**注意**: 
- 如果不配置此项，默认在开发和测试环境启用，生产环境禁用
- 生产环境即使配置为true也不会生效，因为生产环境不包含p6spy依赖

### 2. 无需其他配置

- ✅ 无需修改数据库连接字符串
- ✅ 无需修改驱动类名
- ✅ 无需添加额外的配置文件
- ✅ 系统会自动处理所有配置

## 使用效果

启用P6Spy后，控制台将输出如下格式的SQL日志：

```
2025-07-04 10:30:15.123 | 25ms | statement | connection1 | SELECT * FROM user WHERE id = 1
2025-07-04 10:30:15.150 | 12ms | statement | connection1 | UPDATE user SET name = 'test' WHERE id = 1
2025-07-04 10:30:15.165 | 8ms | statement | connection2 | INSERT INTO log (message) VALUES ('test log')
```

日志格式说明：
- **时间戳**: SQL执行的具体时间
- **执行时间**: SQL语句执行耗时（毫秒）
- **类型**: 操作类型（statement/commit/rollback等）
- **连接**: 数据库连接标识
- **SQL**: 完整的SQL语句（已替换参数）

## 环境支持

| 环境 | P6Spy依赖 | 默认启用 | 说明 |
|------|-----------|----------|------|
| 开发环境(dev) | ✅ 包含 | ✅ 启用 | 便于开发调试 |
| 测试环境(test) | ✅ 包含 | ✅ 启用 | 便于测试验证 |
| 生产环境(prod) | ❌ 不包含 | ❌ 禁用 | 确保生产性能 |

## 验证配置

运行验证脚本检查配置是否正确：

### Windows
```bash
verify-p6spy.bat
```

### Linux/Mac
```bash
chmod +x verify-p6spy.sh
./verify-p6spy.sh
```

## 编译和部署

### 开发环境
```bash
mvn clean compile -Pdev
# 或
mvn clean package -Pdev
```

### 测试环境
```bash
mvn clean compile -Ptest
# 或
mvn clean package -Ptest
```

### 生产环境
```bash
mvn clean compile -Pprod
# 或
mvn clean package -Pprod
```

## 故障排除

### 1. SQL没有打印出来

**检查项**:
- 确认配置了`p6spy.enabled=true`
- 确认当前环境是dev或test（生产环境不支持）
- 检查日志级别，确保能看到INFO级别日志
- 运行验证脚本检查配置

### 2. 启动时出现ClassNotFoundException

**原因**: 通常是在生产环境配置了p6spy.enabled=true
**解决**: 生产环境不包含p6spy依赖，请设置为false或不配置

### 3. 性能问题

**说明**: P6Spy会对性能有一定影响，建议：
- 仅在开发和测试环境使用
- 生产环境务必禁用
- 如需生产环境监控，建议使用专业的APM工具

## 技术实现

### 核心组件

1. **P6spyConfig**: 配置管理类，读取p6spy.enabled配置
2. **JdbcDataSource**: 数据源类，根据配置动态选择驱动
3. **spy.properties**: P6Spy配置文件，定义输出格式等

### 工作原理

1. 系统启动时读取`p6spy.enabled`配置
2. 如果启用且环境支持，自动将JDBC驱动替换为P6Spy代理驱动
3. P6Spy拦截所有SQL操作并记录到日志
4. 生产环境完全不包含P6Spy代码，确保性能

## 注意事项

1. **仅开发测试使用**: 生产环境不要启用，避免性能影响
2. **日志量控制**: SQL较多时注意日志文件大小
3. **敏感信息**: P6Spy会记录完整SQL，注意敏感数据保护
4. **兼容性**: 与现有数据源配置完全兼容，无需修改业务代码

## 更多配置

如需自定义P6Spy行为，可以修改`yl-commons/yl-commons-config/src/main/resources/spy.properties`文件。

常用配置项：
- `executionThreshold`: 只记录超过指定时间的SQL（毫秒）
- `excludecategories`: 排除特定类型的操作
- `customLogMessageFormat`: 自定义日志格式

详细配置说明请参考：http://p6spy.readthedocs.io/en/latest/configandusage.html
