# P6Spy配置示例
# 将此配置添加到Nacos配置中心或application.yml文件中

# P6Spy SQL监控配置
p6spy:
  enabled: true  # true=启用SQL打印, false=禁用SQL打印

# 说明：
# 1. 如果不配置此项，默认在开发和测试环境启用，生产环境禁用
# 2. 生产环境即使配置为true也不会生效，因为生产环境不包含p6spy依赖
# 3. 启用后会在日志中打印所有SQL语句和执行时间
# 4. 日志格式：时间戳 | 执行时间ms | 类型 | 连接ID | SQL语句

# 示例日志输出：
# 2025-07-04 10:30:15.123 | 25ms | statement | connection1 | SELECT * FROM user WHERE id = 1
# 2025-07-04 10:30:15.150 | 12ms | statement | connection1 | UPDATE user SET name = 'test' WHERE id = 1
